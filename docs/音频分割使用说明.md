# 音频分割工具使用说明

## 功能介绍

这个工具可以根据说话人识别结果，为每个不同的说话人分别拼接音频片段，生成指定时长（默认1分钟）的音频文件。

## 文件说明

- `deer-flow/audio_splitter.py` - 主要的音频分割脚本
- `deer-flow/real_tool_call_result.json` - 包含说话人识别和音频分段信息的JSON文件

## 依赖安装

在运行脚本之前，需要安装必要的Python包：

```bash
pip install pydub
```

如果需要处理MP3等格式，还需要安装ffmpeg：

```bash
# macOS
brew install ffmpeg

# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg

# Windows
# 下载ffmpeg并添加到PATH环境变量
```

## 使用方法

### 基本用法

```bash
cd deer-flow
python audio_splitter.py real_tool_call_result.json
```

### 高级用法

```bash
# 指定输出目录
python audio_splitter.py real_tool_call_result.json -o my_output

# 指定每个说话人的目标时长（秒）
python audio_splitter.py real_tool_call_result.json -d 90

# 组合使用
python audio_splitter.py real_tool_call_result.json -o speaker_audio -d 120
```

### 参数说明

- `json_file`: 必需参数，音频分析结果JSON文件路径
- `-o, --output`: 可选，输出目录（默认: output_audio）
- `-d, --duration`: 可选，每个说话人的目标时长，单位秒（默认: 60）

## 输出结果

脚本会在指定的输出目录中生成以下文件：

```
output_audio/
├── 说话人1_主持人_60秒.wav
├── 说话人2_主持人_60秒.wav
├── 说话人3_主持人_60秒.wav
└── 说话人4_主持人_60秒.wav
```

文件命名格式：`说话人{ID}_{说话人名称}_{实际时长}秒.wav`

## 工作原理

1. **读取JSON文件**: 解析包含说话人识别结果的JSON文件
2. **分组音频片段**: 按说话人ID将所有音频片段分组
3. **按时间排序**: 对每个说话人的片段按开始时间排序
4. **拼接音频**: 依次加载并拼接音频片段，直到达到目标时长
5. **保存文件**: 将拼接后的音频保存为WAV格式

## 注意事项

1. **音频文件路径**: 确保JSON文件中指定的音频文件路径存在且可访问
2. **音频格式**: 脚本主要支持WAV格式，其他格式需要安装相应的编解码器
3. **时长控制**: 如果某个说话人的总音频时长不足目标时长，会使用所有可用的音频
4. **内存使用**: 处理大型音频文件时注意内存使用情况

## 故障排除

### 常见错误

1. **"音频文件不存在"**
   - 检查JSON文件中的音频文件路径是否正确
   - 确保音频文件确实存在于指定位置

2. **"无法加载音频文件"**
   - 检查音频文件是否损坏
   - 确保安装了必要的音频编解码器

3. **"ModuleNotFoundError: No module named 'pydub'"**
   - 运行 `pip install pydub` 安装依赖

### 调试模式

脚本会输出详细的处理信息，包括：
- 每个说话人的片段数量
- 正在处理的音频片段信息
- 最终生成的音频时长

## 扩展功能

如果需要其他功能，可以修改脚本：

1. **支持其他音频格式**: 修改 `load_audio_segment` 方法
2. **添加音频效果**: 在拼接过程中添加淡入淡出等效果
3. **自定义输出格式**: 修改 `export` 方法的参数
4. **批量处理**: 扩展脚本以处理多个JSON文件

## 示例输出

```
发现 4 个说话人
目标时长: 60 秒
--------------------------------------------------

处理说话人 1 (主持人)
总片段数: 25
正在处理说话人 1...
  添加片段 1: 各位乡亲，各位父老，下面播声个广告。本人虽说村长落选... (32.5秒)
  添加片段 2: 这大夫不用说，你一定患有更年期综合紊乱症... (9.4秒)
  添加片段 3: 这我听明白了，你重新中大奖不敢告诉他... (17.1秒)
说话人 1 总时长: 60.0秒
已保存: output_audio/说话人1_主持人_60秒.wav
```

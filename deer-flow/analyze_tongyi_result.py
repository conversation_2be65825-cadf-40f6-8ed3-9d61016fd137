#!/usr/bin/env python3
"""
分析通义听悟的说话人分离结果
"""

import logging
import requests
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_and_analyze_result():
    """下载并分析通义听悟的结果"""
    logger.info("🎯 分析通义听悟说话人分离结果...")
    
    # 转写结果URL
    transcription_url = "https://prod-tingwu-paas-common-beijing.oss-cn-beijing.aliyuncs.com/tingwu/output/1298479927372858/167effeeb0aa460b949382f8b2ae199a/167effeeb0aa460b949382f8b2ae199a_Transcription_20250807110635.json?Expires=1757128001&OSSAccessKeyId=LTAI5tMzZ1D4o1drkJN1TfCr&Signature=ujEldpqwJDpq50%2Fi%2F90lCN4Tn%2Fs%3D"
    
    try:
        logger.info("📥 下载转写结果...")
        response = requests.get(transcription_url)
        
        if response.status_code == 200:
            transcription_data = response.json()
            
            # 保存原始结果
            with open('tongyi_transcription_result.json', 'w', encoding='utf-8') as f:
                json.dump(transcription_data, f, ensure_ascii=False, indent=2)
            logger.info("💾 转写结果已保存到: tongyi_transcription_result.json")
            
            # 分析说话人信息
            analyze_speaker_diarization(transcription_data)
            
            return True
            
        else:
            logger.error(f"❌ 下载失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 下载或分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_speaker_diarization(data):
    """分析说话人分离结果"""
    logger.info("\n" + "="*80)
    logger.info("🎤 通义听悟说话人分离详细分析")
    logger.info("="*80)
    
    try:
        # 检查数据结构
        logger.info("📊 数据结构分析:")
        logger.info(f"   顶层键: {list(data.keys())}")
        
        # 查找转写结果 - 通义听悟使用Paragraphs结构
        paragraphs = []
        if 'Transcription' in data and 'Paragraphs' in data['Transcription']:
            paragraphs = data['Transcription']['Paragraphs']
            logger.info(f"   段落数量: {len(paragraphs)}")

        if not paragraphs:
            logger.warning("⚠️ 没有找到段落数据")
            return
        
        # 分析说话人信息
        speaker_stats = {}
        total_paragraphs = len(paragraphs)

        logger.info(f"\n📝 转写结果分析:")
        logger.info(f"   总段落数: {total_paragraphs}")

        # 检查前几个段落的结构
        logger.info(f"\n🔍 段落结构分析 (前3个段落):")
        for i, paragraph in enumerate(paragraphs[:3]):
            logger.info(f"   段落 {i+1} 键: {list(paragraph.keys())}")
            if 'SpeakerId' in paragraph:
                logger.info(f"   段落 {i+1} 说话人: {paragraph['SpeakerId']}")
            if 'Words' in paragraph:
                words = paragraph['Words']
                text = ''.join([word.get('Text', '') for word in words])
                logger.info(f"   段落 {i+1} 内容: {text[:50]}...")

        # 统计说话人
        for i, paragraph in enumerate(paragraphs):
            speaker_id = paragraph.get('SpeakerId', 'unknown')

            # 提取段落文本
            words = paragraph.get('Words', [])
            text = ''.join([word.get('Text', '') for word in words])

            if speaker_id not in speaker_stats:
                speaker_stats[speaker_id] = {
                    'count': 0,
                    'examples': [],
                    'total_chars': 0
                }

            speaker_stats[speaker_id]['count'] += 1
            speaker_stats[speaker_id]['total_chars'] += len(text)

            # 收集前3个例子
            if len(speaker_stats[speaker_id]['examples']) < 3:
                speaker_stats[speaker_id]['examples'].append(text)
        
        # 输出说话人统计
        logger.info(f"\n👥 说话人分离结果:")
        logger.info(f"   检测到的说话人数量: {len(speaker_stats)}")
        
        if len(speaker_stats) > 1:
            logger.info("🎉 成功检测到多个说话人！")
        else:
            logger.warning("⚠️ 只检测到一个说话人")
        
        # 详细统计
        for speaker_id, stats in speaker_stats.items():
            percentage = (stats['count'] / total_paragraphs) * 100
            avg_chars = stats['total_chars'] / stats['count'] if stats['count'] > 0 else 0
            
            logger.info(f"\n🎤 说话人 {speaker_id}:")
            logger.info(f"   句子数量: {stats['count']}")
            logger.info(f"   占比: {percentage:.1f}%")
            logger.info(f"   平均字数: {avg_chars:.1f}")
            logger.info(f"   示例:")
            for j, example in enumerate(stats['examples']):
                logger.info(f"      {j+1}. {example[:60]}...")
        
        # 对比分析
        logger.info(f"\n📊 与火山引擎对比:")
        logger.info(f"   火山引擎: 0个说话人 (API问题)")
        logger.info(f"   通义听悟: {len(speaker_stats)}个说话人")
        
        if len(speaker_stats) > 1:
            logger.info("✅ 通义听悟成功解决了说话人识别问题！")
        else:
            logger.warning("⚠️ 通义听悟也只识别到一个说话人")
        
        # 生成对比报告
        generate_comparison_report(speaker_stats, total_sentences)
        
    except Exception as e:
        logger.error(f"❌ 分析说话人信息失败: {e}")
        import traceback
        traceback.print_exc()

def generate_comparison_report(speaker_stats, total_sentences):
    """生成对比报告"""
    logger.info("\n" + "="*80)
    logger.info("📋 说话人识别对比报告")
    logger.info("="*80)
    
    report = {
        "测试视频": "小品视频 (应该有多个说话人)",
        "火山引擎结果": {
            "说话人数量": 0,
            "问题": "API没有返回说话人信息",
            "状态": "失败"
        },
        "通义听悟结果": {
            "说话人数量": len(speaker_stats),
            "总段落数": total_paragraphs,
            "说话人分布": {},
            "状态": "成功" if len(speaker_stats) > 1 else "部分成功"
        }
    }
    
    # 添加说话人分布
    for speaker_id, stats in speaker_stats.items():
        percentage = (stats['count'] / total_paragraphs) * 100
        report["通义听悟结果"]["说话人分布"][f"说话人{speaker_id}"] = f"{stats['count']}段 ({percentage:.1f}%)"
    
    # 保存报告
    with open('speaker_diarization_comparison_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    logger.info("💾 对比报告已保存到: speaker_diarization_comparison_report.json")
    
    # 结论
    logger.info(f"\n🎯 结论:")
    if len(speaker_stats) > 1:
        logger.info("✅ 通义听悟成功识别出多个说话人")
        logger.info("✅ 可以替代火山引擎解决说话人识别问题")
        logger.info("✅ 建议集成通义听悟到现有工具中")
    elif len(speaker_stats) == 1:
        logger.info("⚠️ 通义听悟也只识别到一个说话人")
        logger.info("🤔 可能的原因:")
        logger.info("   1. 视频确实只有一个主要说话人")
        logger.info("   2. 说话人声音特征相似")
        logger.info("   3. 需要调整说话人分离参数")
    else:
        logger.info("❌ 通义听悟没有返回说话人信息")

def main():
    """主函数"""
    logger.info("🎯 开始分析通义听悟说话人分离结果...")
    
    success = download_and_analyze_result()
    
    logger.info(f"\n{'='*80}")
    logger.info("🎯 通义听悟测试总结")
    logger.info(f"{'='*80}")
    
    if success:
        logger.info("✅ 通义听悟API调用成功")
        logger.info("✅ 转写结果下载成功")
        logger.info("✅ 说话人分离分析完成")
        logger.info("")
        logger.info("📁 生成的文件:")
        logger.info("   • tongyi_transcription_result.json - 完整转写结果")
        logger.info("   • speaker_diarization_comparison_report.json - 对比报告")
        logger.info("")
        logger.info("🔍 下一步:")
        logger.info("   1. 查看分析结果")
        logger.info("   2. 决定是否集成通义听悟")
        logger.info("   3. 如果效果好，替换火山引擎API")
    else:
        logger.error("❌ 分析失败")

if __name__ == "__main__":
    main()

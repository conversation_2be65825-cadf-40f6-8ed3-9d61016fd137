{"project": {"title": "心理诊所角色特写片段", "total_clips": 15, "upload_date": "2025-08-08", "source": "952473473_da2-1-192.mp4"}, "characters": {"doctor": {"name": "心理医生 (说话人1)", "description": "村里的心理医生，曾经是村长候选人", "clips_count": 5, "total_duration": "70.1秒"}, "wife": {"name": "患者家属 (说话人3)", "description": "患者的妻子，因丈夫中彩票后心理问题求医", "clips_count": 5, "total_duration": "51.1秒"}, "patient": {"name": "患者 (说话人4)", "description": "中彩票后出现心理问题的男性患者", "clips_count": 5, "total_duration": "37.3秒"}}, "quick_access": {"best_clips": [{"title": "医生自我介绍", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/6dccee7f-d492-4593-8456-25d45652ed19.mp4", "duration": "17.2秒", "description": "心理医生的经典自我介绍广告"}, {"title": "揭示彩票真相", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/80383245-cd84-4f4a-a9b0-ad328ec6aaf3.mp4", "duration": "5.6秒", "description": "妻子向丈夫揭示彩票中奖的真相"}, {"title": "患者内心困惑", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/2c03e13f-62d7-4943-93d4-960769128fda.mp4", "duration": "16.5秒", "description": "患者表达内心困惑和对妻子的依赖"}], "all_clips": [{"id": "doctor_01", "character": "心理医生", "title": "自我介绍广告", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/6dccee7f-d492-4593-8456-25d45652ed19.mp4", "duration": "17.2秒"}, {"id": "doctor_02", "character": "心理医生", "title": "诊断更年期症状", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/ae9ee98c-9f9a-43b7-9df5-d9c6746166f0.mp4", "duration": "13.8秒"}, {"id": "doctor_03", "character": "心理医生", "title": "安慰患者无病", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/e0696fd5-fde6-4bf7-8cd4-11556d9c262f.mp4", "duration": "18.4秒"}, {"id": "doctor_04", "character": "心理医生", "title": "调解家庭矛盾", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/8200078b-5d87-4490-9658-2115fda0031b.mp4", "duration": "10.2秒"}, {"id": "doctor_05", "character": "心理医生", "title": "询问详细情况", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/734fb258-0c8c-44b9-9e7c-635f58da9258.mp4", "duration": "10.7秒"}, {"id": "wife_01", "character": "患者家属", "title": "挂急诊求医", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/e27f5201-59ee-4996-88c2-8033931ffea3.mp4", "duration": "11.3秒"}, {"id": "wife_02", "character": "患者家属", "title": "不想告诉真相", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/1119192d-a067-47c1-9174-bcb0f7def27c.mp4", "duration": "10.8秒"}, {"id": "wife_03", "character": "患者家属", "title": "感情上的愧疚", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/7ccaae10-089f-44f2-92ef-541048837cac.mp4", "duration": "17.4秒"}, {"id": "wife_04", "character": "患者家属", "title": "揭示彩票真相", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/80383245-cd84-4f4a-a9b0-ad328ec6aaf3.mp4", "duration": "5.6秒"}, {"id": "wife_05", "character": "患者家属", "title": "紧急求助医生", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/c6e56fd9-c1f1-4190-a053-5b16604aecce.mp4", "duration": "6.0秒"}, {"id": "patient_01", "character": "患者", "title": "内心困惑表达", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/2c03e13f-62d7-4943-93d4-960769128fda.mp4", "duration": "16.5秒"}, {"id": "patient_02", "character": "患者", "title": "哲学性思考", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/103e9db4-d44e-4f77-96bc-9d931b608d3c.mp4", "duration": "5.8秒"}, {"id": "patient_03", "character": "患者", "title": "症状表现描述", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/d286027e-8871-47d7-8ee9-894c17d47146.mp4", "duration": "4.0秒"}, {"id": "patient_04", "character": "患者", "title": "坚持自己观点", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/fb2bfaa0-e8c1-4bc7-803d-5859ecd96cfb.mp4", "duration": "5.4秒"}, {"id": "patient_05", "character": "患者", "title": "康复后的感激", "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/4cf69452-64dc-425c-b345-6214fb875919.mp4", "duration": "5.6秒"}]}, "usage_notes": {"access": "所有链接支持直接浏览器播放", "format": "MP4格式，H.264编码", "resolution": "956x720高清", "storage": "腾讯云COS，稳定可靠", "api_ready": "JSON格式便于API集成"}}
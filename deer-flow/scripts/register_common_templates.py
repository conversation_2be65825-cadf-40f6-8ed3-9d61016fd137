#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
register_common_templates.py

注册常用模板，为use_template工具提供可用的模板。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.graph_v2.template_models import PlanTemplate, StepTemplate, ParameterSchema, ParameterType
from src.tools.template_tools import register_template


def create_city_poster_template():
    """创建城市海报系列模板"""
    print("📝 创建城市海报系列模板...")
    
    # 参数定义
    params = {
        "cities": ParameterSchema(
            type=ParameterType.LIST,
            required=True,
            description="城市列表，如 ['巴黎', '纽约', '东京']"
        ),
        "style": ParameterSchema(
            type=ParameterType.STRING,
            required=False,
            default="minimalist",
            options=["minimalist", "vintage", "modern", "artistic"],
            description="海报风格"
        ),
        "color_scheme": ParameterSchema(
            type=ParameterType.STRING,
            required=False,
            default="blue_cream_coral",
            description="配色方案"
        )
    }
    
    # 步骤模板
    step_templates = [
        StepTemplate(
            template_step_id="create_style_guide",
            name="创建风格指南",
            description_template="为{{ style }}风格的城市海报系列创建统一的设计风格指南",
            tool_to_use="visual_expert",
            input_template={
                "task_description": "创建{{ style }}风格的海报设计风格指南，使用{{ color_scheme }}配色方案",
                "style": "{{ style }}",
                "color_scheme": "{{ color_scheme }}",
                "output_type": "style_guide"
            }
        ),
        StepTemplate(
            template_step_id="create_city_posters",
            name="批量创建城市海报",
            description_template="为{{ cities|length }}个城市创建{{ style }}风格海报",
            tool_to_use="visual_expert",
            input_template={
                "task_description": "基于风格指南，为以下城市创建海报：{{ cities|join(', ') }}",
                "cities": "{{ cities }}",
                "style": "{{ style }}",
                "color_scheme": "{{ color_scheme }}",
                "reference_style": "使用第一步的风格指南",
                "batch_mode": True
            },
            dependencies=["create_style_guide"]
        )
    ]
    
    template = PlanTemplate(
        template_id="city_poster_series",
        name="城市海报系列",
        description="为多个城市创建统一风格的海报系列",
        category="visual_design",
        tags=["poster", "city", "series", "design"],
        step_templates=step_templates,
        parameters=params,
        estimated_duration=1800  # 30分钟
    )
    
    register_template(template)
    print("   ✅ 城市海报系列模板已注册")


def create_ai_parody_video_template():
    """创建AI鬼畜视频模板"""
    print("📝 创建AI鬼畜视频模板...")
    
    # 参数定义
    params = {
        "character": ParameterSchema(
            type=ParameterType.STRING,
            required=True,
            description="主角角色名称，如 '哪吒'、'孙悟空'"
        ),
        "theme": ParameterSchema(
            type=ParameterType.STRING,
            required=False,
            default="funny",
            options=["funny", "epic", "cute", "dramatic"],
            description="视频主题风格"
        ),
        "duration": ParameterSchema(
            type=ParameterType.INTEGER,
            required=False,
            default=30,
            min_value=15,
            max_value=120,
            description="视频时长（秒）"
        )
    }
    
    # 步骤模板
    step_templates = [
        StepTemplate(
            template_step_id="collect_character_materials",
            name="收集角色素材",
            description_template="收集{{ character }}的{{ theme }}风格素材",
            tool_to_use="visual_expert",
            input_template={
                "task_description": "收集{{ character }}的图片素材，风格偏向{{ theme }}",
                "character": "{{ character }}",
                "theme": "{{ theme }}",
                "material_type": "character_images"
            }
        ),
        StepTemplate(
            template_step_id="generate_background_music",
            name="生成背景音乐",
            description_template="为{{ character }}鬼畜视频生成{{ theme }}风格背景音乐",
            tool_to_use="audio_expert",
            input_template={
                "task_description": "生成适合{{ character }}鬼畜视频的{{ theme }}风格背景音乐",
                "character": "{{ character }}",
                "theme": "{{ theme }}",
                "duration": "{{ duration }}",
                "music_style": "electronic_remix"
            }
        ),
        StepTemplate(
            template_step_id="create_parody_video",
            name="制作鬼畜视频",
            description_template="制作{{ character }}的{{ duration }}秒{{ theme }}风格鬼畜视频",
            tool_to_use="video_expert",
            input_template={
                "task_description": "制作{{ character }}的鬼畜视频，时长{{ duration }}秒",
                "character": "{{ character }}",
                "theme": "{{ theme }}",
                "duration": "{{ duration }}",
                "materials": "使用前面步骤的素材",
                "video_style": "parody_remix"
            },
            dependencies=["collect_character_materials", "generate_background_music"]
        )
    ]
    
    template = PlanTemplate(
        template_id="ai_parody_video",
        name="AI鬼畜视频制作",
        description="制作{{ character }}的{{ theme }}风格鬼畜视频",
        category="video_creation",
        tags=["video", "parody", "ai", "character"],
        step_templates=step_templates,
        parameters=params,
        estimated_duration=2400  # 40分钟
    )
    
    register_template(template)
    print("   ✅ AI鬼畜视频模板已注册")


def create_brand_design_template():
    """创建品牌设计套件模板"""
    print("📝 创建品牌设计套件模板...")
    
    # 参数定义
    params = {
        "brand_name": ParameterSchema(
            type=ParameterType.STRING,
            required=True,
            description="品牌名称"
        ),
        "industry": ParameterSchema(
            type=ParameterType.STRING,
            required=True,
            options=["tech", "food", "fashion", "education", "healthcare", "finance"],
            description="行业类型"
        ),
        "style": ParameterSchema(
            type=ParameterType.STRING,
            required=False,
            default="modern",
            options=["modern", "classic", "playful", "elegant", "bold"],
            description="设计风格"
        )
    }
    
    # 步骤模板
    step_templates = [
        StepTemplate(
            template_step_id="design_logo",
            name="设计Logo",
            description_template="为{{ brand_name }}设计{{ style }}风格的Logo",
            tool_to_use="visual_expert",
            input_template={
                "task_description": "为{{ industry }}行业的{{ brand_name }}品牌设计{{ style }}风格Logo",
                "brand_name": "{{ brand_name }}",
                "industry": "{{ industry }}",
                "style": "{{ style }}",
                "output_type": "logo"
            }
        ),
        StepTemplate(
            template_step_id="create_brand_poster",
            name="创建品牌海报",
            description_template="创建{{ brand_name }}的品牌宣传海报",
            tool_to_use="visual_expert",
            input_template={
                "task_description": "基于Logo设计，创建{{ brand_name }}的品牌宣传海报",
                "brand_name": "{{ brand_name }}",
                "industry": "{{ industry }}",
                "style": "{{ style }}",
                "reference_logo": "使用第一步的Logo设计",
                "output_type": "poster"
            },
            dependencies=["design_logo"]
        ),
        StepTemplate(
            template_step_id="create_brand_video",
            name="制作品牌视频",
            description_template="制作{{ brand_name }}的品牌宣传视频",
            tool_to_use="video_expert",
            input_template={
                "task_description": "制作{{ brand_name }}的品牌宣传视频，展示Logo和海报设计",
                "brand_name": "{{ brand_name }}",
                "industry": "{{ industry }}",
                "style": "{{ style }}",
                "materials": "使用前面步骤的Logo和海报",
                "video_type": "brand_showcase"
            },
            dependencies=["design_logo", "create_brand_poster"]
        )
    ]
    
    template = PlanTemplate(
        template_id="brand_design_suite",
        name="品牌设计套件",
        description="为{{ brand_name }}创建完整的品牌设计套件",
        category="brand_design",
        tags=["brand", "logo", "poster", "video", "design"],
        step_templates=step_templates,
        parameters=params,
        estimated_duration=3600  # 60分钟
    )
    
    register_template(template)
    print("   ✅ 品牌设计套件模板已注册")


def create_zhao_benshan_xiangsheng_template():
    """创建赵本山小品生成模板"""
    print("📝 创建赵本山小品生成模板...")

    # 参数定义
    params = {
        "topic": ParameterSchema(
            type=ParameterType.STRING,
            required=True,
            description="小品主题，如 '看病'、'买彩票'、'相亲'"
        ),
        "style": ParameterSchema(
            type=ParameterType.STRING,
            required=False,
            default="classic",
            options=["classic", "modern", "absurd"],
            description="小品风格"
        ),
        "duration_preference": ParameterSchema(
            type=ParameterType.STRING,
            required=False,
            default="medium",
            options=["short", "medium", "long"],
            description="视频时长偏好"
        ),
        "background_music": ParameterSchema(
            type=ParameterType.BOOLEAN,
            required=False,
            default=True,
            description="是否添加背景音乐"
        )
    }

    # 步骤模板
    step_templates = [
        StepTemplate(
            template_step_id="create_script",
            name="创作小品剧本",
            description_template="创作关于{{ topic }}的赵本山风格小品剧本",
            tool_to_use="visual_expert",
            input_template={
                "prompt": """创作一个赵本山风格的东北小品剧本，主题是：{{ topic }}

要求：
1. 角色设定：医生、患者（赵本山）、患者家属（宋丹丹）
2. 语言风格：东北方言特色，幽默对话
3. 剧本结构：开场-发展-高潮-结尾
4. 输出格式：医生：[台词] / 患者：[台词] / 患者家属：[台词]

请确保剧本有趣、贴近生活，体现东北人的幽默智慧。"""
            }
        ),
        StepTemplate(
            template_step_id="assign_roles",
            name="分配角色台词",
            description_template="将剧本按角色分配台词，准备TTS合成",
            tool_to_use="visual_expert",
            input_template={
                "prompt": """基于创作的小品剧本，按角色分配台词并格式化。

输出JSON格式：
{
  "script_segments": [
    {
      "order": 1,
      "character": "角色名",
      "voice_id": "声音ID",
      "text": "台词内容",
      "emotion": "情感色彩",
      "estimated_duration": "时长"
    }
  ]
}

声音资产：
- 患者：zhaobenshan_voice_clone_test_001
- 患者家属：cloned_voice_from_host_audio_20240730
- 医生：default_doctor_voice"""
            },
            dependencies=["create_script"]
        ),
        StepTemplate(
            template_step_id="generate_tts",
            name="生成多人TTS语音",
            description_template="使用克隆声音生成角色对话音频",
            tool_to_use="audio_expert",
            input_template={
                "task_description": """基于角色台词分配，生成多人TTS语音文件。

声音资产：
- 赵本山（患者）：voice_id = zhaobenshan_voice_clone_test_001
- 宋丹丹（患者家属）：voice_id = cloned_voice_from_host_audio_20240730
- 医生：使用默认专业男声

要求：为每段台词生成独立的音频文件，保持角色声音特色。"""
            },
            dependencies=["assign_roles"]
        ),
        StepTemplate(
            template_step_id="match_video_clips",
            name="智能匹配视频片段",
            description_template="根据台词内容和情感匹配现有视频片段",
            tool_to_use="visual_expert",
            input_template={
                "prompt": """基于生成的台词和TTS音频，智能匹配最合适的视频片段。

可用视频资产包含：
- 医生片段：自我介绍、诊断、安慰、调解、询问
- 患者家属片段：求医、隐瞒、愧疚、揭示、求助
- 患者片段：困惑、思考、描述、坚持、感激

匹配规则：
1. 角色匹配：根据台词角色选择对应视频分类
2. 情感匹配：根据台词情感选择合适的视频片段
3. 时长匹配：选择时长接近的视频片段

输出视频序列的JSON格式。"""
            },
            dependencies=["generate_tts"]
        ),
        StepTemplate(
            template_step_id="compose_final_video",
            name="合成最终小品视频",
            description_template="将视频片段和音频合成完整小品作品",
            tool_to_use="video_expert",
            input_template={
                "task_description": """将视频片段和TTS音频合成为完整的小品视频。

合成要求：
1. 按剧本顺序排列视频片段
2. 确保音画同步
3. 添加字幕显示台词
4. {% if background_music %}添加轻松的东北风格背景音乐{% endif %}
5. 生成MP4格式，1080p清晰度

时长控制：
{% if duration_preference == 'short' %}1-2分钟
{% elif duration_preference == 'medium' %}2-4分钟
{% else %}4-6分钟{% endif %}"""
            },
            dependencies=["match_video_clips"]
        )
    ]

    template = PlanTemplate(
        template_id="zhao_benshan_xiangsheng",
        name="赵本山小品生成器",
        description="基于赵本山和宋丹丹的声音克隆，生成东北风格小品视频",
        category="entertainment",
        tags=["xiangsheng", "zhao_benshan", "northeast", "comedy", "video"],
        step_templates=step_templates,
        parameters=params,
        estimated_duration=4800  # 80分钟（复杂模板）
    )

    register_template(template)
    print("   ✅ 赵本山小品生成模板已注册")


def main():
    """注册所有常用模板"""
    print("🚀 开始注册常用模板\n")

    try:
        create_city_poster_template()
        create_ai_parody_video_template()
        create_brand_design_template()
        create_zhao_benshan_xiangsheng_template()
        
        print("\n🎉 所有模板注册完成！")
        print("\n📋 已注册的模板：")
        
        from src.tools.template_tools import get_registered_templates
        templates = get_registered_templates()
        
        for template_id, template_name in templates.items():
            print(f"   • {template_id}: {template_name}")
        
        print(f"\n总计: {len(templates)} 个模板")
        
    except Exception as e:
        print(f"\n❌ 模板注册过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
DeerFlow 项目结构整理脚本
自动化整理项目目录结构，清理冗余文件，规范命名
"""

import os
import shutil
import logging
from pathlib import Path
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProjectCleaner:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root.parent / f"deer-flow-backup-{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def create_backup(self):
        """创建项目备份"""
        logger.info(f"创建备份到: {self.backup_dir}")
        shutil.copytree(self.project_root, self.backup_dir, ignore=shutil.ignore_patterns(
            '__pycache__', '*.pyc', 'node_modules', '.git', '*.log'
        ))
        logger.info("备份完成")
    
    def clean_temporary_files(self):
        """清理临时文件和目录"""
        logger.info("清理临时文件...")
        
        # 删除临时目录
        temp_dirs = [
            'backup_tools_simplification',
            'image',  # 只包含 FRAMEWORK 文件
        ]
        
        for temp_dir in temp_dirs:
            temp_path = self.project_root / temp_dir
            if temp_path.exists():
                logger.info(f"删除临时目录: {temp_dir}")
                shutil.rmtree(temp_path)
        
        # 删除临时文件
        temp_files = [
            'debug_config.py',
            'pre-commit',  # 应该是 .pre-commit-config.yaml
        ]
        
        for temp_file in temp_files:
            temp_path = self.project_root / temp_file
            if temp_path.exists():
                logger.info(f"删除临时文件: {temp_file}")
                temp_path.unlink()
    
    def merge_test_directories(self):
        """合并测试目录"""
        logger.info("合并测试目录...")
        
        test_dir = self.project_root / 'test'
        tests_dir = self.project_root / 'tests'
        legacy_dir = tests_dir / 'legacy'
        
        if test_dir.exists():
            # 创建 legacy 目录
            legacy_dir.mkdir(exist_ok=True)
            
            # 移动 test/ 下的文件到 tests/legacy/
            for item in test_dir.iterdir():
                if item.is_file():
                    dest = legacy_dir / item.name
                    logger.info(f"移动 {item} -> {dest}")
                    shutil.move(str(item), str(dest))
            
            # 删除空的 test 目录
            if not any(test_dir.iterdir()):
                test_dir.rmdir()
                logger.info("删除空的 test/ 目录")
    
    def organize_documentation(self):
        """整理文档结构"""
        logger.info("整理文档结构...")
        
        docs_dir = self.project_root / 'docs'
        
        # 创建文档子目录
        subdirs = {
            '架构设计': ['框架.md', 'architecture_visual_explanation.md', 'final_architecture_recommendation.md'],
            '开发指南': ['GRAPH_V2_DEVELOPER_GUIDE.md', 'GRAPH_V2_QUICK_REFERENCE.md', 'CONTRIBUTING.md'],
            '功能说明': ['PLAN_AND_TEMPLATE_SYSTEM.md', '语音克隆工具使用指南.md', 'TODO_multimodal_implementation.md', 'UNDERSTANDING_TOOLS_README.md'],
            '项目管理': ['CLEANUP_PLAN.md', 'PROJECT_CLEANUP_REPORT.md', 'PROMPT_OPTIMIZATION_PLAN.md', 'MASTER_AGENT_UNDERSTANDING_INTEGRATION.md']
        }
        
        for subdir, files in subdirs.items():
            subdir_path = docs_dir / subdir
            subdir_path.mkdir(exist_ok=True)
            
            for file_name in files:
                # 检查根目录
                root_file = self.project_root / file_name
                if root_file.exists():
                    dest = subdir_path / file_name
                    logger.info(f"移动文档: {file_name} -> docs/{subdir}/")
                    shutil.move(str(root_file), str(dest))
                
                # 检查 docs 目录
                docs_file = docs_dir / file_name
                if docs_file.exists() and docs_file != subdir_path / file_name:
                    dest = subdir_path / file_name
                    logger.info(f"重新组织文档: docs/{file_name} -> docs/{subdir}/")
                    shutil.move(str(docs_file), str(dest))
        
        # 处理 CONTRIBUTING 文件（无扩展名）
        contributing_file = self.project_root / 'CONTRIBUTING'
        if contributing_file.exists():
            dest = docs_dir / '开发指南' / 'CONTRIBUTING.md'
            logger.info(f"移动 CONTRIBUTING -> docs/开发指南/CONTRIBUTING.md")
            shutil.move(str(contributing_file), str(dest))
    
    def clean_database_directories(self):
        """清理重复的数据库目录"""
        logger.info("清理数据库目录...")
        
        db_dir = self.project_root / 'db'
        data_db_dir = self.project_root / 'data' / 'db'
        
        if db_dir.exists() and data_db_dir.exists():
            # 将 db/ 下的文件移动到 data/db/
            for item in db_dir.iterdir():
                if item.is_file():
                    dest = data_db_dir / item.name
                    if not dest.exists():
                        logger.info(f"移动数据库文件: {item.name} -> data/db/")
                        shutil.move(str(item), str(dest))
                    else:
                        logger.info(f"跳过重复文件: {item.name}")
            
            # 删除空的 db 目录
            if not any(db_dir.iterdir()):
                db_dir.rmdir()
                logger.info("删除空的 db/ 目录")
    
    def create_directory_readme(self):
        """为主要目录创建 README 文件"""
        logger.info("创建目录说明文件...")
        
        readme_contents = {
            'tests/legacy': """# Legacy Tests

这个目录包含从旧的 `test/` 目录迁移过来的测试文件。

## 注意事项
- 这些测试可能需要更新以适应新的项目结构
- 建议逐步将这些测试重构到相应的测试分类中
- 验证测试是否仍然有效和必要
""",
            'docs/架构设计': """# 架构设计文档

包含 DeerFlow 项目的架构设计相关文档。

## 文件说明
- `框架.md` - 项目框架概述
- `architecture_visual_explanation.md` - 架构可视化说明
- `final_architecture_recommendation.md` - 最终架构建议
""",
            'docs/开发指南': """# 开发指南

包含开发者相关的指南和文档。

## 文件说明
- `GRAPH_V2_DEVELOPER_GUIDE.md` - Graph V2 开发指南
- `GRAPH_V2_QUICK_REFERENCE.md` - Graph V2 快速参考
- `CONTRIBUTING.md` - 贡献指南
""",
            'docs/功能说明': """# 功能说明文档

包含各个功能模块的详细说明。

## 文件说明
- `PLAN_AND_TEMPLATE_SYSTEM.md` - 计划和模板系统
- `语音克隆工具使用指南.md` - 语音克隆功能使用指南
- `TODO_multimodal_implementation.md` - 多模态实现待办事项
- `UNDERSTANDING_TOOLS_README.md` - 理解工具说明
""",
            'docs/项目管理': """# 项目管理文档

包含项目管理相关的文档和计划。

## 文件说明
- `CLEANUP_PLAN.md` - 清理计划
- `PROJECT_CLEANUP_REPORT.md` - 项目清理报告
- `PROMPT_OPTIMIZATION_PLAN.md` - 提示词优化计划
- `MASTER_AGENT_UNDERSTANDING_INTEGRATION.md` - 主代理理解集成
"""
        }
        
        for dir_path, content in readme_contents.items():
            readme_path = self.project_root / dir_path / 'README.md'
            readme_path.parent.mkdir(parents=True, exist_ok=True)
            
            if not readme_path.exists():
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info(f"创建 README: {dir_path}/README.md")
    
    def update_main_readme(self):
        """更新主 README 文件"""
        logger.info("更新主 README 文件...")
        
        readme_path = self.project_root / 'README.md'
        if readme_path.exists():
            # 在 README 末尾添加目录结构说明
            with open(readme_path, 'a', encoding='utf-8') as f:
                f.write(f"""

## 项目结构

```
deer-flow/
├── src/                    # 源代码
├── tests/                  # 测试代码
├── docs/                   # 文档
│   ├── 架构设计/           # 架构设计文档
│   ├── 开发指南/           # 开发指南
│   ├── 功能说明/           # 功能说明
│   └── 项目管理/           # 项目管理文档
├── scripts/                # 脚本工具
├── config/                 # 配置模板
├── data/                   # 数据文件
├── archive/                # 归档文件
└── web/                    # Web 前端
```

> 项目结构已于 {datetime.now().strftime('%Y-%m-%d')} 重新整理
""")
    
    def run_cleanup(self):
        """执行完整的清理流程"""
        logger.info("开始项目结构整理...")
        
        try:
            # 1. 创建备份
            self.create_backup()
            
            # 2. 清理临时文件
            self.clean_temporary_files()
            
            # 3. 合并测试目录
            self.merge_test_directories()
            
            # 4. 整理文档
            self.organize_documentation()
            
            # 5. 清理数据库目录
            self.clean_database_directories()
            
            # 6. 创建目录说明
            self.create_directory_readme()
            
            # 7. 更新主 README
            self.update_main_readme()
            
            logger.info("项目结构整理完成！")
            logger.info(f"备份位置: {self.backup_dir}")
            
        except Exception as e:
            logger.error(f"整理过程中出现错误: {e}")
            logger.info(f"可以从备份恢复: {self.backup_dir}")
            raise

if __name__ == "__main__":
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # 执行清理
    cleaner = ProjectCleaner(str(project_root))
    cleaner.run_cleanup()

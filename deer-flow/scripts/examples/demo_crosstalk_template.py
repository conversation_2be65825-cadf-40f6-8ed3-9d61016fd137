#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo: Crosstalk (相声) Template → UnifiedPlan 生成与概览

用法:
  python deer-flow/scripts/examples/demo_crosstalk_template.py \
    --materials-dir /absolute/path/to/materials

说明:
- 该脚本会:
  1) 注册内置模板（包含 crosstalk_video）
  2) 使用 use_template 以工程化方式生成 UnifiedPlan
  3) 打印计划关键信息，便于调试

注意:
- 请将 --materials-dir 指向你的现成素材根目录
- 可在下方的 DEFAULT_SCRIPT_TEXT 替换为你的真实剧本文本
"""

import argparse
import json
import os
import sys
from pathlib import Path
from typing import Any, Dict

# 将项目的 deer-flow 目录加入 sys.path，以便导入 src.*
CURRENT_FILE = Path(__file__).resolve()
PROJECT_DIR = CURRENT_FILE.parents[2]  # 指向 deer-flow 目录
if str(PROJECT_DIR) not in sys.path:
    sys.path.insert(0, str(PROJECT_DIR))

from src.templates.builtin_templates import load_builtin_templates  # noqa: E402
from src.tools.template_tools import use_template  # noqa: E402

# 一个最小的示例剧本文本（请按需替换）
DEFAULT_SCRIPT_TEXT = (
    "相声标题\n\n"
    "[Natural] 张三: 各位观众朋友们，大家好！今天我们来聊聊网购这个话题\n"
    "[Confused] 李四: 网购？现在谁不网购啊，这有什么好聊的？\n"
    "[Emphatic] 张三: 嗨！你这就不懂了，网购里面学问大着呢！\n"
    "[Natural] 李四: 哦？那你说说看，有什么学问？\n"
)


def build_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Crosstalk Template Demo")
    parser.add_argument(
        "--materials-dir",
        type=str,
        required=True,
        help="现成视频素材根目录（用于匹配步骤）",
    )
    parser.add_argument(
        "--dou",
        type=str,
        default="张三",
        help="逗哏角色名",
    )
    parser.add_argument(
        "--peng",
        type=str,
        default="李四",
        help="捧哏角色名",
    )
    parser.add_argument(
        "--style",
        type=str,
        default="classic",
        choices=["classic", "modern", "absurd"],
        help="风格",
    )
    parser.add_argument(
        "--duration",
        type=str,
        default="medium",
        choices=["short", "medium", "long"],
        help="时长偏好",
    )
    parser.add_argument(
        "--include-bgm",
        action="store_true",
        help="是否在成片中添加BGM",
    )
    parser.add_argument(
        "--script-text",
        type=str,
        default=DEFAULT_SCRIPT_TEXT,
        help="相声完整剧本文本（包含 [Tone] 角色: 台词 格式）",
    )
    return parser.parse_args()


def pretty_print_plan(plan: Dict[str, Any]) -> None:
    print("\n==== 计划概览 ====")
    print(f"plan_id: {plan.get('plan_id')}")
    print(f"original_task: {plan.get('original_task')}")
    print(f"is_from_template: {plan.get('is_from_template')}")
    print(f"template_id: {plan.get('template_id')}")

    steps = plan.get("steps", [])
    print(f"步骤总数: {len(steps)}")
    for idx, step in enumerate(steps, 1):
        print(
            f"  {idx}. {step.get('step_id')} | {step.get('name')} | tool={step.get('tool_to_use')} | deps={step.get('dependencies')}"
        )


def main() -> None:
    args = build_args()

    # 1) 注册内置模板
    load_builtin_templates()

    # 2) 使用工程化 use_template 直接生成计划
    input_payload = {
        "template_id": "crosstalk_video",
        "params": {
            "script_text": args.script_text,
            "dou_gen_name": args.dou,
            "peng_gen_name": args.peng,
            "materials_dir": args.materials_dir,
            "style": args.style,
            "duration": args.duration,
            "include_bgm": bool(args.include_bgm),
        },
        # 可选: user_context，可覆盖 original_task
        # "user_context": "为相声视频生成工作流",
    }

    print("\n🧪 调用 use_template 生成计划...")
    result = use_template.invoke(input_payload)

    # 3) 打印结果与计划摘要
    if isinstance(result, dict):
        success = result.get("success", False)
        print(f"success: {success}")
        if not success:
            print("error:", result.get("error"))
            available = result.get("available_templates")
            if available:
                print("available_templates:", available)
            sys.exit(1)

        plan = result.get("plan")
        if plan is None:
            print("❌ 未返回计划对象")
            sys.exit(2)

        # 打印结构化计划
        pretty_print_plan(plan)

        # 可选: 保存到临时文件便于查看
        out_dir = PROJECT_DIR / "tmp"
        out_dir.mkdir(parents=True, exist_ok=True)
        out_path = out_dir / "crosstalk_plan.json"
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(plan, f, ensure_ascii=False, indent=2)
        print(f"\n📄 已保存计划到: {out_path}")

    else:
        # 某些版本的 tool 可能返回字符串，尝试解析
        try:
            parsed = json.loads(result)  # type: ignore[arg-type]
            pretty_print_plan(parsed.get("plan", {}))
        except Exception:
            print("原始返回:", result)


if __name__ == "__main__":
    main()

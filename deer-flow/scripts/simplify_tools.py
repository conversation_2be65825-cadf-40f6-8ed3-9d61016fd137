#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
simplify_tools.py

简化工具体系，实现我们讨论的新架构：
1. 简化规划工具为create_plan
2. 简化模板工具为use_template  
3. 简化状态管理工具为3个核心工具
4. 保持专家工具不变
5. 删除冗余工具
"""

import os
import shutil
from pathlib import Path


def backup_current_tools():
    """备份当前工具文件"""
    print("📦 备份当前工具文件...")
    
    backup_dir = Path("backup_tools_simplification")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "src/tools/planning.py",
        "src/tools/state_management.py", 
        "src/tools/template_tools.py"
    ]
    
    for file_path in files_to_backup:
        if Path(file_path).exists():
            shutil.copy2(file_path, backup_dir / Path(file_path).name)
            print(f"   ✅ 备份: {file_path}")


def create_simplified_planning_tools():
    """创建简化的规划工具"""
    print("\n🎯 创建简化的规划工具...")
    
    content = '''# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
planning.py - 简化版

简化的规划工具，只保留核心的create_plan功能。
Master Agent根据任务复杂度自主决定是否使用规划工具。
"""

from typing import Dict, Any, Optional
from langchain_core.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable
from pydantic import BaseModel, Field
from langchain_core.output_parsers import PydanticOutputParser

from src.utils.prompt_utils import get_agent_prompt_template
from src.graph_v2.unified_models import UnifiedPlan
from src.graph_v2.models import Plan  # 用于LLM输出解析
from src.graph_v2.migration_utils import migrate_legacy_plan_to_unified

# Global variable for the planner chain
_planner_chain: Optional[Runnable] = None


class CreatePlanInput(BaseModel):
    task: str = Field(description="用户的任务描述，需要创建执行计划")


def _create_planner_chain(llm: Runnable) -> Runnable:
    """创建规划LLM链"""
    parser = PydanticOutputParser(pydantic_object=Plan)
    base_prompt = get_agent_prompt_template("planner_prompt_zh")
    prompt_template = base_prompt.partial(
        format_instructions=parser.get_format_instructions()
    )
    return prompt_template | llm | parser


@tool("create_plan", args_schema=CreatePlanInput)
def create_plan(task: str) -> Dict[str, UnifiedPlan]:
    """
    为复杂任务创建结构化执行计划
    
    当用户的任务需要多个步骤或涉及多种媒体类型时使用此工具。
    例如：制作视频、创建系列内容、复杂的创意项目等。
    
    适用场景：
    - 任务涉及多个步骤（如：先生成图片，再制作视频）
    - 需要创建系列内容（如：多个城市的海报）
    - 复杂的创意项目（如：完整的品牌设计）
    - 用户明确要求制定计划
    
    Args:
        task: 用户的任务描述
        
    Returns:
        {"plan": UnifiedPlan} - 结构化的执行计划
    """
    global _planner_chain
    if _planner_chain is None:
        raise ValueError("Planning tools not initialized. Please call initialize_planning_tools() first.")
    
    try:
        # 使用LLM生成Legacy Plan
        legacy_plan = _planner_chain.invoke({
            "task": task, 
            "plan": "No existing plan."
        })
        
        # 设置原始任务
        legacy_plan.original_task = task
        
        # 转换为UnifiedPlan
        unified_plan = migrate_legacy_plan_to_unified(legacy_plan)
        
        print(f"📋 create_plan生成计划: {len(unified_plan.steps)}个步骤")
        for i, step in enumerate(unified_plan.steps, 1):
            print(f"   {i}. {step.name} ({step.tool_to_use})")
        
        return {"plan": unified_plan}
        
    except Exception as e:
        print(f"❌ create_plan执行失败: {e}")
        # 返回空计划作为fallback
        fallback_plan = UnifiedPlan(original_task=task, steps=[])
        return {"plan": fallback_plan}


def initialize_planning_tools(llm: Runnable):
    """
    初始化规划工具的LLM链
    必须在使用create_plan之前调用
    """
    global _planner_chain
    _planner_chain = _create_planner_chain(llm)


# 导出的工具列表
planning_tools = [create_plan]
'''
    
    with open("src/tools/planning.py", 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("   ✅ 创建简化的planning.py")


def create_simplified_template_tools():
    """创建简化的模板工具"""
    print("\n🎯 创建简化的模板工具...")
    
    content = '''# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
template_tools.py - 简化版

简化的模板工具，只保留核心的use_template功能。
采用工程化方式，模板ID由外部系统传入，不依赖AI选择。
"""

from typing import Dict, Any, Optional
from langchain_core.tools import tool
from pydantic import BaseModel, Field

from src.graph_v2.template_models import PlanTemplate
from src.graph_v2.template_renderer import template_renderer, TemplateRenderError
from src.graph_v2.unified_models import UnifiedPlan

# 全局模板注册表
_template_registry: Dict[str, PlanTemplate] = {}


class UseTemplateInput(BaseModel):
    template_id: str = Field(description="模板标识符")
    params: Dict[str, Any] = Field(description="模板参数")
    user_context: Optional[str] = Field(default=None, description="可选的用户上下文描述")


@tool("use_template", args_schema=UseTemplateInput)
def use_template(template_id: str, params: Dict[str, Any], user_context: Optional[str] = None) -> Dict[str, Any]:
    """
    使用指定模板创建执行计划
    
    适用于已知模板ID的场景，通常由上层系统或用户界面传入。
    这是工程化的模板使用方式，不依赖AI进行模板选择。
    
    常用模板ID：
    - "city_poster_series": 城市海报系列
    - "ai_parody_video": AI鬼畜视频制作
    - "brand_design_suite": 品牌设计套件
    
    Args:
        template_id: 模板标识符（如 "city_poster_series"）
        params: 模板参数（如 {"character": "哪吒", "style": "modern"}）
        user_context: 可选的用户上下文描述
        
    Returns:
        {
            "success": bool,
            "plan": UnifiedPlan,
            "template_used": str,
            "error": Optional[str],
            "metadata": Dict[str, Any]
        }
    """
    try:
        # 获取模板
        template = _template_registry.get(template_id)
        if not template:
            available_templates = list(_template_registry.keys())
            return {
                "success": False,
                "plan": None,
                "template_used": template_id,
                "error": f"模板 '{template_id}' 不存在",
                "available_templates": available_templates
            }
        
        # 使用模板渲染器创建计划
        try:
            plan = template_renderer.render_plan(template, params)
            
            # 如果提供了用户上下文，更新任务描述
            if user_context:
                plan.original_task = user_context
            
        except TemplateRenderError as e:
            return {
                "success": False,
                "plan": None,
                "template_used": template_id,
                "error": f"模板渲染失败: {str(e)}"
            }
        
        # 更新模板使用统计
        template.usage_count += 1
        
        print(f"📋 use_template成功: {template_id} -> {len(plan.steps)}个步骤")
        
        return {
            "success": True,
            "plan": plan,
            "template_used": template_id,
            "error": None,
            "metadata": {
                "template_name": template.name,
                "parameters_used": params,
                "total_steps": len(plan.steps),
                "estimated_duration": template.estimated_duration
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "plan": None,
            "template_used": template_id,
            "error": f"使用模板时出现错误: {str(e)}"
        }


def register_template(template: PlanTemplate):
    """注册模板到全局注册表"""
    _template_registry[template.template_id] = template
    print(f"📝 注册模板: {template.template_id} - {template.name}")


def get_registered_templates() -> Dict[str, str]:
    """获取已注册的模板列表（用于调试）"""
    return {tid: template.name for tid, template in _template_registry.items()}


# 导出的工具列表
template_tools = [use_template]
'''
    
    with open("src/tools/template_tools.py", 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("   ✅ 创建简化的template_tools.py")


def create_simplified_state_management():
    """创建简化的状态管理工具"""
    print("\n🎯 创建简化的状态管理工具...")
    
    content = '''# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
state_management.py - 简化版

简化的状态管理工具，只保留3个核心功能：
1. get_plan_status - 获取计划状态
2. get_next_step - 获取下一步骤
3. report_step_completion - 报告步骤完成
"""

from typing import Dict, Any
from langchain_core.tools import tool
from pydantic import BaseModel, Field
import json
from datetime import datetime

from src.graph_v2.types import State
from src.graph_v2.unified_models import UnifiedPlan
from src.graph_v2.migration_utils import ensure_unified_plan


class ReportCompletionInput(BaseModel):
    step_id: str = Field(description="步骤标识符")
    status: str = Field(description="执行状态：completed 或 failed")
    result: Dict[str, Any] = Field(description="执行结果数据")


@tool
def get_plan_status(state: State) -> str:
    """
    获取当前计划的整体状态和进度
    
    返回计划执行进度、已完成步骤、待执行步骤等信息。
    适用于Master Agent需要了解整体进度时。
    
    Returns:
        JSON格式的计划状态信息
    """
    plan = state.get("plan")
    if not plan:
        return "❌ 当前没有执行计划"
    
    try:
        unified_plan = ensure_unified_plan(plan)
        progress = unified_plan.get_progress_info()
        
        status_info = {
            "plan_id": unified_plan.plan_id,
            "original_task": unified_plan.original_task,
            "total_steps": progress["total_steps"],
            "completed_steps": progress["completed_steps"],
            "progress_percentage": progress["progress_percentage"],
            "is_complete": unified_plan.is_complete(),
            "has_failed": unified_plan.has_failed(),
            "template_info": {
                "is_from_template": unified_plan.is_from_template,
                "template_id": unified_plan.template_id
            }
        }
        
        return json.dumps(status_info, indent=2, ensure_ascii=False)
        
    except Exception as e:
        return f"❌ 获取计划状态失败: {str(e)}"


@tool  
def get_next_step(state: State) -> str:
    """
    获取下一个需要执行的步骤详情
    
    返回下一个可执行步骤的ID、名称、描述、工具和输入参数。
    适用于Master Agent需要知道下一步做什么时。
    
    Returns:
        JSON格式的步骤详情，或提示信息
    """
    plan = state.get("plan")
    if not plan:
        return "❌ 当前没有执行计划，请先创建计划"
    
    try:
        unified_plan = ensure_unified_plan(plan)
        executable_steps = unified_plan.get_next_executable_steps()
        
        if not executable_steps:
            if unified_plan.is_complete():
                return "✅ 计划已完成，所有步骤都已执行"
            elif unified_plan.has_failed():
                return "❌ 计划执行失败，存在无法重试的失败步骤"
            else:
                return "⏸️ 暂无可执行步骤，可能存在依赖关系问题"
        
        next_step = executable_steps[0]
        step_info = {
            "step_id": next_step.step_id,
            "name": next_step.name,
            "description": next_step.description,
            "tool_to_use": next_step.tool_to_use,
            "inputs": next_step.inputs,
            "dependencies": next_step.dependencies,
            "status": next_step.status
        }
        
        return json.dumps(step_info, indent=2, ensure_ascii=False)
        
    except Exception as e:
        return f"❌ 获取下一步骤失败: {str(e)}"


@tool("report_step_completion", args_schema=ReportCompletionInput)
def report_step_completion(step_id: str, status: str, result: Dict[str, Any]) -> str:
    """
    报告步骤执行完成情况
    
    Master Agent执行完步骤后必须调用此工具报告结果。
    这是状态管理的核心机制。
    
    Args:
        step_id: 步骤标识符
        status: "completed" 或 "failed"
        result: 执行结果数据
        
    Returns:
        确认消息和状态更新指令
    """
    # 生成状态更新指令
    update_instruction = {
        "action": "update_step_status",
        "step_id": step_id,
        "status": status,
        "result": result,
        "timestamp": datetime.now().isoformat()
    }
    
    status_emoji = "✅" if status == "completed" else "❌"
    return f"{status_emoji} 步骤 {step_id} 状态已报告: {json.dumps(update_instruction, ensure_ascii=False)}"


# 导出的工具列表
state_management_tools = [
    get_plan_status,
    get_next_step,
    report_step_completion
]
'''
    
    with open("src/tools/state_management.py", 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("   ✅ 创建简化的state_management.py")


def update_nodes_integration():
    """更新nodes.py中的工具集成"""
    print("\n🔧 更新Master Agent工具集成...")
    
    # 这里只是提示，实际的nodes.py更新需要手动进行
    print("   ⚠️ 需要手动更新 src/graph_v2/nodes.py 中的工具导入：")
    print("   - from src.tools.planning import create_plan")
    print("   - from src.tools.template_tools import use_template")  
    print("   - from src.tools.state_management import state_management_tools")
    print("   - 移除旧的工具导入")


def main():
    """执行工具简化"""
    print("🚀 开始简化工具体系\n")
    
    # 确认操作
    response = input("⚠️ 这将重写planning、template_tools、state_management文件，是否继续？(y/N): ")
    if response.lower() != 'y':
        print("❌ 操作已取消")
        return
    
    try:
        backup_current_tools()
        create_simplified_planning_tools()
        create_simplified_template_tools()
        create_simplified_state_management()
        update_nodes_integration()
        
        print("\n🎉 工具简化完成！")
        print("\n📋 简化结果：")
        print("✅ 规划工具：planner_tool + reviser_tool → create_plan")
        print("✅ 模板工具：4个工具 → use_template")
        print("✅ 状态管理：10个工具 → 3个核心工具")
        print("✅ 专家工具：保持不变")
        
        print("\n🔧 后续步骤：")
        print("1. 手动更新 src/graph_v2/nodes.py 中的工具导入")
        print("2. 运行测试验证功能正常")
        print("3. 更新Master Agent的prompt以反映新工具")
        
    except Exception as e:
        print(f"\n❌ 简化过程中出现错误: {e}")
        print("请检查备份文件并手动恢复")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
调试火山引擎API的说话人识别功能
"""

import logging
import sys
import os
import json
import requests

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_volcengine_api():
    """调试火山引擎API调用"""
    logger.info("🔍 调试火山引擎API说话人识别...")
    
    try:
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入必要的模块
        from tools.audio.video_subtitle_extraction import _process_media_input
        
        # 获取API密钥
        api_key = os.getenv("VOLCENGINE_SUBTITLE_KEY")
        appid = os.getenv("VOLCENGINE_SUBTITLE_APPID")
        
        if not api_key or not appid:
            logger.error("❌ 缺少火山引擎API配置")
            return False
        
        logger.info(f"🔑 API Key: {api_key[:10]}...")
        logger.info(f"🆔 App ID: {appid}")
        
        # 处理媒体输入
        video_url = "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/bilibili_BV1Jq4y1C7F8_GAOQING.mp4"
        logger.info("📹 处理媒体输入...")
        media_result = _process_media_input(video_url)
        
        if not media_result["success"]:
            logger.error(f"❌ 媒体处理失败: {media_result['error']}")
            return False
        
        audio_url = media_result["audio_url"]
        local_path = audio_url[7:]  # 移除 "file://" 前缀
        logger.info(f"🎵 音频文件: {local_path}")
        
        # 手动构建API请求
        url = "https://openspeech.bytedance.com/api/v1/vc"
        
        # 测试不同的参数组合
        test_configs = [
            {
                "name": "标准配置",
                "params": {
                    "appid": appid,
                    "token": api_key,
                    "language": "zh-CN",
                    "caption_type": "auto",
                    "words_per_line": 15,
                    "max_lines": 2,
                    "use_itn": "true",
                    "use_punc": "true",
                    "with_speaker_info": "true",
                    "use_ddc": "true"
                }
            },
            {
                "name": "强制说话人识别",
                "params": {
                    "appid": appid,
                    "token": api_key,
                    "language": "zh-CN",
                    "caption_type": "speech",  # 只识别说话，不识别唱歌
                    "words_per_line": 15,
                    "max_lines": 2,
                    "use_itn": "true",
                    "use_punc": "true",
                    "with_speaker_info": "True",  # 大写True
                    "use_ddc": "true"
                }
            },
            {
                "name": "最小参数",
                "params": {
                    "appid": appid,
                    "token": api_key,
                    "language": "zh-CN",
                    "with_speaker_info": "true"
                }
            }
        ]
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n{'='*60}")
            logger.info(f"🧪 测试配置 {i+1}: {config['name']}")
            logger.info(f"{'='*60}")
            
            params = config["params"]
            logger.info("📋 请求参数:")
            for key, value in params.items():
                logger.info(f"   {key}: {value}")
            
            # 构建请求
            headers = {
                "Content-Type": "audio/wav"
            }
            
            # 读取音频文件
            with open(local_path, 'rb') as f:
                audio_data = f.read()
            
            logger.info(f"📁 音频文件大小: {len(audio_data)} 字节")
            
            # 发送请求
            logger.info("📤 发送API请求...")
            try:
                response = requests.post(
                    url,
                    params=params,
                    headers=headers,
                    data=audio_data,
                    timeout=300
                )
                
                logger.info(f"📥 响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 保存完整响应
                    output_file = f"volcengine_debug_response_{i+1}.json"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(result, f, ensure_ascii=False, indent=2)
                    logger.info(f"💾 响应已保存到: {output_file}")
                    
                    # 分析响应
                    logger.info("📊 响应分析:")
                    logger.info(f"   code: {result.get('code', 'N/A')}")
                    logger.info(f"   message: {result.get('message', 'N/A')}")
                    
                    if "utterances" in result:
                        utterances = result["utterances"]
                        logger.info(f"   utterances数量: {len(utterances)}")
                        
                        # 检查前几个utterance的说话人信息
                        speaker_found = False
                        for j, utterance in enumerate(utterances[:5]):
                            logger.info(f"   utterance {j+1}:")
                            logger.info(f"      text: {utterance.get('text', '')[:30]}...")
                            
                            # 检查utterance级别的speaker
                            if "attribute" in utterance:
                                attr = utterance["attribute"]
                                logger.info(f"      attribute: {attr}")
                                
                                if "speaker" in attr:
                                    logger.info(f"      🎤 speaker: {attr['speaker']}")
                                    speaker_found = True
                                else:
                                    logger.info("      ❌ 没有speaker字段")
                            
                            # 检查words级别的speaker
                            if "words" in utterance:
                                words = utterance["words"]
                                for k, word in enumerate(words[:2]):  # 只检查前2个词
                                    if "attribute" in word and "speaker" in word["attribute"]:
                                        logger.info(f"         word {k+1} speaker: {word['attribute']['speaker']}")
                                        speaker_found = True
                        
                        if speaker_found:
                            logger.info("🎉 找到说话人信息！")
                        else:
                            logger.warning("⚠️ 没有找到说话人信息")
                    
                else:
                    logger.error(f"❌ API请求失败: {response.status_code}")
                    logger.error(f"   响应内容: {response.text}")
                
            except Exception as e:
                logger.error(f"❌ API请求异常: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_api_documentation():
    """检查API文档和参数"""
    logger.info("📚 检查API文档和参数...")
    
    logger.info("🔍 火山引擎说话人识别可能的问题:")
    logger.info("   1. API版本问题 - 可能需要使用不同的API端点")
    logger.info("   2. 参数格式问题 - with_speaker_info可能需要不同的值")
    logger.info("   3. 音频格式问题 - 可能需要特定的音频格式")
    logger.info("   4. 音频长度问题 - 可能需要足够长的音频")
    logger.info("   5. 账户权限问题 - 可能需要开通说话人识别功能")
    logger.info("")
    logger.info("💡 建议的解决方案:")
    logger.info("   1. 尝试不同的参数组合")
    logger.info("   2. 检查火山引擎控制台的功能开通状态")
    logger.info("   3. 联系火山引擎技术支持")
    logger.info("   4. 考虑使用其他说话人识别服务")

def main():
    """主函数"""
    logger.info("🎯 开始调试火山引擎API说话人识别...")
    
    tests = [
        ("API调试测试", debug_volcengine_api),
        ("文档检查", check_api_documentation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*80}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*80}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 完成")
            else:
                logger.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*80}")
    logger.info("🎯 调试总结")
    logger.info(f"{'='*80}")
    
    logger.info("🔍 **问题确认:**")
    logger.info("   您说得对，如果是小品确实应该有多个说话人")
    logger.info("   火山引擎API没有返回说话人信息可能是:")
    logger.info("   1. API参数配置问题")
    logger.info("   2. 账户权限问题")
    logger.info("   3. API版本或端点问题")
    logger.info("")
    logger.info("📁 **生成的文件:**")
    logger.info("   • volcengine_debug_response_*.json - 不同配置的API响应")
    logger.info("")
    logger.info("🔧 **下一步:**")
    logger.info("   1. 检查生成的响应文件")
    logger.info("   2. 对比不同配置的结果")
    logger.info("   3. 如果仍然没有说话人信息，可能需要联系火山引擎支持")

if __name__ == "__main__":
    main()

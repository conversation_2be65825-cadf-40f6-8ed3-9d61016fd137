#!/usr/bin/env python3
"""
检查通义听悟配置的脚本
"""

import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_appkey_format():
    """检查AppKey格式"""
    logger.info("🔍 检查AppKey格式...")
    
    provided_appkey = "TQvDYpoD8ofQPIeG"
    logger.info(f"提供的AppKey: {provided_appkey}")
    logger.info(f"AppKey长度: {len(provided_appkey)}")
    
    # 通义听悟的AppKey通常是更长的字符串
    if len(provided_appkey) < 20:
        logger.warning("⚠️ AppKey长度可能不正确")
        logger.info("通义听悟的AppKey通常是20+字符的长字符串")
    else:
        logger.info("✅ AppKey长度看起来正常")

def show_troubleshooting_steps():
    """显示排查步骤"""
    logger.info("\n" + "="*80)
    logger.info("🔧 通义听悟配置排查步骤")
    logger.info("="*80)
    
    logger.info("\n📋 第1步: 确认服务开通")
    logger.info("1. 访问: https://nls-portal.console.aliyun.com/tingwu/projects")
    logger.info("2. 检查是否能正常访问通义听悟控制台")
    logger.info("3. 确认服务状态是否为'已开通'")
    
    logger.info("\n📋 第2步: 检查项目状态")
    logger.info("1. 在通义听悟控制台查看项目列表")
    logger.info("2. 确认项目'蛙蛙多模态'状态正常")
    logger.info("3. 检查项目是否被暂停或删除")
    
    logger.info("\n📋 第3步: 验证AppKey")
    logger.info("1. 点击项目'蛙蛙多模态'")
    logger.info("2. 查看项目详情中的AppKey")
    logger.info("3. 确认AppKey是否与提供的一致")
    logger.info(f"   当前使用: TQvDYpoD8ofQPIeG")
    
    logger.info("\n📋 第4步: 检查权限")
    logger.info("1. 访问: https://ram.console.aliyun.com/")
    logger.info("2. 检查AccessKey对应的用户权限")
    logger.info("3. 确认是否有'智能语音交互'相关权限")
    
    logger.info("\n📋 第5步: 检查账户状态")
    logger.info("1. 访问: https://usercenter2.aliyun.com/finance/overview")
    logger.info("2. 确认账户余额充足")
    logger.info("3. 检查是否有欠费或限制")

def show_alternative_solutions():
    """显示替代解决方案"""
    logger.info("\n" + "="*80)
    logger.info("🔄 替代解决方案")
    logger.info("="*80)
    
    logger.info("\n💡 方案1: 重新创建项目")
    logger.info("1. 在通义听悟控制台删除现有项目")
    logger.info("2. 重新创建新项目")
    logger.info("3. 获取新的AppKey")
    
    logger.info("\n💡 方案2: 使用其他阿里云语音服务")
    logger.info("1. 智能语音交互 (ASR)")
    logger.info("2. 实时语音识别")
    logger.info("3. 一句话识别")
    
    logger.info("\n💡 方案3: 回到原计划")
    logger.info("1. 继续使用火山引擎作为主要服务")
    logger.info("2. 实现基于规则的简单说话人检测")
    logger.info("3. 后续再集成专业的说话人分离服务")

def show_next_steps():
    """显示下一步建议"""
    logger.info("\n" + "="*80)
    logger.info("🎯 建议的下一步")
    logger.info("="*80)
    
    logger.info("\n🔍 立即行动:")
    logger.info("1. 访问通义听悟控制台确认项目状态")
    logger.info("2. 重新获取正确的AppKey")
    logger.info("3. 如果问题持续，考虑联系阿里云技术支持")
    
    logger.info("\n⏰ 时间考虑:")
    logger.info("• 如果配置问题能快速解决 → 继续测试通义听悟")
    logger.info("• 如果配置问题复杂 → 考虑使用其他方案")
    logger.info("• 如果时间紧迫 → 先实现基于规则的说话人检测")
    
    logger.info("\n🎭 对于您的小品视频:")
    logger.info("• 通义听悟确实是很好的选择（如果能正常工作）")
    logger.info("• 但我们也可以先用简单方案解决当前问题")
    logger.info("• 然后再慢慢优化说话人识别功能")

def main():
    """主函数"""
    logger.info("🎯 通义听悟配置检查")
    logger.info("="*60)
    
    # 检查AppKey格式
    check_appkey_format()
    
    # 显示排查步骤
    show_troubleshooting_steps()
    
    # 显示替代方案
    show_alternative_solutions()
    
    # 显示下一步建议
    show_next_steps()
    
    logger.info("\n" + "="*80)
    logger.info("📞 如需帮助:")
    logger.info("• 阿里云工单: https://smartservice.console.aliyun.com/service/create-ticket")
    logger.info("• 通义听悟文档: https://help.aliyun.com/zh/tingwu/")
    logger.info("="*80)

if __name__ == "__main__":
    main()

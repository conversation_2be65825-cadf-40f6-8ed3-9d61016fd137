#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用ffmpeg的音频分割器 - 避免Python 3.13兼容性问题
"""

import json
import os
import subprocess
import tempfile
from pathlib import Path


def check_ffmpeg():
    """检查ffmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False


def get_audio_duration(file_path):
    """获取音频文件时长（秒）"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-show_entries', 
            'format=duration', '-of', 'csv=p=0', file_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            return float(result.stdout.strip())
    except:
        pass
    return 0


def create_file_list(audio_files, output_file):
    """创建ffmpeg的文件列表"""
    with open(output_file, 'w', encoding='utf-8') as f:
        for audio_file in audio_files:
            # 转义文件路径中的特殊字符
            escaped_path = audio_file.replace("'", "'\"'\"'")
            f.write(f"file '{escaped_path}'\n")


def concat_audio_files(audio_files, output_file):
    """使用ffmpeg拼接音频文件"""
    if not audio_files:
        return False
    
    # 创建临时文件列表
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        list_file = f.name
        create_file_list(audio_files, list_file)
    
    try:
        # 使用ffmpeg拼接
        cmd = [
            'ffmpeg', '-f', 'concat', '-safe', '0', 
            '-i', list_file, '-c', 'copy', '-y', output_file
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        success = result.returncode == 0
        
        if not success:
            print(f"ffmpeg错误: {result.stderr}")
        
        return success
        
    finally:
        # 清理临时文件
        try:
            os.unlink(list_file)
        except:
            pass


def split_audio_by_speaker(json_file="real_tool_call_result.json", 
                          output_dir="speaker_audio", 
                          target_duration=60):
    """
    根据说话人分割音频
    
    Args:
        json_file: JSON文件路径
        output_dir: 输出目录
        target_duration: 每个说话人的目标时长（秒）
    """
    # 检查ffmpeg
    if not check_ffmpeg():
        print("错误: 未找到ffmpeg，请先安装ffmpeg")
        print("macOS: brew install ffmpeg")
        print("Ubuntu: sudo apt install ffmpeg")
        return False
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 读取JSON文件
    print(f"读取文件: {json_file}")
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 获取音频片段信息
    audio_segments = data.get('audio_segments', {})
    if not audio_segments:
        print("错误: 未找到audio_segments数据")
        return False
    
    # 按说话人分组
    speaker_groups = {}
    for subtitle in data['subtitles']:
        speaker_id = subtitle['speaker_id']
        if speaker_id not in speaker_groups:
            speaker_groups[speaker_id] = []
        speaker_groups[speaker_id].append(subtitle)
    
    print(f"发现 {len(speaker_groups)} 个说话人")
    print(f"目标时长: {target_duration} 秒")
    print("-" * 50)
    
    # 为每个说话人创建音频
    for speaker_id, subtitles in speaker_groups.items():
        print(f"\n处理说话人 {speaker_id}")
        
        # 按时间排序
        subtitles.sort(key=lambda x: x['start_time'])
        
        # 获取该说话人的音频片段
        speaker_name = subtitles[0]['speaker_name']
        segments = audio_segments.get(speaker_name, {}).get('segments', [])
        
        print(f"  说话人名称: {speaker_name}")
        print(f"  可用片段数: {len(segments)}")
        
        # 收集要拼接的音频文件
        audio_files_to_concat = []
        current_duration = 0
        
        for i, subtitle in enumerate(subtitles):
            if current_duration >= target_duration:
                break
            
            # 查找匹配的音频文件
            audio_file = None
            start_time_sec = subtitle['start_time'] / 1000
            end_time_sec = subtitle['end_time'] / 1000
            
            for segment in segments:
                # 允许0.5秒的时间误差
                if (abs(segment['start_time'] - start_time_sec) < 0.5 and 
                    abs(segment['end_time'] - end_time_sec) < 0.5):
                    audio_file = segment['file_path']
                    break
            
            if not audio_file:
                print(f"    警告: 片段 {i+1} 未找到对应音频文件")
                continue
            
            if not os.path.exists(audio_file):
                print(f"    警告: 音频文件不存在: {audio_file}")
                continue
            
            # 获取音频时长
            duration = get_audio_duration(audio_file)
            if duration == 0:
                print(f"    警告: 无法获取音频时长: {audio_file}")
                continue
            
            # 检查是否会超过目标时长
            if current_duration + duration > target_duration:
                # 如果加上这个片段会超过目标时长，就停止
                break
            
            audio_files_to_concat.append(audio_file)
            current_duration += duration
            
            print(f"    添加片段 {i+1}: {subtitle['text'][:20]}... "
                  f"({duration:.1f}秒)")
        
        # 拼接音频文件
        if audio_files_to_concat:
            output_file = output_path / f"说话人{speaker_id}_{speaker_name}_{current_duration:.1f}秒.wav"
            
            print(f"  正在拼接 {len(audio_files_to_concat)} 个音频片段...")
            
            if concat_audio_files(audio_files_to_concat, str(output_file)):
                print(f"  ✅ 已保存: {output_file} (预计时长: {current_duration:.1f}秒)")
            else:
                print(f"  ❌ 拼接失败: {output_file}")
        else:
            print(f"  警告: 说话人 {speaker_id} 没有有效的音频数据")
    
    print(f"\n完成! 所有音频文件已保存到: {output_path}")
    return True


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="ffmpeg版音频分割器")
    parser.add_argument("-j", "--json", default="real_tool_call_result.json",
                       help="JSON文件路径 (默认: real_tool_call_result.json)")
    parser.add_argument("-o", "--output", default="speaker_audio",
                       help="输出目录 (默认: speaker_audio)")
    parser.add_argument("-d", "--duration", type=int, default=60,
                       help="目标时长（秒，默认: 60）")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.json):
        print(f"错误: JSON文件不存在: {args.json}")
        return
    
    try:
        split_audio_by_speaker(args.json, args.output, args.duration)
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

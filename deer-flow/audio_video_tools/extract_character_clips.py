#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速提取角色特写片段
"""

from video_character_clipper import create_character_clips
import os
import glob

def find_video_file():
    """自动查找视频文件"""
    # 常见视频格式
    video_extensions = ['*.mp4', '*.mov', '*.avi', '*.mkv', '*.m4v', '*.3gp']
    
    # 在当前目录和上级目录查找
    search_paths = ['.', '..', '../..']
    
    for search_path in search_paths:
        for ext in video_extensions:
            pattern = os.path.join(search_path, ext)
            files = glob.glob(pattern)
            if files:
                return os.path.abspath(files[0])
    
    return None

def main():
    """快速提取角色特写片段"""
    print("=" * 60)
    print("视频人物特写切片器 - 为每个关键人物生成特写片段")
    print("=" * 60)
    
    # 检查JSON文件
    json_file = "real_tool_call_result.json"
    if not os.path.exists(json_file):
        print(f"错误: 找不到文件 {json_file}")
        return
    
    # 查找视频文件
    video_file = find_video_file()
    if not video_file:
        print("错误: 未找到视频文件")
        print("请确保视频文件在当前目录或上级目录中")
        print("支持的格式: mp4, mov, avi, mkv, m4v, 3gp")
        
        # 让用户手动输入
        video_path = input("\n请输入视频文件的完整路径: ").strip()
        if video_path and os.path.exists(video_path):
            video_file = video_path
        else:
            print("视频文件不存在，退出程序")
            return
    
    print(f"✅ 找到视频文件: {os.path.basename(video_file)}")
    
    print("\n🎬 处理策略:")
    print("  📹 为每个角色提取5个最佳特写片段")
    print("  🎯 智能识别角色类型（心理医生、患者家属、患者）")
    print("  ⏱️  优选5-15秒的精彩片段")
    print("  🎭 根据角色特征和对话内容评分")
    print("-" * 60)
    
    try:
        success = create_character_clips(
            json_file=json_file,
            input_video=video_file,
            output_dir="character_clips",
            clips_per_character=5
        )
        
        if success:
            print("\n" + "=" * 60)
            print("✅ 角色特写片段提取完成！")
            print("📁 文件保存在: character_clips/")
            print("📂 每个角色都有独立的文件夹")
            print("🎬 每个角色5个精选特写片段")
            print("=" * 60)
            
            # 显示生成的文件
            clips_dir = "character_clips"
            if os.path.exists(clips_dir):
                print(f"\n📂 生成的角色文件夹:")
                for item in os.listdir(clips_dir):
                    item_path = os.path.join(clips_dir, item)
                    if os.path.isdir(item_path):
                        clip_count = len([f for f in os.listdir(item_path) if f.endswith('.mp4')])
                        print(f"  📁 {item} ({clip_count}个片段)")
        else:
            print("\n❌ 特写片段提取失败")
            
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能音频分割器 - 优先选择中短且流畅的句子片段
"""

import json
import os
import subprocess
import tempfile
from pathlib import Path
import re


def check_ffmpeg():
    """检查ffmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False


def score_audio_segment(subtitle):
    """
    为音频片段评分，严格筛选高质量、连贯的句子

    Args:
        subtitle: 字幕数据

    Returns:
        分数（越高越好），返回-1表示应该排除
    """
    text = subtitle['text']
    duration = (subtitle['end_time'] - subtitle['start_time']) / 1000  # 转换为秒

    # 严格过滤条件 - 直接排除的情况

    # 1. 排除过短的片段（小于2秒）
    if duration < 2:
        return -1

    # 2. 排除过长的片段（超过20秒，可能包含多人对话）
    if duration > 20:
        return -1

    # 3. 排除文本过短的片段（少于3个字）
    if len(text) < 3:
        return -1

    # 4. 排除明显的噪音或无意义内容
    noise_patterns = [
        r'^[呃嗯啊哎哦唉]{2,}',  # 开头是连续停顿词
        r'[呃嗯啊哎哦唉]{3,}',   # 包含3个以上连续停顿词
        r'^[。，！？\s]+$',      # 只有标点符号
        r'咋的了',               # 重复的口头禅
        r'你说这事',             # 过于简短的插话
    ]

    for pattern in noise_patterns:
        if re.search(pattern, text):
            return -1

    # 5. 排除可能包含其他人声音的片段（通过关键词判断）
    multi_speaker_indicators = [
        r'大夫.*大夫',           # 同时提到多次大夫（可能是对话）
        r'你.*我.*你',           # 频繁的你我转换
        r'他.*她.*他',           # 提到第三人称
    ]

    for pattern in multi_speaker_indicators:
        if re.search(pattern, text):
            return -1

    # 开始正式评分
    score = 0

    # 1. 时长评分 (4-12秒为最佳，更严格)
    if 4 <= duration <= 12:
        score += 100
    elif 2 <= duration < 4:
        score += 70
    elif 12 < duration <= 16:
        score += 50
    else:
        score += 20

    # 2. 文本长度评分 (8-40字为最佳，更严格)
    text_length = len(text)
    if 8 <= text_length <= 40:
        score += 80
    elif 4 <= text_length < 8:
        score += 50
    elif 40 < text_length <= 60:
        score += 30
    else:
        score += 10

    # 3. 句子完整性评分（更严格）
    if re.search(r'[。！？]$', text):  # 以句号等结尾
        score += 60
    elif re.search(r'[，]', text) and not text.endswith('，'):  # 包含逗号但不以逗号结尾
        score += 40
    elif text.endswith('。') or text.endswith('！') or text.endswith('？'):
        score += 50

    # 4. 语音纯净度评分（更严格）
    hesitation_words = ['呃', '嗯', '啊', '哎', '哦', '唉', '额']
    hesitation_count = sum(1 for word in hesitation_words if word in text)
    if hesitation_count == 0:
        score += 50
    elif hesitation_count == 1:
        score += 20
    else:
        score -= hesitation_count * 15  # 更严厉的扣分

    # 5. 内容连贯性评分
    # 检查是否是完整的表达
    coherent_patterns = [
        r'^[^，。！？]*[。！？]$',  # 完整句子
        r'大夫.*[。！？]',         # 对大夫说的完整话
        r'老头.*[。！？]',         # 对老头说的完整话
        r'我.*[。！？]',           # 以我开头的完整表达
    ]

    for pattern in coherent_patterns:
        if re.search(pattern, text):
            score += 30
            break

    # 6. 女性角色特征评分（患者家属的特点）
    female_role_patterns = [
        r'大夫', r'医生', r'看病', r'治疗',
        r'老头', r'媳妇', r'家里', r'病情',
        r'钱', r'彩票', r'中奖', r'告诉'
    ]

    pattern_count = sum(1 for pattern in female_role_patterns if re.search(pattern, text))
    score += pattern_count * 15

    # 7. 语调自然度评分
    if '？' in text:  # 疑问句通常更自然
        score += 20
    if '！' in text:  # 感叹句表达情感
        score += 15

    return score


def select_best_segments(subtitles, target_duration=12):
    """
    严格选择最佳音频片段，优先质量而非数量

    Args:
        subtitles: 说话人的所有字幕列表
        target_duration: 目标总时长（秒），默认12秒

    Returns:
        选中的字幕列表
    """
    # 为每个片段评分，过滤掉不合格的片段
    scored_segments = []
    filtered_count = 0

    for subtitle in subtitles:
        score = score_audio_segment(subtitle)
        if score == -1:  # 被过滤的片段
            filtered_count += 1
            continue

        duration = (subtitle['end_time'] - subtitle['start_time']) / 1000
        scored_segments.append({
            'subtitle': subtitle,
            'score': score,
            'duration': duration
        })

    print(f"  过滤掉 {filtered_count} 个低质量片段")
    print(f"  剩余 {len(scored_segments)} 个候选片段")

    # 按分数排序，只选择高分片段
    scored_segments.sort(key=lambda x: x['score'], reverse=True)

    # 更严格的选择策略
    selected_segments = []
    total_duration = 0
    min_score_threshold = 200  # 最低分数门槛

    # 首先选择最高分的片段
    for segment in scored_segments:
        if segment['score'] < min_score_threshold:
            break  # 分数太低，停止选择

        if total_duration + segment['duration'] <= target_duration:
            selected_segments.append(segment['subtitle'])
            total_duration += segment['duration']
        elif total_duration >= target_duration * 0.7:  # 已经达到70%就够了
            break

    # 如果选择的片段太少，适当降低门槛
    if len(selected_segments) < 3 and total_duration < target_duration * 0.5:
        min_score_threshold = 150
        for segment in scored_segments:
            if segment['score'] < min_score_threshold:
                continue
            if segment['subtitle'] in [s for s in selected_segments]:
                continue  # 已经选择过的跳过

            if total_duration + segment['duration'] <= target_duration:
                selected_segments.append(segment['subtitle'])
                total_duration += segment['duration']

            if len(selected_segments) >= 5:  # 最多5个片段
                break

    # 按时间顺序重新排序
    selected_segments.sort(key=lambda x: x['start_time'])

    return selected_segments, total_duration


def create_file_list(audio_files, output_file):
    """创建ffmpeg的文件列表"""
    with open(output_file, 'w', encoding='utf-8') as f:
        for audio_file in audio_files:
            escaped_path = audio_file.replace("'", "'\"'\"'")
            f.write(f"file '{escaped_path}'\n")


def concat_audio_files(audio_files, output_file):
    """使用ffmpeg拼接音频文件"""
    if not audio_files:
        return False
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        list_file = f.name
        create_file_list(audio_files, list_file)
    
    try:
        cmd = [
            'ffmpeg', '-f', 'concat', '-safe', '0', 
            '-i', list_file, '-c', 'copy', '-y', output_file
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        success = result.returncode == 0
        
        if not success:
            print(f"ffmpeg错误: {result.stderr}")
        
        return success
        
    finally:
        try:
            os.unlink(list_file)
        except:
            pass


def smart_split_speaker_audio(speaker_id, json_file="real_tool_call_result.json",
                             output_dir="smart_audio", target_duration=12):
    """
    智能分割指定说话人的音频
    """
    if not check_ffmpeg():
        print("错误: 未找到ffmpeg")
        return False
    
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    print(f"读取文件: {json_file}")
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 获取指定说话人的字幕
    speaker_subtitles = [s for s in data['subtitles'] if s['speaker_id'] == speaker_id]
    if not speaker_subtitles:
        print(f"错误: 未找到说话人 {speaker_id} 的数据")
        return False
    
    speaker_name = speaker_subtitles[0]['speaker_name']
    audio_segments = data.get('audio_segments', {})
    segments = audio_segments.get(speaker_name, {}).get('segments', [])
    
    print(f"处理说话人 {speaker_id} ({speaker_name})")
    print(f"总片段数: {len(speaker_subtitles)}")
    print(f"目标时长: {target_duration} 秒")
    print("-" * 50)
    
    # 智能选择最佳片段
    selected_subtitles, estimated_duration = select_best_segments(speaker_subtitles, target_duration)
    
    print(f"智能选择了 {len(selected_subtitles)} 个高质量片段")
    print(f"预计总时长: {estimated_duration:.1f} 秒")
    print("-" * 30)
    
    # 收集对应的音频文件
    audio_files_to_concat = []
    actual_duration = 0
    
    for i, subtitle in enumerate(selected_subtitles):
        # 查找匹配的音频文件
        audio_file = None
        start_time_sec = subtitle['start_time'] / 1000
        end_time_sec = subtitle['end_time'] / 1000
        
        for segment in segments:
            if (abs(segment['start_time'] - start_time_sec) < 0.5 and 
                abs(segment['end_time'] - end_time_sec) < 0.5):
                audio_file = segment['file_path']
                break
        
        if not audio_file or not os.path.exists(audio_file):
            print(f"  跳过片段 {i+1}: 音频文件不存在")
            continue
        
        duration = (subtitle['end_time'] - subtitle['start_time']) / 1000
        audio_files_to_concat.append(audio_file)
        actual_duration += duration
        
        # 显示选中的片段信息
        score = score_audio_segment(subtitle)
        print(f"  ✅ 片段 {i+1} (评分:{score:.0f}): {subtitle['text'][:40]}... ({duration:.1f}秒)")
    
    if not audio_files_to_concat:
        print("错误: 没有找到有效的音频文件")
        return False
    
    # 拼接音频文件
    output_file = output_path / f"说话人{speaker_id}_{speaker_name}_智能版_{actual_duration:.1f}秒.wav"
    
    print(f"\n正在拼接 {len(audio_files_to_concat)} 个音频片段...")
    
    if concat_audio_files(audio_files_to_concat, str(output_file)):
        print(f"✅ 智能拼接完成: {output_file}")
        print(f"📊 实际时长: {actual_duration:.1f}秒")
        return True
    else:
        print(f"❌ 拼接失败")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="智能音频分割器")
    parser.add_argument("speaker_id", help="说话人ID")
    parser.add_argument("-j", "--json", default="real_tool_call_result.json",
                       help="JSON文件路径")
    parser.add_argument("-o", "--output", default="smart_audio",
                       help="输出目录")
    parser.add_argument("-d", "--duration", type=int, default=12,
                       help="目标时长（秒）")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.json):
        print(f"错误: JSON文件不存在: {args.json}")
        return
    
    smart_split_speaker_audio(
        args.speaker_id, args.json, args.output, args.duration
    )


if __name__ == "__main__":
    main()

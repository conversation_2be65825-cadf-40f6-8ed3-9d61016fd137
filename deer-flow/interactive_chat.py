import asyncio
import uuid
import re
import json
import time
import argparse
from typing import Dict, Any, Optional, List
from datetime import datetime

from langchain_core.messages import BaseMessage
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from rich.table import Table
# 移除未使用的导入

from src.graph_v2.builder import GraphBuilder
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver

# Setup rich console for better output
console = Console()

# 初始化模板系统
from src.templates.builtin_templates import load_builtin_templates
load_builtin_templates()
console.print("[dim]🎯 模板系统已初始化[/dim]")

class WorkflowVisualizer:
    """透明化AI工作流可视化器"""

    def __init__(self):
        self.current_step = 0
        self.total_steps = 0
        self.plan_steps = []
        self.execution_log = []
        self.start_time = None

    def reset(self):
        """重置状态"""
        self.current_step = 0
        self.total_steps = 0
        self.plan_steps = []
        self.execution_log = []
        self.start_time = time.time()

    def set_plan(self, plan):
        """设置执行计划"""
        if plan and hasattr(plan, 'steps'):
            self.plan_steps = plan.steps
            self.total_steps = len(plan.steps)

    def log_event(self, event_type: str, content: str, details: Optional[Dict] = None):
        """记录执行事件"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.execution_log.append({
            'timestamp': timestamp,
            'type': event_type,
            'content': content,
            'details': details or {}
        })

    def show_thinking_process(self, thought: str, reasoning: Optional[str] = None):
        """显示AI思考过程"""
        console.print()
        console.print(Panel(
            f"[italic dim]💭 思考过程:[/italic dim]\n\n{thought}" +
            (f"\n\n[italic dim]🤔 推理逻辑:[/italic dim]\n{reasoning}" if reasoning else ""),
            title="[bold cyan]🧠 AI正在思考...[/bold cyan]",
            border_style="cyan",
            expand=False
        ))
        self.log_event("thinking", thought, {"reasoning": reasoning})

    def show_task_analysis(self, task: str, complexity: str, decision: str):
        """显示任务分析结果"""
        complexity_color = {
            "简单": "green",
            "复杂": "yellow",
            "高复杂": "red"
        }.get(complexity, "white")

        console.print(Panel(
            f"[bold]📝 任务:[/bold] {task}\n\n"
            f"[bold]🎯 复杂度评估:[/bold] [{complexity_color}]{complexity}[/{complexity_color}]\n\n"
            f"[bold]🚀 执行策略:[/bold] {decision}",
            title="[bold blue]📊 任务分析[/bold blue]",
            border_style="blue",
            expand=False
        ))
        self.log_event("analysis", f"任务复杂度: {complexity}, 策略: {decision}")

    def show_plan_creation(self, plan):
        """显示计划创建过程"""
        if not plan or not hasattr(plan, 'steps'):
            return

        self.set_plan(plan)

        # 检查是否是模板生成的计划
        is_template_plan = getattr(plan, 'is_from_template', False)
        source_template = getattr(plan, 'source_template', None)

        # 创建计划表格标题
        if is_template_plan and source_template:
            table_title = f"[bold blue]📋 基于 '{source_template}' 模板的执行计划[/bold blue]"
        else:
            table_title = "[bold blue]📋 AI生成的执行计划[/bold blue]"

        plan_table = Table(
            title=table_title,
            show_header=True,
            header_style="bold magenta",
            border_style="blue"
        )
        plan_table.add_column("步骤", style="cyan", width=6, justify="center")
        plan_table.add_column("描述", style="white", min_width=30)
        plan_table.add_column("工具", style="green", width=15)
        plan_table.add_column("状态", style="yellow", width=12, justify="center")
        plan_table.add_column("依赖", style="dim", width=10, justify="center")

        for i, step in enumerate(plan.steps, 1):
            # 支持新的EnhancedStep格式
            step_id = getattr(step, 'step_id', i)
            step_desc = getattr(step, 'description', getattr(step, 'name', '无描述'))
            step_tool = getattr(step, 'tool_to_use', '未指定')
            step_status = getattr(step, 'status', 'pending')
            dependencies = getattr(step, 'dependencies', [])

            # 状态样式
            status_display = {
                'pending': '[yellow]⏳ 待执行[/yellow]',
                'in_progress': '[blue]🔄 执行中[/blue]',
                'completed': '[green]✅ 已完成[/green]',
                'failed': '[red]❌ 失败[/red]',
                'skipped': '[dim]⏭️ 跳过[/dim]'
            }.get(step_status, step_status)

            deps_str = ", ".join(map(str, dependencies)) if dependencies else "-"

            plan_table.add_row(
                f"#{i}",
                step_desc,
                step_tool,
                status_display,
                deps_str
            )

        console.print(plan_table)

        # 显示模板相关信息
        if is_template_plan and source_template:
            console.print(f"[dim]🎯 使用模板: {source_template} | 包含 {len(plan.steps)} 个预定义步骤[/dim]")
        else:
            console.print(f"[dim]✨ 自定义计划包含 {len(plan.steps)} 个步骤，准备开始执行...[/dim]")

        self.log_event("plan_created", f"生成了包含{len(plan.steps)}个步骤的计划")

    def show_step_execution(self, step_info: Dict, step_number: int):
        """显示步骤执行"""
        self.current_step = step_number

        step_desc = step_info.get('description', '执行步骤')
        step_tool = step_info.get('tool_to_use', '未知工具')

        # 进度条
        progress_text = f"步骤 {step_number}/{self.total_steps}" if self.total_steps > 0 else f"步骤 {step_number}"

        console.print()
        console.print(Panel(
            f"[bold]📝 任务描述:[/bold] {step_desc}\n\n"
            f"[bold]🔧 使用工具:[/bold] {step_tool}\n\n"
            f"[bold]📊 进度:[/bold] {progress_text}",
            title=f"[bold blue]🚀 执行步骤 #{step_number}[/bold blue]",
            border_style="blue",
            expand=False
        ))
        self.log_event("step_start", f"开始执行步骤{step_number}: {step_desc}")

    def show_tool_call(self, tool_name: str, tool_args: Dict, context: Optional[str] = None):
        """显示工具调用详情"""
        # 根据工具类型使用不同的图标和颜色
        tool_icons = {
            'visual_expert': '🎨',
            'audio_expert': '🎵',
            'video_expert': '🎬',
            'planner_tool': '📋',
            'get_current_plan': '📖',
            'update_step_status': '📝',
            'resolve_step_inputs': '🔗'
        }

        tool_colors = {
            'visual_expert': 'magenta',
            'audio_expert': 'blue',
            'video_expert': 'red',
            'planner_tool': 'green',
            'get_current_plan': 'cyan',
            'update_step_status': 'yellow',
            'resolve_step_inputs': 'white'
        }

        icon = tool_icons.get(tool_name, '🔧')
        color = tool_colors.get(tool_name, 'magenta')

        # 构建参数显示
        params_text = ""
        if tool_name in ['visual_expert', 'audio_expert', 'video_expert']:
            task_desc = tool_args.get('task_description', '未提供')
            context_info = tool_args.get('context', '无')
            step_inputs = tool_args.get('step_inputs', {})

            params_text = f"[bold]任务描述:[/bold] {task_desc}\n\n"
            if context_info and context_info != '无':
                params_text += f"[bold]上下文:[/bold] {context_info}\n\n"
            if step_inputs:
                params_text += f"[bold]输入参数:[/bold]\n{json.dumps(step_inputs, indent=2, ensure_ascii=False)}"
        else:
            params_text = f"[bold]参数:[/bold]\n{json.dumps(tool_args, indent=2, ensure_ascii=False)}"

        console.print(Panel(
            params_text,
            title=f"[bold {color}]{icon} 调用工具: {tool_name}[/bold {color}]",
            border_style=color,
            expand=False
        ))

        if context:
            console.print(f"[dim]💡 {context}[/dim]")

        self.log_event("tool_call", f"调用{tool_name}", {"args": tool_args})

    def show_tool_progress(self, tool_name: str, message: str = "处理中..."):
        """显示工具执行进度"""
        tool_icons = {
            'visual_expert': '🎨',
            'audio_expert': '🎵',
            'video_expert': '🎬'
        }

        icon = tool_icons.get(tool_name, '⚡')

        with console.status(f"[bold blue]{icon} {tool_name} {message}[/bold blue]", spinner="dots"):
            # 这里可以添加实际的进度跟踪逻辑
            time.sleep(0.5)  # 模拟处理时间

    def show_tool_result(self, tool_name: str, result: Dict, success: bool = True):
        """显示工具执行结果"""
        tool_icons = {
            'visual_expert': '🎨',
            'audio_expert': '🎵',
            'video_expert': '🎬',
            'planner_tool': '📋',
            'get_current_plan': '📖',
            'update_step_status': '📝'
        }

        icon = tool_icons.get(tool_name, '🔧')

        if success:
            content = result.get('content', '执行成功')
            assets = result.get('assets', {})
            metadata = result.get('metadata', {})

            # 构建结果显示
            result_text = f"[bold green]✅ 执行成功:[/bold green] {content}"

            # 显示生成的资产
            if assets:
                result_text += f"\n\n[bold]📁 生成的资产:[/bold]"

                # 处理图片资产
                if 'images' in assets and assets['images']:
                    result_text += "\n\n[bold]🖼️ 图片:[/bold]"
                    for img in assets['images']:
                        if isinstance(img, dict):
                            img_name = img.get('name', '未命名图片')
                            img_desc = img.get('description', '无描述')
                            img_url = img.get('url', '')

                            result_text += f"\n  • [bold]{img_name}[/bold]"
                            result_text += f"\n    [dim]📝 {img_desc}[/dim]"
                            if img_url:
                                result_text += f"\n    [link={img_url}]🔗 查看图片[/link]"
                        else:
                            result_text += f"\n  • {img}"

                # 处理音频资产
                if 'audio' in assets and assets['audio']:
                    result_text += "\n\n[bold]🔊 音频:[/bold]"
                    for audio in assets['audio']:
                        if isinstance(audio, dict):
                            audio_name = audio.get('name', '未命名音频')
                            audio_desc = audio.get('description', '无描述')
                            audio_url = audio.get('url', '')
                            duration = audio.get('duration', '')

                            result_text += f"\n  • [bold]{audio_name}[/bold]"
                            result_text += f"\n    [dim]📝 {audio_desc}[/dim]"
                            if duration:
                                result_text += f"\n    [dim]⏱️ 时长: {duration}[/dim]"
                            if audio_url:
                                result_text += f"\n    [link={audio_url}]🔗 播放音频[/link]"
                        else:
                            result_text += f"\n  • {audio}"

                # 处理视频资产
                if 'video' in assets and assets['video']:
                    result_text += "\n\n[bold]🎬 视频:[/bold]"
                    for video in assets['video']:
                        if isinstance(video, dict):
                            video_name = video.get('name', '未命名视频')
                            video_desc = video.get('description', '无描述')
                            video_url = video.get('url', '')
                            duration = video.get('duration', '')
                            resolution = video.get('resolution', '')

                            result_text += f"\n  • [bold]{video_name}[/bold]"
                            result_text += f"\n    [dim]📝 {video_desc}[/dim]"
                            if duration:
                                result_text += f"\n    [dim]⏱️ 时长: {duration}[/dim]"
                            if resolution:
                                result_text += f"\n    [dim]📐 分辨率: {resolution}[/dim]"
                            if video_url:
                                result_text += f"\n    [link={video_url}]🔗 观看视频[/link]"
                        else:
                            result_text += f"\n  • {video}"

            # 显示关键元数据
            if metadata:
                important_keys = ['style', 'duration', 'resolution', 'format', 'model_used']
                important_metadata = {k: v for k, v in metadata.items() if k in important_keys}
                if important_metadata:
                    result_text += f"\n\n[bold dim]ℹ️ 详细信息:[/bold dim]"
                    for key, value in important_metadata.items():
                        result_text += f"\n  • [dim]{key}: {value}[/dim]"

            console.print(Panel(
                result_text,
                title=f"[bold green]{icon} {tool_name} 执行完成[/bold green]",
                border_style="green",
                expand=False
            ))

            self.log_event("tool_success", f"{tool_name}执行成功", {"result": result})

        else:
            error_msg = result.get('error', '未知错误')
            console.print(Panel(
                f"[bold red]❌ 执行失败:[/bold red] {error_msg}",
                title=f"[bold red]{icon} {tool_name} 执行失败[/bold red]",
                border_style="red",
                expand=False
            ))

            self.log_event("tool_error", f"{tool_name}执行失败: {error_msg}")

    def show_step_completion(self, step_number: int, success: bool = True):
        """显示步骤完成"""
        if success:
            console.print(f"[bold green]✅ 步骤 #{step_number} 完成![/bold green]")
            self.log_event("step_complete", f"步骤{step_number}成功完成")
        else:
            console.print(f"[bold red]❌ 步骤 #{step_number} 失败![/bold red]")
            self.log_event("step_failed", f"步骤{step_number}执行失败")

    def show_final_summary(self, final_message: str, execution_time: Optional[float] = None):
        """显示最终总结"""
        # 计算执行时间
        if self.start_time and not execution_time:
            execution_time = time.time() - self.start_time

        # 提取图片链接
        image_links = re.findall(r'!\[(.*?)\]\((https?://[^\s\)]+)\)', final_message)

        # 显示最终结果
        console.print()
        console.print(Panel(
            final_message,
            title="[bold green]🎉 任务完成 - 最终结果[/bold green]",
            border_style="green",
            expand=False
        ))

        # 显示图片预览
        if image_links:
            console.print("\n[bold yellow]📸 生成的图片:[/bold yellow]")
            for img_alt, img_url in image_links:
                console.print(f"  • [link={img_url}]{img_alt or '查看图片'}[/link]")

        # 显示执行统计
        stats_text = f"[bold]✅ 任务成功完成[/bold]\n"
        if self.total_steps > 0:
            stats_text += f"[dim]• 执行步骤: {self.total_steps} 个[/dim]\n"
        if execution_time:
            stats_text += f"[dim]• 执行时间: {execution_time:.1f} 秒[/dim]\n"
        stats_text += f"[dim]• 事件记录: {len(self.execution_log)} 条[/dim]"

        console.print(Panel(
            stats_text,
            title="[bold blue]📊 执行统计[/bold blue]",
            border_style="blue",
            expand=False
        ))

        self.log_event("task_complete", "任务完成", {
            "execution_time": execution_time,
            "total_steps": self.total_steps,
            "total_events": len(self.execution_log)
        })

# 全局可视化器实例
visualizer = WorkflowVisualizer()

def print_stream_chunk(chunk: Dict[str, Any], metadata: Optional[Dict] = None):
    """
    基于LangGraph最佳实践的流式输出显示函数
    参考: https://github.com/langchain-ai/langgraph/blob/main/docs/docs/how-tos/streaming.md
    """
    # 处理不同类型的chunk
    for node_name, node_output in chunk.items():
        # 简化：不显示节点执行信息，直接处理消息

        # 获取消息
        messages = node_output.get("messages", [])
        if not messages:
            continue

        # 🔥 优化：只处理新的重要消息，避免冗余显示
        previous_count = getattr(print_stream_chunk, '_last_message_count', {}).get(node_name, 0)
        new_messages = messages[previous_count:]

        # 更新消息计数
        if not hasattr(print_stream_chunk, '_last_message_count'):
            print_stream_chunk._last_message_count = {}
        print_stream_chunk._last_message_count[node_name] = len(messages)

        # 只显示最重要的新消息（AI工具调用和工具结果）
        important_messages = []
        for msg in new_messages:
            if msg.type == "ai" and hasattr(msg, 'tool_calls') and msg.tool_calls:
                important_messages.append(msg)
            elif msg.type == "tool":
                important_messages.append(msg)
            elif msg.type == "ai" and not hasattr(msg, 'tool_calls'):
                # 最终AI回复
                important_messages.append(msg)

        # 处理重要消息
        for message in important_messages:
            if message.type == "ai":
                _handle_ai_message(message, node_name)
            elif message.type == "tool":
                _handle_tool_message(message, node_name)

def _handle_ai_message(message: BaseMessage, node_name: str):
    """处理AI消息的显示 - 简化版本"""
    if message.tool_calls:
        # 简洁显示思考过程
        if hasattr(message, 'content') and message.content and message.content.strip():
            console.print(f"[cyan]💭 {message.content}[/cyan]")

        # 简洁显示工具调用
        for tool_call in message.tool_calls:
            tool_name = tool_call['name']
            tool_args = tool_call['args']

            # 工具图标映射
            tool_icons = {
                'visual_expert': '🎨',
                'audio_expert': '🎵',
                'video_expert': '🎬',
                'planner_tool': '📋'
            }
            icon = tool_icons.get(tool_name, '🔧')

            console.print(f"{icon} [bold]调用 {tool_name}[/bold]")

            # 只显示关键参数
            if tool_name in ['visual_expert', 'audio_expert', 'video_expert']:
                task_desc = tool_args.get('task_description', '执行任务')
                console.print(f"   [dim]任务: {task_desc}[/dim]")
            elif tool_name == 'planner_tool':
                task = tool_args.get('task', '制定计划')
                console.print(f"   [dim]规划: {task}[/dim]")
    else:
        # 最终回复 - 使用可视化器显示
        visualizer.show_final_summary(message.content)

def _handle_tool_message(message: BaseMessage, node_name: str):
    """处理工具消息的显示 - 简化版本"""
    try:
        tool_output = json.loads(message.content)
        success = tool_output.get('success', False)

        # 工具图标映射
        tool_icons = {
            'visual_expert': '🎨',
            'audio_expert': '🎵',
            'video_expert': '🎬',
            'planner_tool': '📋'
        }
        icon = tool_icons.get(message.name, '🔧')
        status_icon = "✅" if success else "❌"

        console.print(f"{status_icon} {icon} [bold]{message.name}[/bold] 完成")

        # 只显示关键结果信息
        if success:
            content = tool_output.get('content', '')
            if content:
                # 截取内容，避免过长
                short_content = content[:80] + "..." if len(content) > 80 else content
                console.print(f"   [dim]{short_content}[/dim]")

            # 显示生成的资产数量
            assets = tool_output.get('assets', {})
            if assets:
                for asset_type, asset_list in assets.items():
                    if asset_list:
                        console.print(f"   [green]生成了 {len(asset_list)} 个{asset_type}[/green]")
        else:
            error = tool_output.get('error', '执行失败')
            console.print(f"   [red]错误: {error}[/red]")

    except json.JSONDecodeError:
        console.print(f"[yellow]📤 {message.name} 返回非结构化数据[/yellow]")

# 移除不再使用的辅助函数，简化代码结构

async def normal_chat_mode(initial_plan: Optional['UnifiedPlan'] = None,
                          preload_template_id: Optional[str] = None,
                          preload_params: Optional[Dict[str, Any]] = None):
    """普通对话模式 - 运行透明化AI工作流交互聊天"""

    # 如果有预加载的模板，设置全局状态
    if preload_template_id:
        from src.graph_v2.template_planning import global_planning_state
        from src.tools.template_tools import _template_registry

        template = _template_registry.get(preload_template_id)
        if template:
            # 🚀 立即生成预制计划结构（使用默认参数）
            from src.graph_v2.template_renderer import template_renderer
            try:
                # 使用默认参数生成预制计划
                default_params = {}
                for param_name, param_schema in template.parameters.items():
                    if param_schema.default is not None:
                        default_params[param_name] = param_schema.default
                    elif param_schema.required:
                        # 对于必需参数，使用占位符
                        default_params[param_name] = f"<{param_name}>"

                preview_plan = template_renderer.render_plan(template, default_params)

                # 构建计划预览
                plan_preview = "\n".join([f"{i+1}. {step.name}" for i, step in enumerate(preview_plan.steps)])

                # 构建参数列表
                required_params = []
                optional_params = []
                for param_name, param_schema in template.parameters.items():
                    param_desc = f"**{param_name}**: {param_schema.description}"
                    if param_schema.required:
                        required_params.append(param_desc)
                    else:
                        optional_params.append(f"{param_desc} (默认: {param_schema.default})")

                params_info = ""
                if required_params:
                    params_info += f"[red]必需参数[/red]:\n" + "\n".join(required_params)
                if optional_params:
                    if params_info:
                        params_info += "\n\n"
                    params_info += f"[dim]可选参数[/dim]:\n" + "\n".join(optional_params)

                console.print(Panel(
                    f"[bold green]✅ 已预加载模板: {template.name}[/bold green]\n\n"
                    f"[dim]{template.description}[/dim]\n\n"
                    f"[bold cyan]📋 预制执行计划：[/bold cyan]\n{plan_preview}\n\n"
                    f"[bold yellow]📝 需要的参数：[/bold yellow]\n{params_info}\n\n"
                    f"[green]💡 请提供参数，我将立即开始执行计划！[/green]",
                    title="🎯 模板模式 - 预制计划已就绪",
                    border_style="green"
                ))

                # 🚀 立即将预制计划保存到全局状态
                import src.tools.state_management as sm
                sm._current_state_plan = preview_plan

                # 🚀 重要：设置全局状态，让主agent知道当前处于模板模式
                global_planning_state.enter_template_mode(preload_template_id, default_params)

            except Exception as e:
                console.print(f"[red]❌ 生成预制计划失败: {e}[/red]")
                # 降级到原来的显示方式
                console.print(Panel(
                    f"[bold green]✅ 已预加载模板: {template.name}[/bold green]\n\n"
                    f"[dim]{template.description}[/dim]\n\n"
                    f"[yellow]现在您可以与AI自然对话，描述您的具体需求。[/yellow]\n"
                    f"[yellow]AI会智能收集参数并生成执行计划。[/yellow]",
                    title="🎯 模板模式",
                    border_style="green"
                ))

            # 🚀 如果没有在上面设置，这里作为备用
            if not global_planning_state.planning_mode:
                global_planning_state.enter_template_mode(preload_template_id, preload_params or {})
        else:
            console.print(f"[red]❌ 模板 '{preload_template_id}' 不存在[/red]")
            return

    # 显示欢迎界面
    console.print(Panel(
        "[bold]🎉 欢迎使用 Deer-Flow 透明AI工作流！[/bold]\n\n"
        "🎯 [bold yellow]完全透明化体验[/bold yellow] - 像看真实项目经理工作一样观察AI！\n\n"
        "[green]✨ 你将实时看到:[/green]\n"
        "• 🧠 AI的完整思考过程和决策逻辑\n"
        "• 📊 智能任务分析和复杂度评估\n"
        "• 📋 自动生成的分步执行计划\n"
        "• 🔧 每个工具调用的详细参数\n"
        "• 📁 实时生成的资产和中间结果\n"
        "• 📈 执行进度和状态更新\n\n"
        "[yellow]💡 试试这些任务:[/yellow]\n"
        "• [dim]简单任务:[/dim] '画一只可爱的小猫咪'\n"
        "• [dim]复杂任务:[/dim] '做一个哪吒送外卖的搞笑短片'\n"
        "• [dim]系列任务:[/dim] '为北京、上海、深圳设计城市主题海报'\n"
        "• [dim]跨领域:[/dim] '制作一个产品宣传视频，包含logo、配音和动画'\n\n"
        "[cyan]🎮 可用命令:[/cyan]\n"
        "• [bold]exit/quit[/bold] - 退出程序\n"
        "• [bold]/help[/bold] - 显示详细帮助\n"
        "• [bold]/stream updates[/bold] - 透明工作流模式（默认推荐）\n"
        "• [bold]/stream messages[/bold] - LLM流式输出模式",
        title="[bold cyan]🦌 DeerFlow V2 - 透明AI工作流体验[/bold cyan]",
        border_style="cyan",
        expand=False
    ))

    # Ensure the db directory exists
    import os
    os.makedirs("db", exist_ok=True)

    async with AsyncSqliteSaver.from_conn_string("db/checkpoints.sqlite") as memory:
        # 1. Create the workflow graph using the builder
        builder = GraphBuilder()
        graph = builder.build(checkpointer=memory)

        # 2. Let the user decide to start a new chat or continue an existing one
        choice = console.input("Start a new conversation ([bold green]N[/bold green]) or continue from a thread_id ([bold yellow]C[/bold yellow])? [N/C]: ").strip().upper()
        if choice == 'C':
            thread_id = console.input("Please enter the thread_id: ").strip()
            if not thread_id:
                console.print("[bold red]Error: thread_id cannot be empty.[/bold red]")
                return
        else:
            thread_id = str(uuid.uuid4())
        
        console.print(f"Using Session ID: [dim]{thread_id}[/dim]")

        # 3. Set up the configuration for the graph to use this thread
        config = {"configurable": {"thread_id": thread_id}}

        # Stream mode setting
        stream_mode = "updates"  # Default mode

        # 4. Start the interactive loop
        while True:
            try:
                # Get user input using a thread to avoid blocking asyncio loop
                user_input = await asyncio.to_thread(console.input, "[bold green]>>> [/bold green]")

                if user_input.lower() in ["quit", "exit"]:
                    break

                if not user_input.strip():
                    continue

                # Handle commands
                if user_input.startswith('/'):
                    if user_input == '/help':
                        console.print(Panel(
                            "可用命令:\n"
                            "• /stream updates - 步骤更新模式（默认）\n"
                            "• /stream messages - LLM流式输出模式\n"
                            "• /help - 显示此帮助\n"
                            "• quit/exit - 退出\n\n"
                            f"当前模式: {stream_mode}",
                            title="[yellow]帮助[/yellow]",
                            border_style="yellow"
                        ))
                        continue
                    elif user_input.startswith('/stream '):
                        new_mode = user_input[8:].strip()
                        if new_mode in ['updates', 'messages']:
                            stream_mode = new_mode
                            console.print(f"[green]✅ 切换到 {new_mode} 模式[/green]")
                        else:
                            console.print("[red]❌ 无效模式。可用: updates, messages[/red]")
                        continue
                    else:
                        console.print("[red]❌ 未知命令。输入 /help 查看帮助。[/red]")
                        continue

                # 准备输入并重置可视化器
                inputs = {"messages": [("human", user_input)]}
                visualizer.reset()

                # 🔥 重要：重置消息计数器，确保能捕获所有新消息
                if hasattr(print_stream_chunk, '_last_message_count'):
                    print_stream_chunk._last_message_count.clear()

                # 显示任务开始
                console.rule(f"[bold blue]🚀 开始处理任务 ({stream_mode} 模式)[/bold blue]")
                console.print(f"[bold]📝 用户任务:[/bold] {user_input}")

                final_event = None
                last_node_name = None
                step_count = 0
                current_step_number = 0

                if stream_mode == "updates":
                    # 透明工作流模式 - 逐步显示增强的透明度
                    plan_displayed = False

                    async for event in graph.astream(inputs, config=config, stream_mode="updates"):
                        step_count += 1
                        node_name, node_output = next(iter(event.items()))

                        # 检查是否有新的计划生成
                        plan = node_output.get("plan")
                        if plan and hasattr(plan, 'steps') and not plan_displayed:
                            plan_displayed = True
                            visualizer.show_plan_creation(plan)

                        # 使用新的流式显示函数
                        print_stream_chunk({node_name: node_output})

                        # 检测步骤执行（用于进度跟踪）
                        messages = node_output.get("messages", [])
                        if messages:
                            last_message = messages[-1]

                            # 检测专家工具调用（表示新步骤开始）
                            if (hasattr(last_message, 'tool_calls') and last_message.tool_calls):
                                for tool_call in last_message.tool_calls:
                                    tool_name = tool_call['name']
                                    if tool_name in ['visual_expert', 'audio_expert', 'video_expert']:
                                        current_step_number += 1
                                        step_info = {
                                            'description': tool_call['args'].get('task_description', '执行任务'),
                                            'tool_to_use': tool_name
                                        }
                                        visualizer.show_step_execution(step_info, current_step_number)
                                        break

                        final_event = event
                        last_node_name = node_name

                elif stream_mode == "messages":
                    # Messages mode - 基于LangGraph最佳实践的LLM token流式显示
                    console.print("[bold yellow]🔄 启动LLM流式输出模式...[/bold yellow]")

                    current_content = ""
                    current_node = ""
                    token_count = 0

                    async for message_chunk, metadata in graph.astream(inputs, config=config, stream_mode="messages"):
                        node_name = metadata.get('langgraph_node', 'unknown')

                        # 节点切换处理
                        if node_name != current_node:
                            if current_content:
                                # 完成前一个节点的输出
                                console.print(Panel(
                                    current_content,
                                    title=f"[bold cyan]🤖 {current_node} 完成[/bold cyan]",
                                    border_style="cyan"
                                ))
                                console.print(f"[dim]📊 生成了 {token_count} 个token[/dim]")

                            current_node = node_name
                            current_content = ""
                            token_count = 0

                            # 显示节点开始
                            node_icons = {
                                'master_agent': '🧠',
                                'visual_expert': '🎨',
                                'audio_expert': '🎵',
                                'video_expert': '🎬'
                            }
                            icon = node_icons.get(node_name, '⚡')
                            console.print(f"\n[bold yellow]{icon} {node_name} 正在生成内容...[/bold yellow]")

                        # 处理消息内容
                        if hasattr(message_chunk, 'content') and message_chunk.content:
                            current_content += message_chunk.content
                            token_count += 1

                            # 实时显示（每5个字符更新一次，避免过于频繁）
                            if token_count % 5 == 0:
                                console.print(f"[dim]{message_chunk.content}[/dim]", end="", flush=True)

                            # 显示进度指示
                            if token_count % 50 == 0:
                                console.print(f" [dim]({token_count} tokens)[/dim]", end="")

                    # 完成最后一个节点
                    if current_content:
                        console.print(Panel(
                            current_content,
                            title=f"[bold cyan]🤖 {current_node} 完成[/bold cyan]",
                            border_style="cyan"
                        ))
                        console.print(f"[dim]📊 最终生成了 {token_count} 个token[/dim]")

                    # 获取最终状态用于总结
                    try:
                        final_state = await graph.ainvoke(inputs, config=config)
                        final_event = {"final": final_state}
                    except Exception as e:
                        console.print(f"[red]获取最终状态时出错: {e}[/red]")
                        final_event = None
                
                # 显示最终结果
                if final_event and last_node_name:
                    messages = final_event.get(last_node_name, {}).get("messages", [])
                    if messages and messages[-1].type == "ai" and not messages[-1].tool_calls:
                        final_message = messages[-1]
                        execution_time = time.time() - visualizer.start_time if visualizer.start_time else None
                        visualizer.show_final_summary(final_message.content, execution_time)
                    else:
                        # 如果没有最终AI消息，显示简单的完成信息
                        execution_time = time.time() - visualizer.start_time if visualizer.start_time else None
                        console.print(Panel(
                            f"[bold]✅ 任务处理完成[/bold]\n"
                            f"[dim]• 执行步骤: {step_count} 个[/dim]\n"
                            f"[dim]• 执行时间: {execution_time:.1f} 秒[/dim]" if execution_time else "",
                            title="[bold blue]📊 执行完成[/bold blue]",
                            border_style="blue",
                            expand=False
                        ))

                console.rule("[bold green]✨ 准备接受下一个任务[/bold green]")

            except (KeyboardInterrupt, EOFError):
                break
            except Exception:
                console.print_exception(show_locals=True)

    console.print("\n[bold cyan]Conversation ended. Goodbye![/bold cyan]")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="DeerFlow V2 - 透明AI工作流体验",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 普通对话模式
  python interactive_chat.py

  # 启动时指定模板
  python interactive_chat.py --template zhao_benshan_xiangsheng

  # 启动时指定模板和参数
  python interactive_chat.py --template zhao_benshan_xiangsheng --params '{"topic": "看病", "style": "classic"}'

  # 查看可用模板
  python interactive_chat.py --list-templates
        """
    )

    parser.add_argument(
        '--template',
        type=str,
        help='指定要使用的模板ID'
    )

    parser.add_argument(
        '--params',
        type=str,
        help='模板参数JSON字符串，例如: \'{"topic": "看病", "style": "classic"}\''
    )

    parser.add_argument(
        '--list-templates',
        action='store_true',
        help='列出所有可用模板'
    )

    return parser.parse_args()


def list_available_templates():
    """列出所有可用模板"""
    from src.tools.template_tools import _template_registry

    if not _template_registry:
        console.print("[red]❌ 没有找到可用模板[/red]")
        return

    console.print(Panel(
        "[bold blue]📋 可用模板列表[/bold blue]",
        border_style="blue"
    ))

    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("模板ID", style="cyan")
    table.add_column("名称", style="green")
    table.add_column("分类", style="blue")
    table.add_column("描述", style="dim")

    for template_id, template in _template_registry.items():
        description = template.description[:60] + "..." if len(template.description) > 60 else template.description
        table.add_row(
            template_id,
            template.name,
            template.category,
            description
        )

    console.print(table)
    console.print(f"\n[dim]总计: {len(_template_registry)} 个模板[/dim]")


async def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 如果只是列出模板，显示后退出
    if args.list_templates:
        list_available_templates()
        return

    # 检查依赖
    try:
        from rich.console import Console as _
    except ImportError:
        print("Error: 'rich' library not found. Please run 'pip install rich'")
        exit(1)

    # 如果指定了模板，预加载到全局状态
    if args.template:
        initial_params = {}
        if args.params:
            try:
                initial_params = json.loads(args.params)
            except json.JSONDecodeError as e:
                console.print(f"[red]❌ 参数JSON格式错误: {e}[/red]")
                return

        # 预加载模板信息，然后进入正常对话模式
        await normal_chat_mode(preload_template_id=args.template, preload_params=initial_params)
    else:
        # 普通对话模式
        await normal_chat_mode()








if __name__ == "__main__":
    asyncio.run(main())

    asyncio.run(main())
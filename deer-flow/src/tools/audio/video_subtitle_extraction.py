# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
视频字幕提取工具 - AI二创专用

这个工具提供完整的视频字幕提取和分析能力，专为AI二创场景设计：
1. 智能输入处理（视频URL、本地文件、音频文件）
2. 自动音频提取（内部ffmpeg处理）
3. 精确字幕生成（基于火山引擎API）
4. 智能说话人识别（自动命名和分组）
5. 结构化输出（JSON主要，SRT/VTT可选）

典型应用：AI二创赵本山小品、提取经典对话用于声音克隆等
"""

import logging
import json
import os
import time
import tempfile
import subprocess
from typing import Optional, Dict, Any, List, Literal
import requests
import asyncio
from functools import partial
from urllib.parse import urlparse
import re

from langchain_core.tools import StructuredTool
from pydantic.v1 import BaseModel, Field

# DeerFlow Imports
from src.config.configuration import Configuration

logger = logging.getLogger(__name__)

# === Input Models ===

class VideoSubtitleExtractionInput(BaseModel):
    """视频字幕提取工具输入参数"""

    media_url: str = Field(
        ...,
        description="Video or audio file URL/path to extract subtitles from (supports mp4, avi, mov, mp3, wav, etc.)"
    )

    language: str = Field(
        default="zh-CN",
        description="Language code: zh-CN(Chinese), en-US(English), ja-JP(Japanese), ko-KR(Korean), etc."
    )

    auto_speaker_identification: bool = Field(
        default=True,
        description="Automatically identify and name speakers based on voice characteristics"
    )

    speaker_mapping: Optional[Dict[str, str]] = Field(
        default=None,
        description="Manual speaker ID to name mapping, e.g. {'1': '赵本山', '2': '宋丹丹'}. If provided, overrides auto identification"
    )

    output_format: Literal["json", "srt", "vtt", "all"] = Field(
        default="json",
        description="Output format: json(structured data), srt(subtitle file), vtt(web subtitle), all(multiple formats)"
    )

    precision_level: Literal["sentence"] = Field(
        default="sentence",
        description="Timestamp precision level: sentence(sentence-level timing, recommended for AI recreation)"
    )

    extract_audio_segments: bool = Field(
        default=True,
        description="Extract individual audio segments for each speaker (useful for voice cloning)"
    )

    save_to_cos: bool = Field(
        default=False,
        description="Save results (JSON and audio segments) to Tencent Cloud COS for persistent storage"
    )

    cos_bucket_prefix: str = Field(
        default="ai-recreation",
        description="COS bucket prefix for organizing files, e.g. 'ai-recreation/zhaobenshang/'"
    )

# === 通义听悟API处理 ===

def _process_with_tongyi(
    access_key_id: str,
    access_key_secret: str,
    app_key: str,
    audio_url: str,
    args: VideoSubtitleExtractionInput
) -> Dict[str, Any]:
    """使用通义听悟API处理音频"""
    try:
        # 导入通义听悟适配器
        from .tongyi_tingwu_adapter import TongyiTingwuAdapter

        # 创建适配器
        adapter = TongyiTingwuAdapter(access_key_id, access_key_secret, app_key)

        # 处理音频
        result = adapter.process_audio(audio_url, args.language)

        if not result["success"]:
            return {
                "success": False,
                "error": f"通义听悟处理失败: {result['error']}",
                "content": None
            }

        # 获取转换后的火山引擎格式数据
        volcengine_format_data = result["data"]

        # 使用现有的说话人识别逻辑处理数据
        speaker_result = _identify_speakers(volcengine_format_data, args)
        if not speaker_result["success"]:
            return speaker_result

        return {
            "success": True,
            "subtitles": speaker_result["subtitles"],
            "speaker_info": speaker_result["speaker_info"]
        }

    except Exception as e:
        logger.error(f"通义听悟处理异常: {e}")
        return {
            "success": False,
            "error": f"通义听悟处理异常: {str(e)}",
            "content": None
        }

# === 核心工具实现 ===

def _execute_video_subtitle_extraction(
    config: Configuration,
    **kwargs: Any
) -> Dict[str, Any]:
    """
    执行视频字幕提取的核心逻辑

    Args:
        config: DeerFlow配置对象
        **kwargs: 工具输入参数

    Returns:
        包含字幕提取结果的字典
    """
    try:
        # 解析输入参数
        args = VideoSubtitleExtractionInput(**kwargs)
    except Exception as e:
        return {
            "success": False,
            "error": f"Input parameter error: {str(e)}",
            "content": None
        }

    # 检查API提供商配置
    api_provider = config.subtitle_api_provider or "tongyi"

    # 根据配置选择API提供商
    if api_provider == "tongyi":
        # 检查通义听悟配置
        tongyi_access_key_id = (config.tongyi_tingwu_access_key_id or
                               os.getenv("TONGYI_TINGWU_ACCESS_KEY_ID") or
                               os.getenv("ALIBABA_CLOUD_ACCESS_KEY_ID"))
        tongyi_access_key_secret = (config.tongyi_tingwu_access_key_secret or
                                   os.getenv("TONGYI_TINGWU_ACCESS_KEY_SECRET") or
                                   os.getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET"))
        tongyi_app_key = (config.tongyi_tingwu_app_key or
                         os.getenv("TONGYI_TINGWU_APP_KEY"))

        if not tongyi_access_key_id or not tongyi_access_key_secret or not tongyi_app_key:
            logger.warning("通义听悟API配置缺失，尝试使用火山引擎")
            api_provider = "volcengine"

    if api_provider == "volcengine":
        # 检查火山引擎配置
        api_key = os.getenv("VOLCENGINE_SUBTITLE_KEY") or os.getenv("VOLCENGINE_API_KEY")
        appid = os.getenv("VOLCENGINE_SUBTITLE_APPID") or os.getenv("VOLCENGINE_APPID")

        if not api_key or not appid:
            # 尝试从YAML配置中获取
            try:
                from src.config import load_yaml_config
                yaml_config = load_yaml_config("conf.yaml")
                volcengine_config = yaml_config.get("VOLCENGINE", {})
                if not api_key:
                    api_key = volcengine_config.get("api_key")
                if not appid:
                    appid = volcengine_config.get("appid")
            except:
                pass

        if not api_key or not appid:
            return {
                "success": False,
                "error": "所有API配置都缺失。请配置通义听悟或火山引擎API",
                "content": None
            }

    try:
        # 第一步：智能媒体处理
        media_result = _process_media_input(args.media_url)
        if not media_result["success"]:
            return media_result

        audio_url = media_result["audio_url"]
        media_info = media_result["media_info"]
        
        logger.info(f"Media processed successfully: {media_info}")

        # 第二步：根据API提供商处理字幕
        task_id = None  # 初始化task_id
        raw_subtitles = {}  # 初始化raw_subtitles

        if api_provider == "tongyi":
            # 使用通义听悟API
            logger.info("使用通义听悟API进行字幕提取")
            tongyi_result = _process_with_tongyi(
                tongyi_access_key_id, tongyi_access_key_secret, tongyi_app_key,
                audio_url, args
            )
            if not tongyi_result["success"]:
                return tongyi_result

            processed_subtitles = tongyi_result["subtitles"]
            speaker_info = tongyi_result["speaker_info"]
            task_id = f"tongyi_{int(time.time())}"  # 生成通义听悟任务ID
            raw_subtitles = {"duration": media_info.get("duration", 0)}  # 使用媒体信息中的时长

        else:
            # 使用火山引擎API
            logger.info("使用火山引擎API进行字幕提取")
            submit_result = _submit_subtitle_task(api_key, appid, audio_url, args)
            if not submit_result["success"]:
                return submit_result

            task_id = submit_result["task_id"]
            logger.info(f"Subtitle task submitted: {task_id}")

            # 第三步：查询字幕结果
            query_result = _query_subtitle_result(api_key, appid, task_id)
            if not query_result["success"]:
                return query_result

            raw_subtitles = query_result["data"]

            # 第四步：智能说话人识别
            speaker_result = _identify_speakers(raw_subtitles, args)
            if not speaker_result["success"]:
                return speaker_result

            processed_subtitles = speaker_result["subtitles"]
            speaker_info = speaker_result["speaker_info"]

        # 第五步：提取音频片段（如果需要）
        audio_segments = {}
        if args.extract_audio_segments and media_result.get("local_audio_path"):
            segments_result = _extract_audio_segments(
                media_result["local_audio_path"], 
                processed_subtitles, 
                speaker_info
            )
            if segments_result["success"]:
                audio_segments = segments_result["segments"]

        # 第六步：格式化输出
        final_result = _format_extraction_output(
            processed_subtitles,
            speaker_info,
            media_info,
            audio_segments,
            args
        )

        # 第七步：保存到COS（如果需要）
        cos_urls = {}
        if args.save_to_cos:
            cos_result = _save_to_cos(
                final_result,
                audio_segments,
                args,
                task_id,
                config
            )
            if cos_result["success"]:
                cos_urls = cos_result["urls"]
                logger.info(f"Results saved to COS: {len(cos_urls)} files")

                # 更新JSON结果中的file_path为COS URL
                final_result = _update_file_paths_with_cos_urls(final_result, cos_urls)

        return {
            "success": True,
            "content": final_result,
            "cos_urls": cos_urls,
            "metadata": {
                "task_id": task_id,
                "media_info": media_info,
                "speaker_count": len(speaker_info),
                "total_duration": raw_subtitles.get("duration", 0),
                "language": args.language,
                "saved_to_cos": args.save_to_cos
            }
        }

    except Exception as e:
        logger.error(f"Video subtitle extraction error: {e}", exc_info=True)
        return {
            "success": False,
            "error": f"Video subtitle extraction process error: {str(e)}",
            "content": None
        }

def _process_media_input(media_url: str) -> Dict[str, Any]:
    """
    智能处理媒体输入（视频URL、本地文件、音频文件）
    """
    try:
        logger.info(f"Processing media input: {media_url}")
        
        # 判断输入类型
        is_url = media_url.startswith(('http://', 'https://'))
        is_local_file = os.path.exists(media_url)
        
        if not is_url and not is_local_file:
            return {
                "success": False,
                "error": f"Invalid media input: {media_url} (not a valid URL or local file)",
                "audio_url": None,
                "media_info": None
            }

        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="video_subtitle_")
        
        if is_url:
            # 下载文件
            logger.info("Downloading media file...")
            response = requests.get(media_url, timeout=300, stream=True)
            response.raise_for_status()
            
            # 根据URL或Content-Type判断文件类型
            content_type = response.headers.get('content-type', '')
            file_extension = _get_file_extension(media_url, content_type)
            
            local_media_path = os.path.join(temp_dir, f"media{file_extension}")
            
            with open(local_media_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
        else:
            local_media_path = media_url

        # 检查文件类型并处理
        media_info = _get_media_info(local_media_path)
        
        if media_info["is_video"]:
            # 视频文件，需要提取音频
            logger.info("Extracting audio from video...")
            audio_path = _extract_audio_from_video(local_media_path, temp_dir)
            if not audio_path:
                return {
                    "success": False,
                    "error": "Failed to extract audio from video",
                    "audio_url": None,
                    "media_info": None
                }
        else:
            # 音频文件，直接使用
            audio_path = local_media_path

        # 上传音频到临时存储（或返回本地路径）
        if is_url:
            # 如果原始输入是URL，我们需要提供一个可访问的音频URL
            # 这里简化处理，实际项目中可能需要上传到COS等
            audio_url = _upload_temp_audio(audio_path)
        else:
            # 本地文件，直接使用路径（需要确保API能访问）
            audio_url = audio_path

        return {
            "success": True,
            "audio_url": audio_url,
            "local_audio_path": audio_path,
            "media_info": media_info
        }

    except Exception as e:
        logger.error(f"Media processing error: {e}", exc_info=True)
        return {
            "success": False,
            "error": f"Media processing error: {str(e)}",
            "audio_url": None,
            "media_info": None
        }

def _get_file_extension(url: str, content_type: str) -> str:
    """根据URL和Content-Type推断文件扩展名"""
    # 先尝试从URL获取
    parsed_url = urlparse(url)
    path = parsed_url.path
    if '.' in path:
        return os.path.splitext(path)[1]
    
    # 从Content-Type推断
    content_type_map = {
        'video/mp4': '.mp4',
        'video/avi': '.avi',
        'video/mov': '.mov',
        'video/quicktime': '.mov',
        'audio/mpeg': '.mp3',
        'audio/wav': '.wav',
        'audio/mp4': '.m4a',
        'audio/flac': '.flac'
    }
    
    return content_type_map.get(content_type, '.mp4')  # 默认mp4

def _get_media_info(file_path: str) -> Dict[str, Any]:
    """获取媒体文件信息"""
    try:
        # 使用ffprobe获取媒体信息
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json', 
            '-show_format', '-show_streams', file_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            logger.warning(f"ffprobe failed: {result.stderr}")
            # 回退到简单的文件扩展名判断
            ext = os.path.splitext(file_path)[1].lower()
            is_video = ext in ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']
            return {
                "is_video": is_video,
                "duration": 0,
                "format": ext[1:] if ext else "unknown"
            }
        
        probe_data = json.loads(result.stdout)
        
        # 分析流信息
        has_video = False
        has_audio = False
        duration = 0
        
        for stream in probe_data.get('streams', []):
            if stream.get('codec_type') == 'video':
                has_video = True
            elif stream.get('codec_type') == 'audio':
                has_audio = True
        
        # 获取时长
        format_info = probe_data.get('format', {})
        duration = float(format_info.get('duration', 0))
        
        return {
            "is_video": has_video,
            "has_audio": has_audio,
            "duration": duration,
            "format": format_info.get('format_name', 'unknown'),
            "size": int(format_info.get('size', 0))
        }
        
    except Exception as e:
        logger.error(f"Get media info error: {e}")
        # 回退处理
        ext = os.path.splitext(file_path)[1].lower()
        is_video = ext in ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']
        return {
            "is_video": is_video,
            "duration": 0,
            "format": ext[1:] if ext else "unknown"
        }

def _extract_audio_from_video(video_path: str, output_dir: str) -> Optional[str]:
    """从视频中提取音频"""
    try:
        audio_path = os.path.join(output_dir, "extracted_audio.wav")

        # 使用ffmpeg提取音频
        cmd = [
            'ffmpeg', '-i', video_path, '-vn', '-acodec', 'pcm_s16le',
            '-ar', '16000', '-ac', '1', '-y', audio_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

        if result.returncode != 0:
            logger.error(f"ffmpeg audio extraction failed: {result.stderr}")
            return None

        if os.path.exists(audio_path):
            logger.info(f"Audio extracted successfully: {audio_path}")
            return audio_path
        else:
            logger.error("Audio file not created")
            return None

    except Exception as e:
        logger.error(f"Extract audio error: {e}")
        return None

def _upload_temp_audio(audio_path: str) -> str:
    """上传临时音频文件（简化实现，实际项目中可能需要上传到COS）"""
    # 这里简化处理，实际项目中应该上传到云存储
    # 暂时返回本地路径，需要确保API能访问
    return f"file://{audio_path}"

def _submit_subtitle_task(api_key: str, appid: str, audio_url: str, args: VideoSubtitleExtractionInput) -> Dict[str, Any]:
    """提交字幕生成任务到火山引擎API"""
    try:
        submit_url = "https://openspeech.bytedance.com/api/v1/vc/submit"

        headers = {
            "Authorization": f"Bearer; {api_key}",
            "Content-Type": "application/json"
        }

        # 构建请求参数
        params = {
            "appid": appid,
            "token": api_key,  # 添加token参数到URL中
            "language": args.language,
            "caption_type": "auto",  # 自动识别语音和歌唱
            "words_per_line": 15 if args.language.startswith("zh") else 55,
            "max_lines": 2,  # 允许更多行以获得更好的分句
            "use_itn": "true",
            "use_punc": "true",
            "with_speaker_info": "true",  # 启用说话人识别
            "use_ddc": "true"  # 启用顺滑标注，有助于识别重复词和口水词
        }

        # 请求体
        if audio_url.startswith("file://"):
            # 本地文件，需要读取二进制数据
            local_path = audio_url[7:]  # 移除 "file://" 前缀
            headers["Content-Type"] = "audio/wav"

            with open(local_path, 'rb') as f:
                audio_data = f.read()

            response = requests.post(
                submit_url,
                headers=headers,
                params=params,
                data=audio_data,
                timeout=60
            )
        else:
            # URL方式
            payload = {"url": audio_url}
            response = requests.post(
                submit_url,
                headers=headers,
                params=params,
                json=payload,
                timeout=60
            )

        response.raise_for_status()
        response_data = response.json()

        if response_data.get("code") != 0:
            return {
                "success": False,
                "error": f"Submit task failed: {response_data.get('message', 'Unknown error')}",
                "task_id": None
            }

        return {
            "success": True,
            "task_id": response_data.get("id"),
            "message": response_data.get("message", "Success")
        }

    except Exception as e:
        logger.error(f"Submit subtitle task error: {e}")
        return {
            "success": False,
            "error": f"Submit subtitle task error: {str(e)}",
            "task_id": None
        }

def _query_subtitle_result(api_key: str, appid: str, task_id: str) -> Dict[str, Any]:
    """查询字幕生成结果"""
    try:
        query_url = "https://openspeech.bytedance.com/api/v1/vc/query"

        headers = {
            "Authorization": f"Bearer; {api_key}"
        }

        params = {
            "appid": appid,
            "token": api_key,  # 添加token参数到URL中
            "id": task_id,
            "blocking": "1"  # 使用阻塞模式
        }

        logger.info(f"Querying subtitle result for task: {task_id}")
        response = requests.get(query_url, headers=headers, params=params, timeout=300)
        response.raise_for_status()

        response_data = response.json()

        if response_data.get("code") != 0:
            return {
                "success": False,
                "error": f"Query result failed: {response_data.get('message', 'Unknown error')}",
                "data": None
            }

        return {
            "success": True,
            "data": response_data
        }

    except Exception as e:
        logger.error(f"Query subtitle result error: {e}")
        return {
            "success": False,
            "error": f"Query subtitle result error: {str(e)}",
            "data": None
        }

def _identify_speakers(raw_subtitles: Dict[str, Any], args: VideoSubtitleExtractionInput) -> Dict[str, Any]:
    """智能说话人识别和命名"""
    try:
        utterances = raw_subtitles.get("utterances", [])

        if not utterances:
            return {
                "success": False,
                "error": "No utterances found in subtitle data",
                "subtitles": [],
                "speaker_info": {}
            }

        # 收集所有说话人ID
        speaker_ids = set()
        for utterance in utterances:
            # 检查utterance级别的speaker信息
            speaker_id = None
            if "attribute" in utterance and "speaker" in utterance["attribute"]:
                speaker_id = utterance["attribute"]["speaker"]
            else:
                # 检查words级别的speaker信息
                words = utterance.get("words", [])
                for word in words:
                    if "attribute" in word and "speaker" in word["attribute"]:
                        speaker_id = word["attribute"]["speaker"]
                        break

            if speaker_id:
                speaker_ids.add(speaker_id)

        logger.info(f"Found {len(speaker_ids)} speakers: {speaker_ids}")

        # 如果没有检测到说话人，使用备用检测方法
        if len(speaker_ids) == 0:
            logger.warning("⚠️ 火山引擎API没有返回说话人信息，使用备用检测方法")
            utterances = _apply_fallback_speaker_detection(utterances)

            # 重新提取说话人ID
            speaker_ids = set()
            for utterance in utterances:
                speaker_id = utterance.get("fallback_speaker_id")
                if speaker_id:
                    speaker_ids.add(speaker_id)

            logger.info(f"备用检测结果: {len(speaker_ids)} speakers: {speaker_ids}")

        # 说话人命名策略
        speaker_info = {}

        if args.speaker_mapping:
            # 使用用户提供的映射
            speaker_info = args.speaker_mapping.copy()
        elif args.auto_speaker_identification:
            # 自动识别和命名
            speaker_info = _auto_name_speakers(utterances, speaker_ids, args.language)
        else:
            # 使用默认命名
            speaker_names = ["主要角色", "次要角色", "第三角色", "第四角色", "第五角色"]
            for i, speaker_id in enumerate(sorted(speaker_ids)):
                speaker_info[speaker_id] = speaker_names[i] if i < len(speaker_names) else f"角色{i+1}"

        # 处理字幕数据，添加说话人信息
        processed_subtitles = []
        for utterance in utterances:
            # 确定这个utterance的说话人
            speaker_id = _get_utterance_speaker(utterance)
            speaker_name = speaker_info.get(speaker_id, f"说话人{speaker_id}")

            processed_utterance = {
                "speaker_id": speaker_id,
                "speaker_name": speaker_name,
                "text": utterance.get("text", ""),
                "start_time": utterance.get("start_time", 0),
                "end_time": utterance.get("end_time", 0),
                "duration": utterance.get("end_time", 0) - utterance.get("start_time", 0)
            }

            processed_subtitles.append(processed_utterance)

        return {
            "success": True,
            "subtitles": processed_subtitles,
            "speaker_info": speaker_info
        }

    except Exception as e:
        logger.error(f"Speaker identification error: {e}")
        return {
            "success": False,
            "error": f"Speaker identification error: {str(e)}",
            "subtitles": [],
            "speaker_info": {}
        }

def _apply_fallback_speaker_detection(utterances: List[Dict]) -> List[Dict]:
    """
    备用说话人检测：基于语句时长的智能分组
    当火山引擎API没有返回说话人信息时使用
    """
    logger.info("🎤 使用备用说话人检测（基于语句时长）")

    if not utterances:
        return utterances

    # 计算每句话的时长
    durations = []
    for utterance in utterances:
        start_time = utterance.get("start_time", 0) / 1000.0
        end_time = utterance.get("end_time", 0) / 1000.0
        duration = end_time - start_time
        durations.append(duration)

    # 使用三分位数进行智能分组
    import numpy as np
    short_threshold = np.percentile(durations, 33)
    long_threshold = np.percentile(durations, 67)

    logger.info(f"   时长阈值: 短句<{short_threshold:.1f}s, 长句>{long_threshold:.1f}s")

    # 分配说话人ID
    for i, utterance in enumerate(utterances):
        duration = durations[i]

        if duration < short_threshold:
            speaker_id = "1"  # 短句说话人（通常是快速对话）
        elif duration > long_threshold:
            speaker_id = "3"  # 长句说话人（通常是叙述或解释）
        else:
            speaker_id = "2"  # 中等句说话人（通常是正常对话）

        utterance["fallback_speaker_id"] = speaker_id

    # 统计结果
    speaker_ids = set(u.get("fallback_speaker_id") for u in utterances)
    logger.info(f"✅ 备用检测完成，识别到 {len(speaker_ids)} 个说话人")

    return utterances

def _get_utterance_speaker(utterance: Dict[str, Any]) -> str:
    """获取utterance的说话人ID"""
    # 优先检查备用检测的结果
    if "fallback_speaker_id" in utterance:
        return utterance["fallback_speaker_id"]

    # 检查utterance级别的speaker信息
    if "attribute" in utterance and "speaker" in utterance["attribute"]:
        return utterance["attribute"]["speaker"]

    # 检查words级别的speaker信息
    words = utterance.get("words", [])
    speaker_counts = {}

    for word in words:
        if "attribute" in word and "speaker" in word["attribute"]:
            speaker_id = word["attribute"]["speaker"]
            speaker_counts[speaker_id] = speaker_counts.get(speaker_id, 0) + 1

    if speaker_counts:
        # 返回出现次数最多的说话人
        return max(speaker_counts, key=speaker_counts.get)

    return "1"  # 默认使用数字ID

def _auto_name_speakers(utterances: List[Dict], speaker_ids: set, language: str) -> Dict[str, str]:
    """基于内容自动命名说话人"""
    try:
        speaker_info = {}

        # 分析每个说话人的对话内容，尝试推断身份
        speaker_texts = {}
        speaker_stats = {}

        for utterance in utterances:
            speaker_id = _get_utterance_speaker(utterance)
            text = utterance.get("text", "")

            if speaker_id not in speaker_texts:
                speaker_texts[speaker_id] = []
                speaker_stats[speaker_id] = {
                    "total_duration": 0,
                    "utterance_count": 0,
                    "avg_words_per_utterance": 0
                }

            speaker_texts[speaker_id].append(text)
            speaker_stats[speaker_id]["total_duration"] += utterance.get("end_time", 0) - utterance.get("start_time", 0)
            speaker_stats[speaker_id]["utterance_count"] += 1

        # 计算统计信息
        for speaker_id in speaker_stats:
            stats = speaker_stats[speaker_id]
            total_words = sum(len(text) for text in speaker_texts[speaker_id])
            stats["avg_words_per_utterance"] = total_words / max(stats["utterance_count"], 1)

        # 智能命名策略
        sorted_speakers = sorted(speaker_ids, key=lambda x: speaker_stats.get(x, {}).get("total_duration", 0), reverse=True)

        if language.startswith("zh"):
            # 中文命名策略
            name_patterns = [
                "主角", "配角", "旁白", "群众甲", "群众乙", "群众丙"
            ]

            # 特殊内容检测
            for i, speaker_id in enumerate(sorted_speakers):
                texts = speaker_texts.get(speaker_id, [])
                combined_text = "".join(texts)

                # 检测特定角色特征
                if any(keyword in combined_text for keyword in ["我说", "你说", "咱们", "老铁"]):
                    speaker_info[speaker_id] = "主持人"
                elif any(keyword in combined_text for keyword in ["哎呀", "我的妈", "这咋整"]):
                    speaker_info[speaker_id] = "东北角色"
                elif any(keyword in combined_text for keyword in ["各位观众", "大家好", "谢谢大家"]):
                    speaker_info[speaker_id] = "表演者"
                else:
                    speaker_info[speaker_id] = name_patterns[i] if i < len(name_patterns) else f"角色{i+1}"
        else:
            # 英文命名策略
            name_patterns = [
                "Speaker A", "Speaker B", "Speaker C", "Speaker D", "Speaker E"
            ]

            for i, speaker_id in enumerate(sorted_speakers):
                speaker_info[speaker_id] = name_patterns[i] if i < len(name_patterns) else f"Speaker {chr(65+i)}"

        return speaker_info

    except Exception as e:
        logger.error(f"Auto naming speakers error: {e}")
        # 回退到简单命名
        speaker_info = {}
        for i, speaker_id in enumerate(sorted(speaker_ids)):
            speaker_info[speaker_id] = f"说话人{i+1}"
        return speaker_info

def _extract_audio_segments(audio_path: str, subtitles: List[Dict], speaker_info: Dict[str, str]) -> Dict[str, Any]:
    """为每个说话人提取音频片段"""
    try:
        segments = {}
        temp_dir = os.path.dirname(audio_path)

        # 按说话人分组
        speaker_segments = {}
        for subtitle in subtitles:
            speaker_id = subtitle["speaker_id"]
            speaker_name = subtitle["speaker_name"]

            if speaker_name not in speaker_segments:
                speaker_segments[speaker_name] = []

            speaker_segments[speaker_name].append({
                "start_time": subtitle["start_time"] / 1000.0,  # 转换为秒
                "end_time": subtitle["end_time"] / 1000.0,
                "text": subtitle["text"]
            })

        # 为每个说话人提取音频片段
        for speaker_name, speaker_segs in speaker_segments.items():
            speaker_audio_files = []

            for i, seg in enumerate(speaker_segs):
                output_file = os.path.join(temp_dir, f"{speaker_name}_{i+1:03d}.wav")

                # 使用ffmpeg提取片段
                cmd = [
                    'ffmpeg', '-i', audio_path,
                    '-ss', str(seg["start_time"]),
                    '-t', str(seg["end_time"] - seg["start_time"]),
                    '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1',
                    '-y', output_file
                ]

                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                    if result.returncode == 0 and os.path.exists(output_file):
                        speaker_audio_files.append({
                            "file_path": output_file,
                            "start_time": seg["start_time"],
                            "end_time": seg["end_time"],
                            "duration": seg["end_time"] - seg["start_time"],
                            "text": seg["text"]
                        })
                except Exception as e:
                    logger.warning(f"Failed to extract segment {i+1} for {speaker_name}: {e}")
                    continue

            if speaker_audio_files:
                segments[speaker_name] = speaker_audio_files

        return {
            "success": True,
            "segments": segments
        }

    except Exception as e:
        logger.error(f"Extract audio segments error: {e}")
        return {
            "success": False,
            "segments": {}
        }

def _format_extraction_output(
    subtitles: List[Dict],
    speaker_info: Dict[str, str],
    media_info: Dict[str, Any],
    audio_segments: Dict[str, List],
    args: VideoSubtitleExtractionInput
) -> str:
    """格式化最终输出"""
    try:
        if args.output_format == "json" or args.output_format == "all":
            # JSON格式输出
            json_output = {
                "media_info": {
                    "duration": media_info.get("duration", 0),
                    "format": media_info.get("format", "unknown"),
                    "is_video": media_info.get("is_video", False),
                    "has_audio": media_info.get("has_audio", True)
                },
                "speaker_info": speaker_info,
                "total_speakers": len(speaker_info),
                "total_utterances": len(subtitles),
                "language": args.language,
                "subtitles": subtitles
            }

            # 添加音频片段信息
            if audio_segments:
                json_output["audio_segments"] = {}
                for speaker_name, segments in audio_segments.items():
                    json_output["audio_segments"][speaker_name] = {
                        "segment_count": len(segments),
                        "total_duration": sum(seg["duration"] for seg in segments),
                        "segments": segments
                    }

            # 添加统计信息
            json_output["statistics"] = _generate_statistics(subtitles, speaker_info)

            if args.output_format == "json":
                return json.dumps(json_output, ensure_ascii=False, indent=2)

        # 如果需要其他格式，这里可以扩展
        if args.output_format == "srt":
            return _generate_srt_format(subtitles)
        elif args.output_format == "vtt":
            return _generate_vtt_format(subtitles)
        elif args.output_format == "all":
            return json.dumps({
                "json": json_output,
                "srt": _generate_srt_format(subtitles),
                "vtt": _generate_vtt_format(subtitles)
            }, ensure_ascii=False, indent=2)

        return json.dumps(json_output, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"Format output error: {e}")
        return f"Error formatting output: {str(e)}"

def _save_to_cos(
    json_result: str,
    audio_segments: Dict[str, List],
    args: VideoSubtitleExtractionInput,
    task_id: str,
    config: Configuration
) -> Dict[str, Any]:
    """
    使用现有的COS上传函数保存结果到腾讯云COS
    """
    try:
        # 使用现有的COS上传工具
        from src.utils.cos_uploader import get_cos_client, upload_to_cos

        # 创建COS客户端
        cos_client = get_cos_client(config)

        uploaded_urls = {}

        # 1. 保存JSON结果
        try:
            json_bytes = json_result.encode('utf-8')
            json_url = upload_to_cos(
                client=cos_client,
                file_bytes=json_bytes,
                bucket=config.cos_bucket,
                region=config.cos_region,
                file_extension="json"
            )
            uploaded_urls["json_result"] = json_url
            logger.info(f"JSON result saved to COS: {json_url}")
        except Exception as e:
            logger.error(f"Failed to save JSON to COS: {e}")

        # 2. 保存音频片段
        audio_urls = {}
        for speaker_name, segments in audio_segments.items():
            speaker_urls = []

            for i, segment in enumerate(segments):
                if "file_path" in segment and os.path.exists(segment["file_path"]):
                    try:
                        # 读取音频文件
                        with open(segment["file_path"], 'rb') as f:
                            audio_bytes = f.read()

                        # 上传音频文件
                        audio_url = upload_to_cos(
                            client=cos_client,
                            file_bytes=audio_bytes,
                            bucket=config.cos_bucket,
                            region=config.cos_region,
                            file_extension="wav"
                        )

                        speaker_urls.append({
                            "segment_index": i + 1,
                            "text": segment.get("text", ""),
                            "start_time": segment.get("start_time", 0),
                            "end_time": segment.get("end_time", 0),
                            "cos_url": audio_url
                        })

                        logger.info(f"Audio segment {i+1} for {speaker_name} saved to COS: {audio_url}")

                    except Exception as e:
                        logger.error(f"Failed to upload audio segment {i+1} for {speaker_name}: {e}")

            if speaker_urls:
                audio_urls[speaker_name] = speaker_urls

        uploaded_urls["audio_segments"] = audio_urls

        # 3. 生成汇总信息
        summary = {
            "task_id": task_id,
            "total_files": 1 + sum(len(segments) for segments in audio_urls.values()),
            "speakers": list(audio_urls.keys()),
            "json_url": uploaded_urls.get("json_result"),
            "audio_segments_count": {speaker: len(segments) for speaker, segments in audio_urls.items()}
        }

        # 保存汇总信息
        try:
            summary_bytes = json.dumps(summary, ensure_ascii=False, indent=2).encode('utf-8')
            summary_url = upload_to_cos(
                client=cos_client,
                file_bytes=summary_bytes,
                bucket=config.cos_bucket,
                region=config.cos_region,
                file_extension="json"
            )
            uploaded_urls["summary"] = summary_url
            logger.info(f"Summary saved to COS: {summary_url}")
        except Exception as e:
            logger.error(f"Failed to save summary to COS: {e}")

        logger.info(f"Successfully saved to COS: {summary}")

        return {
            "success": True,
            "urls": uploaded_urls,
            "summary": summary
        }

    except Exception as e:
        logger.error(f"COS save error: {e}")
        return {
            "success": False,
            "error": f"COS save error: {str(e)}",
            "urls": {}
        }

def _update_file_paths_with_cos_urls(json_result: str, cos_urls: Dict[str, Any]) -> str:
    """更新JSON结果中的file_path为COS URL"""
    try:
        import json

        # 解析JSON结果
        result_data = json.loads(json_result)

        # 获取音频片段的COS URL映射
        audio_segments_cos = cos_urls.get("audio_segments", {})

        # 更新audio_segments中的file_path
        if "audio_segments" in result_data:
            for speaker_name, speaker_data in result_data["audio_segments"].items():
                if isinstance(speaker_data, dict) and "segments" in speaker_data:
                    segments = speaker_data["segments"]
                    cos_segments = audio_segments_cos.get(speaker_name, [])

                    # 为每个片段更新file_path
                    for i, segment in enumerate(segments):
                        if i < len(cos_segments):
                            cos_segment = cos_segments[i]
                            segment["file_path"] = cos_segment.get("cos_url", segment.get("file_path", ""))

        # 返回更新后的JSON字符串
        return json.dumps(result_data, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"Failed to update file paths with COS URLs: {e}")
        return json_result  # 返回原始结果

def _generate_statistics(subtitles: List[Dict], speaker_info: Dict[str, str]) -> Dict[str, Any]:
    """生成统计信息"""
    stats = {
        "total_duration": 0,
        "total_words": 0,
        "speaker_stats": {}
    }

    for subtitle in subtitles:
        duration = subtitle["duration"]
        word_count = len(subtitle["text"])
        speaker_name = subtitle["speaker_name"]

        stats["total_duration"] += duration
        stats["total_words"] += word_count

        if speaker_name not in stats["speaker_stats"]:
            stats["speaker_stats"][speaker_name] = {
                "utterance_count": 0,
                "total_duration": 0,
                "total_words": 0,
                "avg_words_per_utterance": 0
            }

        speaker_stats = stats["speaker_stats"][speaker_name]
        speaker_stats["utterance_count"] += 1
        speaker_stats["total_duration"] += duration
        speaker_stats["total_words"] += word_count
        speaker_stats["avg_words_per_utterance"] = speaker_stats["total_words"] / speaker_stats["utterance_count"]

    return stats

def _generate_srt_format(subtitles: List[Dict]) -> str:
    """生成SRT格式字幕"""
    srt_content = []

    for i, subtitle in enumerate(subtitles, 1):
        start_time = _ms_to_srt_time(subtitle["start_time"])
        end_time = _ms_to_srt_time(subtitle["end_time"])
        speaker_name = subtitle["speaker_name"]
        text = subtitle["text"]

        srt_content.append(f"{i}")
        srt_content.append(f"{start_time} --> {end_time}")
        srt_content.append(f"[{speaker_name}] {text}")
        srt_content.append("")  # 空行

    return "\n".join(srt_content)

def _generate_vtt_format(subtitles: List[Dict]) -> str:
    """生成VTT格式字幕"""
    vtt_content = ["WEBVTT", ""]

    for subtitle in subtitles:
        start_time = _ms_to_vtt_time(subtitle["start_time"])
        end_time = _ms_to_vtt_time(subtitle["end_time"])
        speaker_name = subtitle["speaker_name"]
        text = subtitle["text"]

        vtt_content.append(f"{start_time} --> {end_time}")
        vtt_content.append(f"<v {speaker_name}>{text}")
        vtt_content.append("")  # 空行

    return "\n".join(vtt_content)

def _ms_to_srt_time(ms: int) -> str:
    """毫秒转SRT时间格式"""
    hours = ms // 3600000
    minutes = (ms % 3600000) // 60000
    seconds = (ms % 60000) // 1000
    milliseconds = ms % 1000
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

def _ms_to_vtt_time(ms: int) -> str:
    """毫秒转VTT时间格式"""
    hours = ms // 3600000
    minutes = (ms % 3600000) // 60000
    seconds = (ms % 60000) // 1000
    milliseconds = ms % 1000
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}.{milliseconds:03d}"

# === 异步版本 ===

async def _execute_video_subtitle_extraction_async(
    config: Configuration,
    **kwargs: Any
) -> Dict[str, Any]:
    """异步版本的视频字幕提取"""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(
        None,
        partial(_execute_video_subtitle_extraction, config, **kwargs)
    )

# === 工具工厂函数 ===

def get_video_subtitle_extraction_tool(config: Configuration) -> Optional[StructuredTool]:
    """
    创建视频字幕提取工具

    Args:
        config: DeerFlow配置对象

    Returns:
        配置好的StructuredTool实例，如果配置不足则返回None
    """
    # 检查API提供商配置
    api_provider = config.subtitle_api_provider or "tongyi"

    if api_provider == "tongyi":
        # 检查通义听悟配置
        tongyi_access_key_id = (config.tongyi_tingwu_access_key_id or
                               os.getenv("TONGYI_TINGWU_ACCESS_KEY_ID") or
                               os.getenv("ALIBABA_CLOUD_ACCESS_KEY_ID"))
        tongyi_access_key_secret = (config.tongyi_tingwu_access_key_secret or
                                   os.getenv("TONGYI_TINGWU_ACCESS_KEY_SECRET") or
                                   os.getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET"))
        tongyi_app_key = (config.tongyi_tingwu_app_key or
                         os.getenv("TONGYI_TINGWU_APP_KEY"))

        if not tongyi_access_key_id or not tongyi_access_key_secret or not tongyi_app_key:
            logger.warning("Video subtitle extraction tool: 通义听悟API配置缺失，尝试使用火山引擎")
            api_provider = "volcengine"
        else:
            logger.info("Video subtitle extraction tool: 使用通义听悟API")

    if api_provider == "volcengine":
        # 检查火山引擎配置
        api_key = os.getenv("VOLCENGINE_SUBTITLE_KEY") or os.getenv("VOLCENGINE_API_KEY")
        appid = os.getenv("VOLCENGINE_SUBTITLE_APPID") or os.getenv("VOLCENGINE_APPID")

        if not api_key or not appid:
            # 尝试从YAML配置中获取
            try:
                from src.config import load_yaml_config
                yaml_config = load_yaml_config("conf.yaml")
                volcengine_config = yaml_config.get("VOLCENGINE", {})
                if not api_key:
                    api_key = volcengine_config.get("api_key")
                if not appid:
                    appid = volcengine_config.get("appid")
            except:
                pass

        if not api_key or not appid:
            logger.warning("Video subtitle extraction tool: 所有API配置都缺失，无法创建工具")
            return None
        else:
            logger.info("Video subtitle extraction tool: 使用火山引擎API")

    def sync_func_with_config(**kwargs) -> str:
        """同步函数包装器"""
        try:
            result = _execute_video_subtitle_extraction(config, **kwargs)
            if result["success"]:
                return result["content"]
            else:
                return f"❌ Video subtitle extraction failed: {result['error']}"
        except Exception as e:
            logger.error(f"Video subtitle extraction tool execution error: {e}", exc_info=True)
            return f"❌ Video subtitle extraction tool error: {str(e)}"

    async def async_func_with_config(**kwargs) -> str:
        """异步函数包装器"""
        try:
            result = await _execute_video_subtitle_extraction_async(config, **kwargs)
            if result["success"]:
                return result["content"]
            else:
                return f"❌ Video subtitle extraction failed: {result['error']}"
        except Exception as e:
            logger.error(f"Video subtitle extraction tool async execution error: {e}", exc_info=True)
            return f"❌ Video subtitle extraction tool error: {str(e)}"

    return StructuredTool.from_function(
        func=sync_func_with_config,
        coroutine=async_func_with_config,
        name="video_subtitle_extraction",
        description=(
            "Advanced video subtitle extraction tool optimized for AI content recreation. "
            "Extracts audio, generates sentence-level subtitles with timestamps, identifies speakers automatically, "
            "and provides clean structured data for voice cloning and content generation. "
            "Perfect for AI recreation of classic videos like Zhao Benshan skits. "
            "Features: sentence-level precision, automatic speaker identification, audio segment extraction, "
            "and optional COS cloud storage for persistent AI recreation workflows."
        ),
        args_schema=VideoSubtitleExtractionInput
    )

# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
通义听悟API适配器
将通义听悟的数据格式转换为与火山引擎兼容的格式
"""

import logging
import json
import time
import requests
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class TongyiTingwuAdapter:
    """通义听悟API适配器"""
    
    def __init__(self, access_key_id: str, access_key_secret: str, app_key: str):
        """初始化适配器"""
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.app_key = app_key
        
        # 导入阿里云SDK
        try:
            from aliyunsdkcore.client import AcsClient
            from aliyunsdkcore.request import CommonRequest
            from aliyunsdkcore.auth.credentials import AccessKeyCredential
            
            credentials = AccessKeyCredential(access_key_id, access_key_secret)
            self.client = AcsClient(region_id='cn-beijing', credential=credentials)
            self.CommonRequest = CommonRequest
            
        except ImportError:
            logger.error("缺少阿里云SDK，请安装: pip install aliyun-python-sdk-core")
            raise
    
    def submit_task(self, audio_url: str, language: str = "cn", enable_speaker_diarization: bool = True) -> Dict[str, Any]:
        """提交转写任务"""
        logger.info("📤 提交通义听悟转写任务...")

        # 语言参数映射
        language_mapping = {
            "zh-CN": "cn",
            "zh": "cn",
            "cn": "cn",
            "en": "en",
            "en-US": "en"
        }

        # 转换语言参数
        source_language = language_mapping.get(language, "cn")
        logger.info(f"语言参数: {language} -> {source_language}")

        # 构建请求体
        request_body = {
            "AppKey": self.app_key,
            "Input": {
                "SourceLanguage": source_language,
                "TaskKey": f"speaker_diarization_{int(time.time())}",
                "FileUrl": audio_url
            },
            "Parameters": {
                "Transcription": {
                    "DiarizationEnabled": enable_speaker_diarization,
                    "Diarization": {
                        "SpeakerCount": 0  # 自动检测说话人数量
                    }
                }
            }
        }
        
        # 创建请求
        request = self.CommonRequest()
        request.set_accept_format('json')
        request.set_domain('tingwu.cn-beijing.aliyuncs.com')
        request.set_version('2023-09-30')
        request.set_protocol_type('https')
        request.set_method('PUT')
        request.set_uri_pattern('/openapi/tingwu/v2/tasks')
        request.add_header('Content-Type', 'application/json')
        request.add_query_param('type', 'offline')
        request.set_content(json.dumps(request_body).encode('utf-8'))
        
        try:
            response = self.client.do_action_with_exception(request)
            response_data = json.loads(response)
            
            if response_data.get('Code') == '0':
                task_id = response_data['Data']['TaskId']
                logger.info(f"✅ 任务提交成功: {task_id}")
                return {"success": True, "task_id": task_id}
            else:
                error_msg = response_data.get('Message', 'Unknown error')
                logger.error(f"❌ 任务提交失败: {error_msg}")
                return {"success": False, "error": error_msg}
                
        except Exception as e:
            logger.error(f"❌ 任务提交异常: {e}")
            return {"success": False, "error": str(e)}
    
    def query_task_result(self, task_id: str, max_wait_time: int = 1800) -> Dict[str, Any]:
        """查询任务结果"""
        logger.info(f"🔄 查询任务结果: {task_id}")
        
        start_time = time.time()
        poll_interval = 30  # 30秒查询一次
        
        while time.time() - start_time < max_wait_time:
            try:
                # 创建查询请求
                query_request = self.CommonRequest()
                query_request.set_accept_format('json')
                query_request.set_domain('tingwu.cn-beijing.aliyuncs.com')
                query_request.set_version('2023-09-30')
                query_request.set_protocol_type('https')
                query_request.set_method('GET')
                query_request.set_uri_pattern(f'/openapi/tingwu/v2/tasks/{task_id}')
                query_request.add_header('Content-Type', 'application/json')
                
                response = self.client.do_action_with_exception(query_request)
                response_data = json.loads(response)
                
                if response_data.get('Code') != '0':
                    error_msg = response_data.get('Message', 'Query failed')
                    return {"success": False, "error": error_msg}
                
                task_status = response_data['Data']['TaskStatus']
                logger.info(f"📊 任务状态: {task_status}")
                
                if task_status == 'COMPLETED':
                    logger.info("🎉 任务完成！")
                    
                    # 获取转写结果URL
                    result_data = response_data['Data']
                    if 'Result' in result_data and 'Transcription' in result_data['Result']:
                        transcription_url = result_data['Result']['Transcription']
                        return {"success": True, "transcription_url": transcription_url}
                    else:
                        return {"success": False, "error": "No transcription result found"}
                        
                elif task_status == 'FAILED':
                    error_msg = response_data['Data'].get('ErrorMessage', 'Task failed')
                    logger.error(f"❌ 任务失败: {error_msg}")
                    return {"success": False, "error": error_msg}
                    
                elif task_status == 'ONGOING':
                    elapsed = time.time() - start_time
                    logger.info(f"⏳ 任务进行中... (已等待 {elapsed:.0f}秒)")
                    time.sleep(poll_interval)
                else:
                    logger.warning(f"⚠️ 未知任务状态: {task_status}")
                    time.sleep(poll_interval)
                    
            except Exception as e:
                logger.error(f"❌ 查询异常: {e}")
                time.sleep(poll_interval)
        
        logger.error("❌ 任务超时")
        return {"success": False, "error": "Task timeout"}
    
    def download_transcription_result(self, transcription_url: str) -> Dict[str, Any]:
        """下载转写结果"""
        logger.info("📥 下载转写结果...")
        
        try:
            response = requests.get(transcription_url)
            
            if response.status_code == 200:
                transcription_data = response.json()
                logger.info("✅ 转写结果下载成功")
                return {"success": True, "data": transcription_data}
            else:
                error_msg = f"HTTP {response.status_code}"
                logger.error(f"❌ 下载失败: {error_msg}")
                return {"success": False, "error": error_msg}
                
        except Exception as e:
            logger.error(f"❌ 下载异常: {e}")
            return {"success": False, "error": str(e)}
    
    def convert_to_volcengine_format(self, tongyi_data: Dict[str, Any]) -> Dict[str, Any]:
        """将通义听悟格式转换为火山引擎格式"""
        logger.info("🔄 转换数据格式...")
        
        try:
            # 提取段落数据
            if 'Transcription' not in tongyi_data or 'Paragraphs' not in tongyi_data['Transcription']:
                return {"success": False, "error": "Invalid data format"}
            
            paragraphs = tongyi_data['Transcription']['Paragraphs']
            utterances = []
            
            for paragraph in paragraphs:
                speaker_id = paragraph.get('SpeakerId', '1')
                words = paragraph.get('Words', [])
                
                if not words:
                    continue
                
                # 合并词语为完整文本
                text_parts = []
                start_time = None
                end_time = None
                
                for word in words:
                    text_parts.append(word.get('Text', ''))
                    
                    # 记录时间范围
                    word_start = word.get('Start', 0)
                    word_end = word.get('End', 0)
                    
                    if start_time is None or word_start < start_time:
                        start_time = word_start
                    if end_time is None or word_end > end_time:
                        end_time = word_end
                
                # 构建utterance
                utterance = {
                    "start_time": start_time or 0,
                    "end_time": end_time or 0,
                    "text": ''.join(text_parts),
                    "attribute": {
                        "speaker": speaker_id
                    }
                }
                
                utterances.append(utterance)
            
            # 构建火山引擎格式的返回数据
            volcengine_format = {
                "utterances": utterances
            }
            
            logger.info(f"✅ 格式转换完成: {len(utterances)} 个utterances")
            return {"success": True, "data": volcengine_format}
            
        except Exception as e:
            logger.error(f"❌ 格式转换失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _upload_audio_to_cos(self, local_file_path: str) -> Dict[str, Any]:
        """上传音频文件到COS存储"""
        try:
            import os
            import time

            # 使用现有的COS工具
            from src.utils.cos_uploader import get_cos_client, upload_to_cos
            from src.config.configuration import Configuration

            # 创建配置
            config = Configuration.from_runnable_config()

            # 检查COS配置
            if not all([config.tencent_secret_id, config.tencent_secret_key,
                       config.cos_region, config.cos_bucket]):
                return {
                    "success": False,
                    "error": "缺少腾讯云COS配置，无法上传音频文件"
                }

            # 创建COS客户端
            cos_client = get_cos_client(config)

            # 读取音频文件
            with open(local_file_path, 'rb') as f:
                audio_bytes = f.read()

            # 上传文件
            logger.info(f"上传音频文件到COS: {local_file_path}")
            audio_url = upload_to_cos(
                client=cos_client,
                file_bytes=audio_bytes,
                bucket=config.cos_bucket,
                region=config.cos_region,
                file_extension="wav"
            )

            logger.info(f"音频文件上传成功: {audio_url}")
            return {
                "success": True,
                "audio_url": audio_url
            }

        except Exception as e:
            logger.error(f"音频文件上传失败: {e}")
            return {
                "success": False,
                "error": f"文件上传失败: {str(e)}"
            }

    def process_audio(self, audio_url: str, language: str = "cn") -> Dict[str, Any]:
        """完整的音频处理流程"""
        logger.info("🎯 开始通义听悟音频处理流程...")

        # 检查是否是本地文件
        if audio_url.startswith("file://"):
            local_path = audio_url[7:]  # 移除 "file://" 前缀
            logger.info(f"检测到本地文件: {local_path}")
            logger.info("正在上传到COS存储...")

            # 上传到COS
            upload_result = self._upload_audio_to_cos(local_path)
            if not upload_result["success"]:
                return upload_result

            # 使用上传后的URL
            audio_url = upload_result["audio_url"]
            logger.info(f"使用COS URL: {audio_url}")

        # 1. 提交任务
        submit_result = self.submit_task(audio_url, language)
        if not submit_result["success"]:
            return submit_result

        task_id = submit_result["task_id"]

        # 2. 查询结果
        query_result = self.query_task_result(task_id)
        if not query_result["success"]:
            return query_result

        transcription_url = query_result["transcription_url"]

        # 3. 下载转写结果
        download_result = self.download_transcription_result(transcription_url)
        if not download_result["success"]:
            return download_result

        tongyi_data = download_result["data"]

        # 4. 转换格式
        convert_result = self.convert_to_volcengine_format(tongyi_data)
        if not convert_result["success"]:
            return convert_result

        volcengine_format_data = convert_result["data"]

        logger.info("🎉 通义听悟处理完成！")
        return {
            "success": True,
            "data": volcengine_format_data,
            "original_data": tongyi_data
        }

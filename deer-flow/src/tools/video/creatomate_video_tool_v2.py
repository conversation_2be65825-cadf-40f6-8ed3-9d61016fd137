# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
Creatomate专业视频制作工具 V2 - 集成AI JSON专家

这是新一代的Creatomate工具，采用双层Agent架构：
1. 上层Agent提供宽松、语义化的输入
2. 内置JSON专家Agent（基于Gemini）负责转换为标准JSON
3. 底层执行引擎调用Creatomate API完成视频制作

特点：
- AI友好的输入接口
- 智能JSON生成和验证  
- 完整的错误恢复机制
- 自动下载和状态监控
"""

import asyncio
import requests
import time
import logging
import os
import json
import tempfile
import uuid
from functools import partial
from typing import Optional, Dict, Any, List, Literal, Union
from pathlib import Path

from pydantic.v1 import BaseModel, Field, validator
from langchain_core.tools import StructuredTool

from src.config.configuration import Configuration
from .creatomate_json_agent import convert_to_creatomate_json

# 配置日志
logger = logging.getLogger(__name__)

# === Input Models ===

class CreatomateVideoInput(BaseModel):
    """Creatomate视频制作工具输入参数 - AI友好版本"""
    
    # 输入模式选择
    input_mode: Literal["scenes", "custom_json", "natural_language"] = Field(
        default="scenes",
        description="输入模式：scenes=场景描述，custom_json=自定义JSON，natural_language=自然语言描述"
    )
    
    # 场景模式输入（推荐）
    scenes: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="""
        场景数组，每个场景可包含：
        - duration: 持续时间（秒）
        - background_video: 背景视频URL
        - overlay_video: 覆盖视频（可指定位置）
        - audio: 音频URL
        - subtitle/text: 显示文字
        - style: 风格设置
        
        示例：
        [
            {
                "duration": 3,
                "background_video": "URL",
                "audio": "音频URL",
                "subtitle": "文字内容"
            }
        ]
        """
    )
    
    # 自然语言描述
    description: Optional[str] = Field(
        default=None,
        description="自然语言描述视频需求，例如：'制作一个3秒的鬼畜视频，背景是猩猩跳舞，配上宋丹丹的音频'"
    )
    
    # 自定义JSON（高级用户）
    custom_json: Optional[Dict[str, Any]] = Field(
        default=None,
        description="自定义的Creatomate JSON配置，会直接传递给API"
    )
    
    # 全局设置
    video_width: int = Field(default=1920, description="视频宽度")
    video_height: int = Field(default=1080, description="视频高度")
    output_format: Literal["mp4", "gif", "png"] = Field(default="mp4", description="输出格式")
    auto_download: bool = Field(default=True, description="是否自动下载到本地")
    
    @validator('input_mode', pre=True)
    def validate_input_mode(cls, v, values):
        """验证输入模式和对应参数"""
        if v == "scenes" and not values.get('scenes'):
            raise ValueError("scenes模式需要提供scenes参数")
        elif v == "natural_language" and not values.get('description'):
            raise ValueError("natural_language模式需要提供description参数")
        elif v == "custom_json" and not values.get('custom_json'):
            raise ValueError("custom_json模式需要提供custom_json参数")
        return v

# === Core Implementation ===

class CreatomateVideoProcessor:
    """Creatomate视频处理器 - 核心业务逻辑"""
    
    def __init__(self, config: Configuration):
        self.config = config
        self.api_key = getattr(config, 'creatomate_api_key', None)
        self.api_url = "https://api.creatomate.com/v1/renders"
        self.download_path = getattr(config, 'creatomate_download_path', tempfile.gettempdir())
        
        if not self.api_key:
            raise ValueError("Creatomate API密钥未配置")
    
    async def create_video(self, input_args: CreatomateVideoInput) -> str:
        """
        创建视频的主要流程
        
        Returns:
            本地文件路径或视频URL
        """
        try:
            logger.info(f"🚀 开始Creatomate视频制作 (模式: {input_args.input_mode})")
            
            # 步骤1: 生成标准JSON配置
            json_config = await self._generate_json_config(input_args)
            
            # 步骤2: 调用Creatomate API
            render_id = await self._create_render_task(json_config)
            
            # 步骤3: 监控渲染进度
            video_url = await self._wait_for_completion(render_id)
            
            # 步骤4: 下载到本地（可选）
            if input_args.auto_download:
                local_path = await self._download_video(video_url, render_id)
                return local_path
            else:
                return video_url
                
        except Exception as e:
            logger.error(f"❌ 视频制作失败: {str(e)}")
            raise
    
    async def _generate_json_config(self, input_args: CreatomateVideoInput) -> Dict[str, Any]:
        """生成标准Creatomate JSON配置"""
        
        if input_args.input_mode == "custom_json":
            # 直接使用自定义JSON
            logger.info("📋 使用自定义JSON配置")
            return input_args.custom_json
        
        elif input_args.input_mode == "scenes":
            # 场景模式 - 构建输入结构
            logger.info(f"🎬 场景模式：{len(input_args.scenes)}个场景")
            loose_input = {
                "video_config": {
                    "width": input_args.video_width,
                    "height": input_args.video_height,
                    "output_format": input_args.output_format
                },
                "scenes": input_args.scenes
            }
            
        elif input_args.input_mode == "natural_language":
            # 自然语言模式
            logger.info(f"💬 自然语言模式：{input_args.description[:50]}...")
            loose_input = {
                "description": input_args.description,
                "video_config": {
                    "width": input_args.video_width,
                    "height": input_args.video_height,
                    "output_format": input_args.output_format
                }
            }
        
        # 调用JSON专家Agent进行转换
        logger.info("🧠 调用JSON专家Agent转换配置...")
        json_config = await convert_to_creatomate_json(self.config, loose_input)
        
        logger.info(f"✅ JSON配置生成成功，包含{len(json_config.get('source', {}).get('elements', []))}个元素")
        return json_config
    
    async def _create_render_task(self, json_config: Dict[str, Any]) -> str:
        """创建渲染任务"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
        }
        
        logger.info("📡 创建Creatomate渲染任务...")
        logger.debug(f"请求配置: {json.dumps(json_config, ensure_ascii=False, indent=2)}")
        
        response = requests.post(
            self.api_url,
            headers=headers,
            json=json_config,
            timeout=30
        )
        
        if response.status_code in [200, 201, 202]:
            render_data = response.json()
            
            # 处理不同的响应格式
            if isinstance(render_data, list) and len(render_data) > 0:
                render_data = render_data[0]
            
            render_id = render_data.get('id')
            if not render_id:
                raise ValueError("响应中未找到渲染任务ID")
            
            logger.info(f"✅ 渲染任务创建成功 (ID: {render_id})")
            return render_id
        else:
            error_msg = f"创建渲染任务失败: {response.status_code} - {response.text}"
            logger.error(error_msg)
            raise ValueError(error_msg)
    
    async def _wait_for_completion(self, render_id: str, max_wait: int = 600) -> str:
        """等待渲染完成"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
        }
        
        logger.info(f"⏳ 等待渲染完成 (最大等待{max_wait}秒)...")
        start_time = time.time()
        check_count = 0
        
        while time.time() - start_time < max_wait:
            check_count += 1
            elapsed = time.time() - start_time
            
            try:
                logger.info(f"🔍 第{check_count}次检查状态 (已等待{elapsed:.1f}秒)...")
                
                response = requests.get(
                    f"{self.api_url}/{render_id}",
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code == 200:
                    render_info = response.json()
                    
                    # 处理响应格式
                    if isinstance(render_info, list) and len(render_info) > 0:
                        render_info = render_info[0]
                    
                    status = render_info.get('status')
                    logger.info(f"📊 当前状态: {status}")
                    
                    if status == 'succeeded':
                        video_url = render_info.get('url')
                        if video_url:
                            logger.info(f"🎉 渲染成功完成！URL: {video_url}")
                            return video_url
                        else:
                            raise ValueError("渲染成功但未获取到视频URL")
                    
                    elif status == 'failed':
                        error = render_info.get('error', '未知错误')
                        raise ValueError(f"渲染失败: {error}")
                    
                    elif status in ['pending', 'processing', 'rendering']:
                        wait_time = min(10, max_wait - elapsed)
                        if wait_time > 0:
                            await asyncio.sleep(wait_time)
                        else:
                            break
                    else:
                        logger.warning(f"⚠️ 未知状态: {status}")
                        await asyncio.sleep(10)
                else:
                    logger.error(f"❌ 获取状态失败: {response.status_code}")
                    await asyncio.sleep(10)
                    
            except Exception as e:
                logger.warning(f"⚠️ 检查状态时出错: {str(e)}")
                await asyncio.sleep(10)
        
        raise TimeoutError(f"渲染超时，等待时间超过{max_wait}秒")
    
    async def _download_video(self, video_url: str, render_id: str) -> str:
        """下载视频到本地"""
        try:
            # 确保下载目录存在
            os.makedirs(self.download_path, exist_ok=True)
            
            # 生成文件名
            timestamp = int(time.time())
            filename = f"creatomate_video_{render_id}_{timestamp}.mp4"
            local_path = os.path.join(self.download_path, filename)
            
            logger.info(f"📥 开始下载视频到: {local_path}")
            
            response = requests.get(video_url, stream=True, timeout=300)
            response.raise_for_status()
            
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            # 验证文件
            file_size = os.path.getsize(local_path)
            logger.info(f"✅ 下载完成！文件大小: {file_size:,} bytes")
            
            return local_path
            
        except Exception as e:
            logger.error(f"❌ 下载失败: {str(e)}")
            return video_url  # 返回原始URL作为备选

# === Tool Factory ===

def get_creatomate_video_tool_v2(config: Configuration) -> Optional[StructuredTool]:
    """
    创建Creatomate视频制作工具V2
    """
    logger.info("🔧 初始化Creatomate视频工具V2...")
    
    # 检查配置
    if not hasattr(config, 'creatomate_api_key') or not config.creatomate_api_key:
        logger.warning("❌ Creatomate工具未启用：缺少API密钥配置")
        return None
    
    processor = CreatomateVideoProcessor(config)
    
    # 同步包装器
    def _sync_create_video(**kwargs):
        try:
            input_args = CreatomateVideoInput(**kwargs)
            return asyncio.run(processor.create_video(input_args))
        except Exception as e:
            return f"❌ Creatomate视频制作失败: {str(e)}"
    
    return StructuredTool.from_function(
        func=_sync_create_video,
        coroutine=partial(processor.create_video),
        name="creatomate_video_maker_v2",
        description=(
            "Creatomate专业视频制作工具V2 - AI驱动的智能视频生成\n\n"
            "**🎯 核心特性:**\n"
            "• **AI友好接口**: 支持场景描述、自然语言、自定义JSON三种输入模式\n"
            "• **智能JSON生成**: 内置Gemini驱动的JSON专家，自动转换复杂配置\n"
            "• **专业视频制作**: 支持多轨道合成、精确时间控制、专业特效\n"
            "• **自动化流程**: 一键完成渲染创建→状态监控→自动下载\n\n"
            "**📋 输入模式详解:**\n\n"
            "**1. 场景模式 (推荐)** - 适合大多数视频制作需求\n"
            "```python\n"
            "{\n"
            "    'input_mode': 'scenes',\n"
            "    'scenes': [\n"
            "        {\n"
            "            'duration': 3,\n"
            "            'background_video': '背景视频URL',\n"
            "            'audio': '音频URL',\n"
            "            'subtitle': '显示文字',\n"
            "            'style': '风格设置'\n"
            "        }\n"
            "    ]\n"
            "}\n"
            "```\n\n"
            "**2. 自然语言模式** - 最简单的使用方式\n"
            "```python\n"
            "{\n"
            "    'input_mode': 'natural_language',\n"
            "    'description': '制作一个3秒的鬼畜视频，背景是猩猩跳舞，配上宋丹丹的音频，显示\"现在有钱\"字幕'\n"
            "}\n"
            "```\n\n"
            "**3. 自定义JSON模式** - 高级用户直接控制\n"
            "```python\n"
            "{\n"
            "    'input_mode': 'custom_json',\n"
            "    'custom_json': {完整的Creatomate JSON配置}\n"
            "}\n"
            "```\n\n"
            "**🎬 支持的元素类型:**\n"
            "• **视频**: 背景视频、覆盖视频、人物视频\n"
            "• **音频**: 背景音乐、配音、音效\n"
            "• **文本**: 标题、字幕、说明文字\n"
            "• **图片**: 静态图片、Logo、装饰元素\n"
            "• **动画**: 淡入淡出、移动、缩放、旋转\n\n"
            "**⚙️ 输出配置:**\n"
            "• 格式: MP4/GIF/PNG\n"
            "• 分辨率: 自定义宽高（默认1920x1080）\n"
            "• 质量: 专业级云端渲染\n"
            "• 下载: 自动下载到本地或返回URL\n\n"
            "**🔧 技术优势:**\n"
            "• **零配置**: AI自动处理复杂的时间轴和轨道分配\n"
            "• **智能修复**: 自动检测和修复配置错误\n"
            "• **进度监控**: 实时反馈渲染状态和进度\n"
            "• **错误恢复**: 完善的重试和错误处理机制\n\n"
            "**💡 使用建议:**\n"
            "1. 新手推荐使用'scenes'模式，结构清晰易懂\n"
            "2. 快速原型可以使用'natural_language'模式\n"
            "3. 复杂项目可以使用'custom_json'模式精确控制\n"
            "4. 建议开启auto_download获取本地文件\n\n"
            "**📚 参考资源:**\n"
            "• Creatomate官方文档: https://creatomate.com/docs\n"
            "• JSON配置示例: 查看工具代码中的测试用例\n"
            "• 最佳实践: 遵循轨道分配和时间轴规则"
        ),
        args_schema=CreatomateVideoInput,
    )


# === 测试代码 ===
if __name__ == "__main__":
    async def test_creatomate_tool():
        """测试Creatomate工具V2"""
        from src.config.configuration import Configuration
        
        # 模拟配置
        config = Configuration()
        config.creatomate_api_key = "test_key"
        
        processor = CreatomateVideoProcessor(config)
        
        # 测试场景模式
        test_input = CreatomateVideoInput(
            input_mode="scenes",
            scenes=[
                {
                    "duration": 3,
                    "background_video": "https://example.com/monkey.mp4",
                    "audio": "https://example.com/audio.mp3",
                    "subtitle": "现在有钱"
                },
                {
                    "duration": 4,
                    "background_video": "继续猩猩",
                    "overlay_video": {"url": "https://example.com/nezha.mp4", "position": "right"},
                    "subtitle": "瞅着穿的相当有钱"
                }
            ]
        )
        
        try:
            # 只测试JSON生成部分
            json_config = await processor._generate_json_config(test_input)
            print("✅ JSON生成测试成功:")
            print(json.dumps(json_config, ensure_ascii=False, indent=2))
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
    
    # 运行测试
    asyncio.run(test_creatomate_tool())

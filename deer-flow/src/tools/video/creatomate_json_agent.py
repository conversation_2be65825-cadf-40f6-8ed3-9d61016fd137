# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
Creatomate JSON专家Agent - 基于Gemini的智能JSON生成器

这个Agent专门负责将自然语言描述或宽松的JSON结构转换为标准的Creatomate JSON配置。
使用Google Gemini模型，结合专业的Prompt Engineering来确保生成高质量的视频配置。
"""

import json
import logging
import asyncio
from typing import Dict, Any, Optional, List, Union
from pydantic.v1 import BaseModel, Field

from src.config.configuration import Configuration  
from src.llms.llm import get_llm_by_type
from src.config.agents import LLMType

logger = logging.getLogger(__name__)

class CreatomateJSONAgent:
    """Creatomate JSON专家Agent - 基于Gemini的智能转换器"""
    
    def __init__(self, config: Configuration):
        self.config = config
        # 使用系统统一的LLM配置，使用reasoning级别的模型来保证质量
        self.llm = get_llm_by_type("reasoning")
        
        # Creatomate JSON语法规则 - 详细完整版
        self.json_rules = """
## Creatomate JSON语法规则 - 完整专家指南

### 🏗️ 基础结构模板（必须严格遵循）
```json
{
  "source": {
    "output_format": "mp4",
    "width": 1920,
    "height": 1080,
    "duration": 总时长(秒),
    "elements": [
      // 所有元素必须有id、type、track
      {
        "id": "unique_element_id",
        "type": "video|audio|text|image|shape|composition",
        "track": 轨道编号,
        "time": 开始时间(秒),
        "duration": 持续时间(秒),
        // ...其他属性
      }
    ]
  }
}
```

### 🎬 轨道系统规则（严格执行）
**轨道分配策略：**
- track 1: 背景视频/图片（最底层，通常占满屏幕）
- track 2: 主要覆盖视频/人物（中间层）
- track 3: 次要覆盖元素/图标/装饰
- track 4: 主音频/背景音乐
- track 5+: 文本/字幕层（最上层，每个文本独立轨道）

**重要规则：**
- 数字越大，层级越高（track 5 会覆盖 track 1）
- 同轨道元素按时间顺序播放，不能重叠
- 不同轨道元素可以并行播放

### ⏰ 时间轴系统规则（精确控制）
**时间计算原则：**
- time: 开始时间（秒），必须 >= 0
- duration: 持续时间（秒），必须 > 0
- 场景切换：下一场景的time = 上一场景的time + duration
- 跨场景元素：audio通常从0开始，跨越整个视频

**示例时间轴：**
```
场景1: time=0, duration=3    (0-3秒)
场景2: time=3, duration=4    (3-7秒)
音频: time=0, duration=7     (整个视频)
```

### 🧩 元素类型完整规范

#### 视频元素（完整属性）
```json
{
  "id": "video_element_1",
  "type": "video",
  "track": 1,
  "time": 0,
  "duration": 5,
  "source": "https://example.com/video.mp4",
  "volume": "0%",      // 0%-100%，通常背景视频静音
  "x": "50%",          // 水平位置
  "y": "50%",          // 垂直位置  
  "width": "100%",     // 宽度
  "height": "100%",    // 高度
  "x_alignment": "50%", // 对齐方式（可选）
  "y_alignment": "50%", // 对齐方式（可选）
  "opacity": 100       // 透明度0-100（可选）
}
```

#### 音频元素（完整属性）
```json
{
  "id": "audio_element_1",
  "type": "audio",
  "track": 4,
  "time": 0,
  "source": "https://example.com/audio.mp3",
  "volume": "80%",     // 主音频80-100%，背景音乐50-70%
  "loop": false,       // 是否循环
  "fade_in": "0.5 s",  // 淡入时间（可选）
  "fade_out": "0.5 s"  // 淡出时间（可选）
}
```

#### 文本元素（完整属性）
```json
{
  "id": "text_element_1", 
  "type": "text",
  "track": 5,
  "time": 0,
  "duration": 3,
  "text": "显示的文字内容",
  "font_family": "Arial",          // 字体：Arial, Microsoft YaHei, 等
  "font_size": 40,                 // 字号：标题50+，正文40，字幕35
  "font_weight": "700",            // 字重：400normal, 700bold
  "fill_color": "#ffffff",         // 文字颜色（白色常用）
  "stroke_color": "#333333",       // 描边颜色（黑色常用）
  "stroke_width": "2px",           // 描边宽度
  "x": "50%",                      // 水平位置
  "y": "80%",                      // 垂直位置（字幕通常80-90%）
  "x_alignment": "50%",            // 水平对齐：0%左对齐，50%居中，100%右对齐
  "y_alignment": "50%",            // 垂直对齐：0%顶部，50%居中，100%底部
  "background_color": "rgba(0,0,0,0.5)", // 背景色（可选，半透明常用）
  "background_x_padding": "10%",   // 背景内边距（可选）
  "background_y_padding": "5%",    // 背景内边距（可选）
  "line_height": "120%"            // 行高（多行文字）
}
```

### 📐 位置语义转换精确规则
**语义位置映射表：**
- "center": {"x": "50%", "y": "50%", "x_alignment": "50%", "y_alignment": "50%"}
- "top": {"y": "10%", "y_alignment": "0%"}
- "bottom": {"y": "85%", "y_alignment": "100%"}  
- "left": {"x": "15%", "x_alignment": "0%"}
- "right": {"x": "75%", "x_alignment": "0%", "width": "25%"}
- "top-left": {"x": "15%", "y": "10%", "x_alignment": "0%", "y_alignment": "0%"}
- "top-right": {"x": "75%", "y": "10%", "x_alignment": "0%", "y_alignment": "0%", "width": "25%"}
- "bottom-left": {"x": "15%", "y": "85%", "x_alignment": "0%", "y_alignment": "100%"}
- "bottom-right": {"x": "75%", "y": "85%", "x_alignment": "0%", "y_alignment": "100%", "width": "25%"}

### 🎨 样式配置标准
**文字样式等级：**
- 主标题：font_size: 60, y: "15%", font_weight: "700"
- 副标题：font_size: 45, y: "25%", font_weight: "600"  
- 正文：font_size: 40, y: "50%", font_weight: "400"
- 字幕：font_size: 35, y: "80%", font_weight: "500"

**音量配置标准：**
- 主配音/对话：volume: "100%"
- 背景音乐：volume: "60%"
- 音效：volume: "80%"
- 背景视频：volume: "0%" （通常静音）

**颜色配置标准：**
- 白色文字：fill_color: "#ffffff"
- 黑色描边：stroke_color: "#333333" 
- 半透明背景：background_color: "rgba(0,0,0,0.6)"
- 强调色：fill_color: "#ff6b35" （橙红色）

### 🎬 完整视频制作示例
**鬼畜视频标准模板：**
```json
{
  "source": {
    "output_format": "mp4", 
    "width": 1920,
    "height": 1080,
    "duration": 7,
    "elements": [
      {
        "id": "background_video",
        "type": "video",
        "track": 1,
        "time": 0,
        "duration": 7,
        "source": "背景视频URL",
        "volume": "0%",
        "x": "50%",
        "y": "50%",
        "width": "100%", 
        "height": "100%"
      },
      {
        "id": "overlay_character",
        "type": "video",
        "track": 2,
        "time": 3,
        "duration": 4,
        "source": "人物视频URL",
        "volume": "0%",
        "x": "75%",
        "y": "50%", 
        "width": "25%",
        "height": "60%",
        "x_alignment": "50%",
        "y_alignment": "50%"
      },
      {
        "id": "main_audio",
        "type": "audio",
        "track": 4,
        "time": 0,
        "source": "音频URL",
        "volume": "85%"
      },
      {
        "id": "subtitle_1",
        "type": "text", 
        "track": 5,
        "time": 0,
        "duration": 3,
        "text": "现在有钱",
        "font_family": "Microsoft YaHei",
        "font_size": 40,
        "font_weight": "700",
        "fill_color": "#ffffff",
        "stroke_color": "#333333",
        "stroke_width": "2px",
        "x": "50%",
        "y": "85%",
        "x_alignment": "50%",
        "y_alignment": "100%",
        "background_color": "rgba(0,0,0,0.6)",
        "background_x_padding": "8%",
        "background_y_padding": "3%"
      },
      {
        "id": "subtitle_2",
        "type": "text",
        "track": 6, 
        "time": 3,
        "duration": 4,
        "text": "瞅着穿的相当有钱",
        "font_family": "Microsoft YaHei",
        "font_size": 40,
        "font_weight": "700", 
        "fill_color": "#ffffff",
        "stroke_color": "#333333",
        "stroke_width": "2px",
        "x": "50%",
        "y": "85%",
        "x_alignment": "50%",
        "y_alignment": "100%",
        "background_color": "rgba(0,0,0,0.6)",
        "background_x_padding": "8%", 
        "background_y_padding": "3%"
      }
    ]
  }
}
```

### ⚠️ 关键注意事项
1. **每个元素必须有唯一的id**
2. **track轨道不能冲突**（同轨道元素不能时间重叠）
3. **时间轴必须逻辑正确**（time + duration 不能超出总duration）
4. **位置使用百分比**（兼容不同分辨率）
5. **文字必须有足够对比度**（白字配黑描边，或半透明背景）
6. **音量级别要合理**（避免过大或过小）
7. **字体选择要安全**（Arial, Microsoft YaHei 等系统字体）
"""

    async def convert_to_creatomate_json(self, loose_input: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        将宽松输入转换为标准Creatomate JSON
        
        Args:
            loose_input: 自然语言描述或宽松的JSON结构
            
        Returns:
            标准的Creatomate JSON配置
        """
        try:
            # 构建专家级Prompt
            prompt = self._build_expert_prompt(loose_input)
            
            # 调用Gemini生成JSON
            response = await self.llm.ainvoke(prompt)
            
            # 解析和验证JSON
            json_config = self._parse_and_validate_json(response.content)
            
            # 应用工程保障
            final_config = self._apply_engineering_safeguards(json_config)
            
            logger.info(f"✅ JSON转换成功，生成{len(final_config.get('source', {}).get('elements', []))}个元素")
            return final_config
            
        except Exception as e:
            logger.error(f"❌ JSON转换失败: {str(e)}")
            raise

    def _build_expert_prompt(self, loose_input: Union[str, Dict[str, Any]]) -> str:
        """构建专家级Prompt"""
        
        input_str = json.dumps(loose_input, ensure_ascii=False, indent=2) if isinstance(loose_input, dict) else str(loose_input)
        
        prompt = f"""
你是世界顶级的Creatomate视频制作JSON专家，拥有丰富的视频剪辑和编程经验。你的任务是将用户的视频需求转换为完美的Creatomate JSON配置。

## 📋 你的核心专业知识库：
{self.json_rules}

## 🎯 用户输入内容：
```
{input_str}
```

## ⚡ 核心转换原则：

### 1. 🔍 输入分析阶段
- **深度理解**：仔细分析用户的视频需求和场景描述
- **元素识别**：识别所有视频、音频、文字元素及其关系
- **时间规划**：计算每个场景的时间安排和元素持续时间

### 2. 🎬 JSON构建阶段
- **结构完整**：必须包含完整的source结构和所有必需字段
- **ID唯一性**：每个元素都要有描述性的唯一ID (如background_video, subtitle_1等)
- **轨道分配**：严格按照轨道规则分配 (track 1=背景, track 2=覆盖, track 4=音频, track 5+=文字)
- **时间精确**：计算精确的time和duration，确保逻辑正确

### 3. 🎨 样式优化阶段
- **位置计算**：将语义位置转换为精确的百分比坐标
- **字体选择**：使用安全的系统字体（Microsoft YaHei用于中文，Arial用于英文）
- **颜色搭配**：确保文字有足够对比度（白字+黑描边+半透明背景）
- **音量平衡**：主音频85%，背景音乐60%，视频静音0%

### 4. 🛡️ 质量保证阶段
- **兼容性检查**：确保所有属性符合Creatomate API规范
- **逻辑验证**：验证时间轴无冲突，轨道分配合理
- **完整性检查**：确保没有遗漏的必需属性

## 🎯 特殊场景处理策略：

### 鬼畜视频 (如示例场景)：
- 背景视频：track 1, 全程播放, 静音
- 人物覆盖：track 2, 适时出现, 右侧位置
- 音频节拍：track 4, 跨场景连续
- 文字字幕：track 5+, 配合音频节拍, 底部居中

### 产品宣传视频：
- 产品展示：track 1, 主要视频
- Logo水印：track 2, 右上角小尺寸
- 背景音乐：track 4, 60%音量, 循环
- 标题文字：track 5, 顶部大字号
- 说明文字：track 6, 底部小字号

### 教学演示视频：
- 屏幕录制：track 1, 全屏显示
- 讲师画面：track 2, 右下角小窗
- 配音解说：track 4, 100%音量
- 重点标注：track 5+, 动态文字

## 📊 输出格式要求：

**绝对要求：**
1. 只输出纯JSON，不要任何markdown标记或说明文字
2. JSON必须格式正确，可以直接被解析
3. 所有字符串值必须用双引号包围
4. 数值不要引号（除非是百分比字符串如"80%"）
5. 确保JSON结构完整，不能有语法错误

**质量标准：**
- 每个元素都有合理的id命名
- 轨道分配科学合理，无冲突
- 时间轴逻辑清晰，无重叠错误
- 位置和尺寸精确，适配1920x1080
- 文字样式专业，易读性强
- 音量搭配合理，听觉体验佳

## 💡 成功案例参考：
参考上述完整示例中的鬼畜视频模板，它展示了：
- 完美的轨道分配策略
- 精确的时间轴控制
- 专业的文字样式设置
- 合理的音量配置
- 标准的位置布局

现在，基于以上所有专业知识和要求，请将用户输入转换为完美的Creatomate JSON配置：
"""
        return prompt

    def _parse_and_validate_json(self, response_content: str) -> Dict[str, Any]:
        """解析和验证JSON响应"""
        try:
            # 清理响应内容
            content = response_content.strip()
            
            # 移除可能的markdown标记
            if content.startswith("```json"):
                content = content[7:]
            if content.startswith("```"):
                content = content[3:]
            if content.endswith("```"):
                content = content[:-3]
            
            content = content.strip()
            
            # 解析JSON
            json_config = json.loads(content)
            
            # 基础验证
            if not isinstance(json_config, dict):
                raise ValueError("响应不是有效的JSON对象")
                
            if "source" not in json_config:
                raise ValueError("缺少必需的'source'字段")
                
            source = json_config["source"]
            if "elements" not in source:
                raise ValueError("缺少必需的'elements'字段")
                
            if not isinstance(source["elements"], list):
                raise ValueError("'elements'必须是数组")
                
            logger.info(f"✅ JSON验证通过，包含{len(source['elements'])}个元素")
            return json_config
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON解析失败: {str(e)}")
            logger.error(f"原始内容: {response_content}")
            raise ValueError(f"无效的JSON格式: {str(e)}")
        except Exception as e:
            logger.error(f"❌ JSON验证失败: {str(e)}")
            raise

    def _apply_engineering_safeguards(self, json_config: Dict[str, Any]) -> Dict[str, Any]:
        """应用完整的工程保障机制"""
        source = json_config["source"]
        
        # 1. 基础配置保障
        source.setdefault("output_format", "mp4")
        source.setdefault("width", 1920)
        source.setdefault("height", 1080)
        
        # 2. 元素数组验证
        if "elements" not in source or not isinstance(source["elements"], list):
            raise ValueError("缺少有效的elements数组")
        
        elements = source["elements"]
        
        # 3. 计算总时长（更精确的算法）
        max_end_time = 0
        for element in elements:
            start_time = element.get("time", 0)
            duration = element.get("duration", 0)
            if duration > 0:  # 只考虑有持续时间的元素
                end_time = start_time + duration
                max_end_time = max(max_end_time, end_time)
        
        # 为audio元素计算特殊时长
        for element in elements:
            if element.get("type") == "audio" and "duration" not in element:
                # 音频通常跨越整个视频
                if max_end_time > 0:
                    element.setdefault("duration", max_end_time)
        
        # 更新总时长
        if max_end_time > 0:
            source.setdefault("duration", max_end_time)
        
        # 4. 元素级别的完整性保障
        track_usage = {}  # 追踪轨道使用情况
        
        for i, element in enumerate(elements):
            # 基础字段保障
            element.setdefault("id", f"element_{i+1}")
            
            # 类型验证
            element_type = element.get("type")
            if element_type not in ["video", "audio", "text", "image", "shape", "composition"]:
                logger.warning(f"⚠️ 元素{i}类型可能无效: {element_type}")
            
            # 智能轨道分配
            if "track" not in element:
                if element_type == "video":
                    # 第一个video用track 1，后续用track 2, 3...
                    video_count = sum(1 for e in elements[:i] if e.get("type") == "video")
                    element["track"] = 1 if video_count == 0 else 2 + video_count
                elif element_type == "audio":
                    element["track"] = 4
                elif element_type == "text":
                    # 文本从track 5开始，每个独立轨道
                    text_count = sum(1 for e in elements[:i] if e.get("type") == "text")
                    element["track"] = 5 + text_count
                else:
                    element["track"] = 3  # 其他类型默认track 3
            
            # 轨道冲突检测
            track = element["track"]
            start_time = element.get("time", 0)
            duration = element.get("duration", 0)
            
            if track in track_usage:
                # 检查时间重叠
                for existing_start, existing_end in track_usage[track]:
                    if not (start_time >= existing_end or start_time + duration <= existing_start):
                        logger.warning(f"⚠️ 轨道{track}时间冲突：{start_time}-{start_time + duration} vs {existing_start}-{existing_end}")
            
            track_usage.setdefault(track, []).append((start_time, start_time + duration))
            
            # 时间默认值
            element.setdefault("time", 0)
            
            # 5. 类型特定的样式保障
            if element_type == "text":
                self._apply_text_safeguards(element)
            elif element_type == "video":
                self._apply_video_safeguards(element)
            elif element_type == "audio":
                self._apply_audio_safeguards(element)
        
        # 6. 全局质量检查
        self._validate_final_config(source)
        
        logger.info(f"✅ 工程保障完成：{len(elements)}个元素，{len(track_usage)}条轨道")
        return json_config
    
    def _apply_text_safeguards(self, element: Dict[str, Any]):
        """文本元素专用保障"""
        # 字体安全
        element.setdefault("font_family", "Microsoft YaHei")  # 中文友好
        
        # 字号分级
        if "font_size" not in element:
            text_content = element.get("text", "")
            if len(text_content) <= 4:  # 短文字用大字号
                element["font_size"] = 45
            elif len(text_content) <= 8:  # 中等文字
                element["font_size"] = 40
            else:  # 长文字用小字号
                element["font_size"] = 35
        
        # 样式保障
        element.setdefault("font_weight", "700")
        element.setdefault("fill_color", "#ffffff")
        element.setdefault("stroke_color", "#333333") 
        element.setdefault("stroke_width", "2px")
        
        # 位置保障
        element.setdefault("x", "50%")
        element.setdefault("y", "85%")  # 字幕位置
        element.setdefault("x_alignment", "50%")
        element.setdefault("y_alignment", "100%")
        
        # 背景保障（提高可读性）
        element.setdefault("background_color", "rgba(0,0,0,0.6)")
        element.setdefault("background_x_padding", "8%")
        element.setdefault("background_y_padding", "3%")
        
        # 持续时间检查
        if "duration" not in element or element["duration"] <= 0:
            element["duration"] = 3  # 默认3秒
    
    def _apply_video_safeguards(self, element: Dict[str, Any]):
        """视频元素专用保障"""
        # 基础定位
        element.setdefault("x", "50%")
        element.setdefault("y", "50%")
        element.setdefault("x_alignment", "50%")
        element.setdefault("y_alignment", "50%")
        
        # 尺寸保障
        track = element.get("track", 1)
        if track == 1:  # 背景视频
            element.setdefault("width", "100%")
            element.setdefault("height", "100%")
            element.setdefault("volume", "0%")  # 背景视频通常静音
        elif track == 2:  # 覆盖视频
            element.setdefault("width", "30%")
            element.setdefault("height", "40%")
            element.setdefault("volume", "0%")
        
        # 音量保障
        if "volume" not in element:
            element["volume"] = "0%"  # 默认静音
    
    def _apply_audio_safeguards(self, element: Dict[str, Any]):
        """音频元素专用保障"""
        # 音量分级
        if "volume" not in element:
            track = element.get("track", 4)
            if track == 4:  # 主音频
                element["volume"] = "85%"
            else:  # 背景音频
                element["volume"] = "60%"
        
        # 音频通常不需要位置信息，移除可能的错误属性
        for key in ["x", "y", "width", "height"]:
            if key in element:
                del element[key]
    
    def _validate_final_config(self, source: Dict[str, Any]):
        """最终配置验证"""
        # 检查必需字段
        required_fields = ["output_format", "width", "height", "elements"]
        for field in required_fields:
            if field not in source:
                raise ValueError(f"缺少必需字段: {field}")
        
        # 检查元素完整性
        for i, element in enumerate(source["elements"]):
            required_element_fields = ["id", "type", "track"]
            for field in required_element_fields:
                if field not in element:
                    raise ValueError(f"元素{i}缺少必需字段: {field}")
        
        logger.info("✅ 最终配置验证通过")

# 简化接口函数
async def convert_to_creatomate_json(config: Configuration, loose_input: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
    """
    简化的接口函数，将宽松输入转换为Creatomate JSON
    
    Args:
        config: 系统配置
        loose_input: 宽松的输入（自然语言或简化JSON）
        
    Returns:
        标准Creatomate JSON配置
    """
    agent = CreatomateJSONAgent(config)
    return await agent.convert_to_creatomate_json(loose_input)


# 测试代码
if __name__ == "__main__":
    async def test_json_agent():
        from src.config.configuration import Configuration
        
        # 模拟配置
        config = Configuration()
        
        # 测试用例1：简化的场景描述
        test_input = {
            "scenes": [
                {
                    "duration": 3,
                    "background_video": "猩猩跳舞URL",
                    "audio": "宋丹丹音频URL", 
                    "subtitle": "现在有钱"
                },
                {
                    "duration": 4,
                    "background_video": "继续猩猩",
                    "overlay_video": {"url": "哪吒URL", "position": "right"},
                    "subtitle": "瞅着穿的相当有钱"
                }
            ]
        }
        
        try:
            result = await convert_to_creatomate_json(config, test_input)
            print("✅ 转换成功:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        except Exception as e:
            print(f"❌ 转换失败: {str(e)}")
    
    # 运行测试
    asyncio.run(test_json_agent())

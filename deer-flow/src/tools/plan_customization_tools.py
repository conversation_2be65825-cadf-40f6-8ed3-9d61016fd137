#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
plan_customization_tools.py

为主agent提供计划自定义工具，在现有系统基础上增加灵活性。
"""

from typing import Dict, Any, List, Optional
from langchain_core.tools import tool
from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
import json


# 全局变量存储当前计划（简化实现）
_current_plan: Optional[UnifiedPlan] = None


@tool
def modify_plan_step(
    step_id: str,
    modifications: Dict[str, Any],
    reason: Optional[str] = None
) -> Dict[str, Any]:
    """
    修改计划中的指定步骤
    
    Args:
        step_id: 要修改的步骤ID
        modifications: 修改内容，可包含 name, description, tool_to_use, inputs 等
        reason: 修改原因（可选）
    
    Returns:
        修改结果和建议
    
    Examples:
        modify_plan_step("create_script", {
            "name": "创意剧本构思",
            "description": "融入更多东北方言特色的剧本创作",
            "inputs": {"style": "更加幽默"}
        })
    """
    
    global _current_plan
    
    if not _current_plan:
        return {"error": "没有找到当前计划，请先使用模板生成计划"}
    
    # 查找目标步骤
    target_step = None
    step_index = -1
    for i, step in enumerate(_current_plan.steps):
        if step.step_id == step_id:
            target_step = step
            step_index = i
            break
    
    if not target_step:
        return {"error": f"步骤 '{step_id}' 不存在"}
    
    # 记录原始值
    original_values = {}
    
    # 应用修改
    modified_aspects = []
    for key, value in modifications.items():
        if hasattr(target_step, key):
            original_values[key] = getattr(target_step, key)
            setattr(target_step, key, value)
            modified_aspects.append(key)
        elif key == "inputs" and hasattr(target_step, "inputs"):
            # 特殊处理inputs字段
            original_values["inputs"] = target_step.inputs.copy()
            if isinstance(value, dict):
                target_step.inputs.update(value)
            else:
                target_step.inputs = value
            modified_aspects.append("inputs")
    
    # 生成优化建议
    suggestions = []
    if "name" in modified_aspects:
        suggestions.append("考虑同时更新步骤描述以保持一致性")
    if "tool_to_use" in modified_aspects:
        suggestions.append("检查新工具是否需要不同的输入参数")
    if "inputs" in modified_aspects:
        suggestions.append("验证修改后的参数是否与工具兼容")
    
    return {
        "success": True,
        "step_id": step_id,
        "modified_aspects": modified_aspects,
        "original_values": original_values,
        "current_step": {
            "name": target_step.name,
            "description": target_step.description,
            "tool_to_use": target_step.tool_to_use,
            "inputs": target_step.inputs
        },
        "suggestions": suggestions,
        "reason": reason
    }


@tool
def add_plan_step(
    position: str,  # "before:step_id", "after:step_id", "beginning", "end"
    step_definition: Dict[str, Any],
    reason: Optional[str] = None
) -> Dict[str, Any]:
    """
    在计划中添加新步骤
    
    Args:
        position: 插入位置，格式如 "after:create_script" 或 "beginning"
        step_definition: 新步骤定义，包含 step_id, name, description, tool_to_use, inputs
        reason: 添加原因（可选）
    
    Returns:
        添加结果
    
    Examples:
        add_plan_step("after:create_script", {
            "step_id": "script_review",
            "name": "剧本审核优化",
            "description": "检查剧本质量并进行优化",
            "tool_to_use": "visual_expert",
            "inputs": {"task": "review_and_optimize"}
        })
    """
    
    global _current_plan
    
    if not _current_plan:
        return {"error": "没有找到当前计划，请先使用模板生成计划"}
    
    # 验证步骤定义
    required_fields = ["step_id", "name", "description", "tool_to_use"]
    missing_fields = [field for field in required_fields if field not in step_definition]
    if missing_fields:
        return {"error": f"步骤定义缺少必需字段: {missing_fields}"}
    
    # 创建新步骤
    new_step = UnifiedStep(
        step_id=step_definition["step_id"],
        name=step_definition["name"],
        description=step_definition["description"],
        tool_to_use=step_definition["tool_to_use"],
        inputs=step_definition.get("inputs", {}),
        dependencies=step_definition.get("dependencies", []),
        estimated_duration=step_definition.get("estimated_duration", 300)
    )
    
    # 确定插入位置
    insert_index = 0
    if position == "beginning":
        insert_index = 0
    elif position == "end":
        insert_index = len(_current_plan.steps)
    elif position.startswith("before:"):
        target_step_id = position.split(":", 1)[1]
        for i, step in enumerate(_current_plan.steps):
            if step.step_id == target_step_id:
                insert_index = i
                break
        else:
            return {"error": f"参考步骤 '{target_step_id}' 不存在"}
    elif position.startswith("after:"):
        target_step_id = position.split(":", 1)[1]
        for i, step in enumerate(_current_plan.steps):
            if step.step_id == target_step_id:
                insert_index = i + 1
                break
        else:
            return {"error": f"参考步骤 '{target_step_id}' 不存在"}
    else:
        return {"error": f"无效的位置格式: {position}"}
    
    # 插入新步骤
    _current_plan.steps.insert(insert_index, new_step)
    
    # 生成建议
    suggestions = []
    if new_step.dependencies:
        suggestions.append("检查依赖步骤是否在新步骤之前")
    if insert_index < len(_current_plan.steps) - 1:
        suggestions.append("检查后续步骤是否需要这个新步骤的输出")
    
    return {
        "success": True,
        "new_step": {
            "step_id": new_step.step_id,
            "name": new_step.name,
            "position": insert_index,
            "total_steps": len(_current_plan.steps)
        },
        "suggestions": suggestions,
        "reason": reason
    }


@tool
def remove_plan_step(
    step_id: str,
    reason: Optional[str] = None
) -> Dict[str, Any]:
    """
    从计划中删除指定步骤
    
    Args:
        step_id: 要删除的步骤ID
        reason: 删除原因（可选）
    
    Returns:
        删除结果和影响分析
    """
    
    global _current_plan
    
    if not _current_plan:
        return {"error": "没有找到当前计划，请先使用模板生成计划"}
    
    # 查找并删除步骤
    removed_step = None
    for i, step in enumerate(_current_plan.steps):
        if step.step_id == step_id:
            removed_step = _current_plan.steps.pop(i)
            break
    
    if not removed_step:
        return {"error": f"步骤 '{step_id}' 不存在"}
    
    # 分析影响
    affected_steps = []
    for step in _current_plan.steps:
        if step_id in step.dependencies:
            affected_steps.append({
                "step_id": step.step_id,
                "name": step.name,
                "issue": "依赖被删除的步骤"
            })
    
    # 生成建议
    suggestions = []
    if affected_steps:
        suggestions.append("需要更新依赖被删除步骤的其他步骤")
        suggestions.append("考虑是否需要替代步骤来提供必要的输出")
    
    return {
        "success": True,
        "removed_step": {
            "step_id": removed_step.step_id,
            "name": removed_step.name
        },
        "affected_steps": affected_steps,
        "remaining_steps": len(_current_plan.steps),
        "suggestions": suggestions,
        "reason": reason
    }


@tool
def reorder_plan_steps(
    new_order: List[str],
    reason: Optional[str] = None
) -> Dict[str, Any]:
    """
    重新排序计划步骤
    
    Args:
        new_order: 新的步骤ID顺序列表
        reason: 重排原因（可选）
    
    Returns:
        重排结果和依赖检查
    """
    
    global _current_plan
    
    if not _current_plan:
        return {"error": "没有找到当前计划，请先使用模板生成计划"}
    
    # 验证所有步骤ID都存在
    current_step_ids = {step.step_id for step in _current_plan.steps}
    new_order_set = set(new_order)
    
    if current_step_ids != new_order_set:
        missing = current_step_ids - new_order_set
        extra = new_order_set - current_step_ids
        return {
            "error": f"步骤ID不匹配。缺少: {missing}, 多余: {extra}"
        }
    
    # 创建步骤ID到步骤对象的映射
    step_map = {step.step_id: step for step in _current_plan.steps}
    
    # 重新排序
    _current_plan.steps = [step_map[step_id] for step_id in new_order]
    
    # 检查依赖关系
    dependency_issues = []
    for i, step in enumerate(_current_plan.steps):
        for dep in step.dependencies:
            # 查找依赖步骤的位置
            dep_index = -1
            for j, dep_step in enumerate(_current_plan.steps):
                if dep_step.step_id == dep:
                    dep_index = j
                    break
            
            if dep_index > i:
                dependency_issues.append({
                    "step": step.step_id,
                    "dependency": dep,
                    "issue": "依赖步骤在当前步骤之后"
                })
    
    return {
        "success": True,
        "new_order": new_order,
        "dependency_issues": dependency_issues,
        "suggestions": [
            "检查依赖关系是否仍然合理",
            "考虑是否需要调整某些步骤的依赖"
        ] if dependency_issues else ["步骤重排成功，依赖关系正常"],
        "reason": reason
    }


@tool
def get_plan_customization_suggestions(
    focus_area: Optional[str] = None  # "quality", "speed", "creativity", "efficiency"
) -> Dict[str, Any]:
    """
    获取计划自定义建议
    
    Args:
        focus_area: 关注领域（可选）
    
    Returns:
        个性化的自定义建议
    """
    
    global _current_plan
    
    if not _current_plan:
        return {"error": "没有找到当前计划，请先使用模板生成计划"}
    
    suggestions = {
        "general": [
            "可以调整步骤名称使其更具体和个性化",
            "可以根据需要更换工具以获得不同效果",
            "可以添加质量检查步骤确保输出质量",
            "可以调整步骤顺序优化工作流程"
        ],
        "step_specific": []
    }
    
    # 分析每个步骤并提供具体建议
    for step in _current_plan.steps:
        step_suggestions = []
        
        if "create" in step.step_id or "script" in step.step_id:
            step_suggestions.extend([
                "可以添加更多创意元素或个人风格",
                "可以调整内容复杂度和详细程度",
                "可以指定特定的风格或主题偏好"
            ])
        
        if "audio" in step.step_id or "voice" in step.step_id:
            step_suggestions.extend([
                "可以调整语音情感和语调",
                "可以添加背景音乐或音效",
                "可以优化音频质量设置"
            ])
        
        if "video" in step.step_id or "visual" in step.step_id:
            step_suggestions.extend([
                "可以调整视觉风格和效果",
                "可以添加转场和特效",
                "可以优化视频质量和格式"
            ])
        
        if step_suggestions:
            suggestions["step_specific"].append({
                "step_id": step.step_id,
                "step_name": step.name,
                "suggestions": step_suggestions
            })
    
    # 根据关注领域添加特定建议
    if focus_area:
        if focus_area == "quality":
            suggestions["focus_suggestions"] = [
                "在每个步骤后添加质量检查",
                "使用更高级的工具和参数",
                "增加迭代优化步骤"
            ]
        elif focus_area == "speed":
            suggestions["focus_suggestions"] = [
                "简化复杂步骤",
                "使用快速工具替代",
                "并行执行独立步骤"
            ]
        elif focus_area == "creativity":
            suggestions["focus_suggestions"] = [
                "添加创意探索步骤",
                "尝试非传统工具组合",
                "增加用户交互和反馈环节"
            ]
        elif focus_area == "efficiency":
            suggestions["focus_suggestions"] = [
                "合并相似步骤",
                "优化步骤顺序",
                "减少不必要的中间步骤"
            ]
    
    return {
        "success": True,
        "current_plan_info": {
            "total_steps": len(_current_plan.steps),
            "estimated_duration": sum(getattr(step, 'estimated_duration', 300) for step in _current_plan.steps),
            "template_id": getattr(_current_plan, 'template_id', 'unknown')
        },
        "suggestions": suggestions,
        "customization_examples": [
            "modify_plan_step('create_script', {'name': '创意剧本构思'})",
            "add_plan_step('after:generate_audio', {'step_id': 'audio_effects', 'name': '添加音效'})",
            "remove_plan_step('unnecessary_step')",
            "reorder_plan_steps(['step1', 'step3', 'step2', 'step4'])"
        ]
    }


# 辅助函数：设置当前计划
def set_current_plan(plan: UnifiedPlan):
    """设置当前计划（供其他模块调用）"""
    global _current_plan
    _current_plan = plan


# 辅助函数：获取当前计划
def get_current_plan() -> Optional[UnifiedPlan]:
    """获取当前计划（供其他模块调用）"""
    global _current_plan
    return _current_plan

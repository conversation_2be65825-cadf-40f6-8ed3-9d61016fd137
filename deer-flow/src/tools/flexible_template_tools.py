#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
flexible_template_tools.py

为主agent提供高自由度的模板调整工具。
"""

from typing import Dict, Any, List, Optional
from langchain_core.tools import tool
from src.templates.flexible_template_system import flexible_renderer
from src.templates.flexible_zhao_benshan_template import create_flexible_zhao_benshan_template
from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
import json
import uuid


@tool
def use_flexible_template(
    template_id: str,
    user_params: Dict[str, Any],
    user_preferences: Optional[Dict[str, Any]] = None,
    customization_requests: Optional[List[str]] = None,
    context: Optional[str] = None
) -> Dict[str, Any]:
    """
    使用高自由度模板生成可完全自定义的执行计划
    
    Args:
        template_id: 模板ID
        user_params: 用户参数
        user_preferences: 用户偏好 (quality, creativity, urgency等)
        customization_requests: 自定义请求列表
        context: 上下文信息
    
    Returns:
        包含灵活计划和自定义选项的字典
    """
    
    # 获取灵活模板
    if template_id == "flexible_zhao_benshan_xiangsheng":
        template = create_flexible_zhao_benshan_template()
    else:
        return {"error": f"模板 '{template_id}' 不存在"}
    
    try:
        # 渲染自适应计划
        result = flexible_renderer.render_adaptive_plan(
            template=template,
            user_params=user_params,
            user_preferences=user_preferences,
            context=context
        )
        
        # 应用用户的自定义请求
        if customization_requests:
            result = apply_customization_requests(result, customization_requests)
        
        # 转换为UnifiedPlan格式
        unified_plan = convert_to_unified_plan(result, template_id, user_params)
        
        return {
            "success": True,
            "plan": unified_plan,
            "flexibility_info": result["plan"]["flexibility_info"],
            "customization_guide": generate_customization_guide(template, result),
            "template_id": template_id
        }
        
    except Exception as e:
        return {"error": f"模板渲染失败: {str(e)}"}


@tool
def customize_plan_step(
    step_id: str,
    modification_type: str,
    modification_details: Dict[str, Any],
    reason: Optional[str] = None
) -> Dict[str, Any]:
    """
    自定义计划中的特定步骤
    
    Args:
        step_id: 步骤ID
        modification_type: 修改类型 (rename, retool, reparameter, restructure, etc.)
        modification_details: 修改详情
        reason: 修改原因
    
    Returns:
        修改结果和建议
    """
    
    try:
        # 获取当前计划状态
        current_plan = get_current_plan_state()
        if not current_plan:
            return {"error": "没有找到当前计划"}
        
        # 找到目标步骤
        target_step = None
        for step in current_plan.steps:
            if step.step_id == step_id:
                target_step = step
                break
        
        if not target_step:
            return {"error": f"步骤 '{step_id}' 不存在"}
        
        # 应用修改
        modification_result = apply_step_modification(
            target_step, modification_type, modification_details, reason
        )
        
        # 生成优化建议
        optimization_suggestions = generate_optimization_suggestions(
            target_step, modification_type, modification_details
        )
        
        return {
            "success": True,
            "modified_step": modification_result,
            "optimization_suggestions": optimization_suggestions,
            "impact_analysis": analyze_modification_impact(current_plan, step_id, modification_type)
        }
        
    except Exception as e:
        return {"error": f"步骤自定义失败: {str(e)}"}


@tool
def add_custom_step(
    position: str,  # "before:step_id", "after:step_id", "beginning", "end"
    step_definition: Dict[str, Any],
    integration_mode: str = "seamless"  # "seamless", "independent", "conditional"
) -> Dict[str, Any]:
    """
    添加自定义步骤到计划中
    
    Args:
        position: 插入位置
        step_definition: 步骤定义
        integration_mode: 集成模式
    
    Returns:
        添加结果和集成建议
    """
    
    try:
        # 验证步骤定义
        validation_result = validate_step_definition(step_definition)
        if not validation_result["valid"]:
            return {"error": f"步骤定义无效: {validation_result['errors']}"}
        
        # 创建新步骤
        new_step = create_custom_step(step_definition, integration_mode)
        
        # 分析插入位置的影响
        insertion_analysis = analyze_insertion_impact(position, new_step)
        
        # 生成集成建议
        integration_suggestions = generate_integration_suggestions(new_step, position)
        
        return {
            "success": True,
            "new_step": new_step,
            "insertion_analysis": insertion_analysis,
            "integration_suggestions": integration_suggestions,
            "dependencies_check": check_dependencies(new_step)
        }
        
    except Exception as e:
        return {"error": f"添加自定义步骤失败: {str(e)}"}


@tool
def optimize_plan_flow(
    optimization_goals: List[str],  # ["speed", "quality", "creativity", "efficiency"]
    constraints: Optional[Dict[str, Any]] = None,
    user_priorities: Optional[Dict[str, int]] = None  # 优先级权重
) -> Dict[str, Any]:
    """
    优化整个计划流程
    
    Args:
        optimization_goals: 优化目标
        constraints: 约束条件
        user_priorities: 用户优先级
    
    Returns:
        优化建议和替代方案
    """
    
    try:
        current_plan = get_current_plan_state()
        if not current_plan:
            return {"error": "没有找到当前计划"}
        
        # 分析当前计划
        plan_analysis = analyze_current_plan(current_plan)
        
        # 生成优化建议
        optimization_suggestions = generate_plan_optimizations(
            current_plan, optimization_goals, constraints, user_priorities
        )
        
        # 创建替代方案
        alternative_approaches = create_alternative_approaches(
            current_plan, optimization_goals
        )
        
        return {
            "success": True,
            "current_analysis": plan_analysis,
            "optimization_suggestions": optimization_suggestions,
            "alternative_approaches": alternative_approaches,
            "implementation_guide": generate_implementation_guide(optimization_suggestions)
        }
        
    except Exception as e:
        return {"error": f"计划优化失败: {str(e)}"}


@tool
def get_customization_suggestions(
    current_context: str,
    user_feedback: Optional[str] = None,
    performance_data: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    获取智能自定义建议
    
    Args:
        current_context: 当前上下文
        user_feedback: 用户反馈
        performance_data: 性能数据
    
    Returns:
        个性化的自定义建议
    """
    
    try:
        # 分析上下文和反馈
        context_analysis = analyze_context_and_feedback(current_context, user_feedback)
        
        # 生成个性化建议
        personalized_suggestions = generate_personalized_suggestions(
            context_analysis, performance_data
        )
        
        # 创建实施指南
        implementation_guide = create_suggestion_implementation_guide(personalized_suggestions)
        
        return {
            "success": True,
            "context_analysis": context_analysis,
            "suggestions": personalized_suggestions,
            "implementation_guide": implementation_guide,
            "expected_benefits": calculate_expected_benefits(personalized_suggestions)
        }
        
    except Exception as e:
        return {"error": f"获取自定义建议失败: {str(e)}"}


# 辅助函数
def apply_customization_requests(result, requests):
    """应用用户的自定义请求"""
    # 简化实现
    return result

def convert_to_unified_plan(result, template_id, user_params):
    """转换为UnifiedPlan格式"""
    steps = []
    for step_data in result["plan"]["steps"]:
        step = UnifiedStep(
            step_id=step_data["step_id"],
            name=step_data["suggested_name"],
            description=step_data["objective"],
            tool_to_use=step_data["recommended_tool"],
            inputs=step_data["parameters"],
            dependencies=step_data.get("dependencies", []),
            estimated_duration=300  # 默认5分钟
        )
        steps.append(step)
    
    return UnifiedPlan(
        plan_id=str(uuid.uuid4()),
        original_task=f"使用{template_id}模板创建内容",
        steps=steps,
        is_from_template=True,
        template_id=template_id
    )

def generate_customization_guide(template, result):
    """生成自定义指南"""
    return {
        "modifiable_aspects": result["plan"]["flexibility_info"]["modifiable_aspects"],
        "customization_examples": [
            "修改步骤名称：'我想把第一步改名为创意剧本构思'",
            "更换工具：'第二步用audio_expert替代默认工具'",
            "调整参数：'提高第三步的质量设置'",
            "添加步骤：'在第二步后添加音效处理'",
            "重新排序：'把第三步移到第二步前面'"
        ],
        "expert_tips": template.expert_tips
    }

def get_current_plan_state():
    """获取当前计划状态"""
    # 这里应该从全局状态获取
    return None

def apply_step_modification(step, mod_type, details, reason):
    """应用步骤修改"""
    # 简化实现
    return {"modified": True, "details": details}

def generate_optimization_suggestions(step, mod_type, details):
    """生成优化建议"""
    return [
        "考虑调整相关步骤的参数",
        "检查依赖关系是否需要更新",
        "评估对整体质量的影响"
    ]

def analyze_modification_impact(plan, step_id, mod_type):
    """分析修改影响"""
    return {
        "affected_steps": [],
        "quality_impact": "neutral",
        "time_impact": "minimal",
        "complexity_change": "none"
    }

def validate_step_definition(definition):
    """验证步骤定义"""
    required_fields = ["objective", "tool_to_use"]
    errors = []
    
    for field in required_fields:
        if field not in definition:
            errors.append(f"缺少必需字段: {field}")
    
    return {"valid": len(errors) == 0, "errors": errors}

def create_custom_step(definition, integration_mode):
    """创建自定义步骤"""
    return UnifiedStep(
        step_id=definition.get("step_id", str(uuid.uuid4())),
        name=definition["objective"],
        description=definition.get("description", definition["objective"]),
        tool_to_use=definition["tool_to_use"],
        inputs=definition.get("inputs", {}),
        dependencies=definition.get("dependencies", [])
    )

def analyze_insertion_impact(position, new_step):
    """分析插入影响"""
    return {
        "position_analysis": f"在{position}插入步骤",
        "workflow_impact": "minimal",
        "dependency_changes": []
    }

def generate_integration_suggestions(step, position):
    """生成集成建议"""
    return [
        "确保新步骤的输入来源明确",
        "检查输出是否被后续步骤使用",
        "考虑添加质量检查点"
    ]

def check_dependencies(step):
    """检查依赖关系"""
    return {
        "missing_dependencies": [],
        "circular_dependencies": False,
        "optimization_opportunities": []
    }

def analyze_current_plan(plan):
    """分析当前计划"""
    return {
        "total_steps": len(plan.steps),
        "estimated_duration": sum(getattr(step, 'estimated_duration', 300) for step in plan.steps),
        "complexity_level": "medium",
        "bottlenecks": [],
        "optimization_potential": "high"
    }

def generate_plan_optimizations(plan, goals, constraints, priorities):
    """生成计划优化建议"""
    suggestions = []
    
    if "speed" in goals:
        suggestions.append({
            "type": "speed_optimization",
            "description": "并行执行兼容的步骤",
            "impact": "减少30%执行时间"
        })
    
    if "quality" in goals:
        suggestions.append({
            "type": "quality_enhancement",
            "description": "添加质量检查和优化步骤",
            "impact": "提升输出质量"
        })
    
    return suggestions

def create_alternative_approaches(plan, goals):
    """创建替代方案"""
    return [
        {
            "name": "快速原型方法",
            "description": "先快速生成基础版本，再迭代优化",
            "suitable_for": ["speed", "iteration"]
        },
        {
            "name": "分阶段精品方法", 
            "description": "每个阶段都追求最高质量",
            "suitable_for": ["quality", "perfectionism"]
        }
    ]

def generate_implementation_guide(suggestions):
    """生成实施指南"""
    return {
        "step_by_step": [
            "1. 备份当前计划",
            "2. 逐步应用优化建议",
            "3. 测试每个修改的效果",
            "4. 根据结果调整策略"
        ],
        "risk_mitigation": [
            "保留原始计划作为备份",
            "分步骤验证修改效果",
            "准备回滚方案"
        ]
    }

def analyze_context_and_feedback(context, feedback):
    """分析上下文和反馈"""
    return {
        "context_type": "creative_project",
        "user_satisfaction": "medium",
        "improvement_areas": ["creativity", "efficiency"],
        "strengths": ["technical_quality"]
    }

def generate_personalized_suggestions(analysis, performance_data):
    """生成个性化建议"""
    return [
        {
            "category": "创意增强",
            "suggestions": [
                "添加更多个性化元素",
                "尝试非传统的创作方法",
                "融入用户的独特视角"
            ]
        },
        {
            "category": "效率优化",
            "suggestions": [
                "简化复杂的步骤",
                "使用更高效的工具",
                "优化工作流程"
            ]
        }
    ]

def create_suggestion_implementation_guide(suggestions):
    """创建建议实施指南"""
    return {
        "priority_order": ["高优先级建议", "中优先级建议", "低优先级建议"],
        "implementation_steps": ["评估", "计划", "执行", "验证"],
        "success_metrics": ["质量提升", "效率改善", "用户满意度"]
    }

def calculate_expected_benefits(suggestions):
    """计算预期收益"""
    return {
        "quality_improvement": "15-25%",
        "efficiency_gain": "20-30%",
        "user_satisfaction": "显著提升",
        "creative_value": "高"
    }

# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
state_management.py - 简化版

简化的状态管理工具，只保留3个核心功能：
1. get_plan_status - 获取计划状态
2. get_next_step - 获取下一步骤
3. report_step_completion - 报告步骤完成
"""

from typing import Dict, Any
from langchain_core.tools import tool
from pydantic import BaseModel, Field
import json
from datetime import datetime

from src.graph_v2.types import State
from src.graph_v2.unified_models import UnifiedPlan
from src.graph_v2.migration_utils import ensure_unified_plan


class ReportCompletionInput(BaseModel):
    step_id: str = Field(description="步骤标识符")
    status: str = Field(description="执行状态：completed 或 failed")
    result: Dict[str, Any] = Field(description="执行结果数据")


@tool
def save_plan_to_state(plan_data: Dict[str, Any]) -> str:
    """
    将计划保存到系统状态中

    当use_template等工具生成计划后，需要调用此工具将计划保存到state中，
    这样路由系统才能检测到计划并切换到执行引擎。

    Args:
        plan_data: 包含计划信息的字典，必须包含'plan'字段

    Returns:
        保存结果的描述
    """
    try:
        if "plan" not in plan_data:
            return "❌ 错误：plan_data中缺少'plan'字段"

        plan = plan_data["plan"]

        # 这里我们需要访问当前的state，但是工具无法直接访问
        # 我们使用全局变量作为临时解决方案
        global _current_state_plan
        _current_state_plan = plan

        return f"✅ 计划已保存到系统状态，包含{len(plan.steps)}个步骤，可以开始执行"

    except Exception as e:
        return f"❌ 保存计划失败: {str(e)}"


# 全局变量存储当前计划（临时解决方案）
_current_state_plan = None


def get_current_state_plan():
    """获取当前保存的计划"""
    global _current_state_plan
    return _current_state_plan


def clear_current_state_plan():
    """清除当前保存的计划"""
    global _current_state_plan
    _current_state_plan = None


@tool
def get_plan_status(state: State) -> str:
    """
    获取当前计划的整体状态和进度
    
    返回计划执行进度、已完成步骤、待执行步骤等信息。
    适用于Master Agent需要了解整体进度时。
    
    Returns:
        JSON格式的计划状态信息
    """
    plan = state.get("plan")
    if not plan:
        return "❌ 当前没有执行计划"
    
    try:
        unified_plan = ensure_unified_plan(plan)
        progress = unified_plan.get_progress_info()
        
        status_info = {
            "plan_id": unified_plan.plan_id,
            "original_task": unified_plan.original_task,
            "total_steps": progress["total_steps"],
            "completed_steps": progress["completed_steps"],
            "progress_percentage": progress["progress_percentage"],
            "is_complete": unified_plan.is_complete(),
            "has_failed": unified_plan.has_failed(),
            "template_info": {
                "is_from_template": unified_plan.is_from_template,
                "template_id": unified_plan.template_id
            }
        }
        
        return json.dumps(status_info, indent=2, ensure_ascii=False)
        
    except Exception as e:
        return f"❌ 获取计划状态失败: {str(e)}"


@tool  
def get_next_step(state: State) -> str:
    """
    获取下一个需要执行的步骤详情
    
    返回下一个可执行步骤的ID、名称、描述、工具和输入参数。
    适用于Master Agent需要知道下一步做什么时。
    
    Returns:
        JSON格式的步骤详情，或提示信息
    """
    plan = state.get("plan")
    if not plan:
        return "❌ 当前没有执行计划，请先创建计划"
    
    try:
        unified_plan = ensure_unified_plan(plan)
        executable_steps = unified_plan.get_next_executable_steps()
        
        if not executable_steps:
            if unified_plan.is_complete():
                return "✅ 计划已完成，所有步骤都已执行"
            elif unified_plan.has_failed():
                return "❌ 计划执行失败，存在无法重试的失败步骤"
            else:
                return "⏸️ 暂无可执行步骤，可能存在依赖关系问题"
        
        next_step = executable_steps[0]
        step_info = {
            "step_id": next_step.step_id,
            "name": next_step.name,
            "description": next_step.description,
            "tool_to_use": next_step.tool_to_use,
            "inputs": next_step.inputs,
            "dependencies": next_step.dependencies,
            "status": next_step.status
        }
        
        return json.dumps(step_info, indent=2, ensure_ascii=False)
        
    except Exception as e:
        return f"❌ 获取下一步骤失败: {str(e)}"


@tool("report_step_completion", args_schema=ReportCompletionInput)
def report_step_completion(step_id: str, status: str, result: Dict[str, Any]) -> str:
    """
    报告步骤执行完成情况
    
    Master Agent执行完步骤后必须调用此工具报告结果。
    这是状态管理的核心机制。
    
    Args:
        step_id: 步骤标识符
        status: "completed" 或 "failed"
        result: 执行结果数据
        
    Returns:
        确认消息和状态更新指令
    """
    # 生成状态更新指令
    update_instruction = {
        "action": "update_step_status",
        "step_id": step_id,
        "status": status,
        "result": result,
        "timestamp": datetime.now().isoformat()
    }
    
    status_emoji = "✅" if status == "completed" else "❌"
    return f"{status_emoji} 步骤 {step_id} 状态已报告: {json.dumps(update_instruction, ensure_ascii=False)}"


# 导出的工具列表
state_management_tools = [
    get_plan_status,
    get_next_step,
    report_step_completion
]

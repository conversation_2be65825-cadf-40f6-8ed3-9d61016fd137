# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
template_tools.py - 简化版

简化的模板工具，只保留核心的use_template功能。
采用工程化方式，模板ID由外部系统传入，不依赖AI选择。
"""

from typing import Dict, Any, Optional
from langchain_core.tools import tool
from pydantic import BaseModel, Field

from src.graph_v2.template_models import PlanTemplate
from src.graph_v2.template_renderer import template_renderer, TemplateRenderError
from src.graph_v2.unified_models import UnifiedPlan

# 全局模板注册表
_template_registry: Dict[str, PlanTemplate] = {}


class UseTemplateInput(BaseModel):
    template_id: str = Field(description="模板标识符")
    params: Dict[str, Any] = Field(description="模板参数")
    user_context: Optional[str] = Field(default=None, description="可选的用户上下文描述")


@tool("use_template", args_schema=UseTemplateInput)
def use_template(template_id: str, params: Dict[str, Any], user_context: Optional[str] = None, state: Optional[Dict] = None) -> str:
    """
    使用指定模板创建执行计划（增强版，支持高自由度自定义）

    适用于已知模板ID的场景，通常由上层系统或用户界面传入。
    这是工程化的模板使用方式，不依赖AI进行模板选择。

    常用模板ID：
    - "city_poster_series": 城市海报系列
    - "ai_parody_video": AI鬼畜视频制作
    - "zhao_benshan_xiangsheng": 赵本山小品生成器
    - "story_rewrite": 小品故事改写

    Args:
        template_id: 模板标识符（必须使用当前模板模式的template_id）
        params: 模板参数（根据当前模板要求提供，如 story_rewrite 需要 video_file, rewrite_requirements 等）
        user_context: 可选的用户上下文描述

    Returns:
        {
            "success": bool,
            "plan": UnifiedPlan,
            "customization_info": dict,  # 🚀 新增：自定义信息
            "template_used": str,
            "error": Optional[str],
            "metadata": Dict[str, Any]
        }
    """
    try:
        # 获取模板
        template = _template_registry.get(template_id)
        if not template:
            available_templates = list(_template_registry.keys())
            return {
                "success": False,
                "plan": None,
                "template_used": template_id,
                "error": f"模板 '{template_id}' 不存在",
                "available_templates": available_templates
            }
        
        # 使用模板渲染器创建计划
        try:
            plan = template_renderer.render_plan(template, params)

            # 如果提供了用户上下文，更新任务描述
            if user_context:
                plan.original_task = user_context

        except TemplateRenderError as e:
            return {
                "success": False,
                "plan": None,
                "template_used": template_id,
                "error": f"模板渲染失败: {str(e)}"
            }

        # 🚀 收集自定义信息
        customization_info = extract_customization_info(template, plan)

        # 设置当前计划到全局状态（供自定义工具使用）
        from src.tools.plan_customization_tools import set_current_plan
        set_current_plan(plan)

        # 更新模板使用统计
        template.usage_count += 1

        print(f"📋 use_template成功: {template_id} -> {len(plan.steps)}个步骤")

        # 🚀 直接将计划保存到state中（这是关键！）
        if state is not None:
            state["plan"] = plan
            print(f"✅ 计划已直接保存到state中")

        # 设置当前计划到全局状态（供自定义工具使用和路由检查）
        from src.tools.plan_customization_tools import set_current_plan
        set_current_plan(plan)

        # 🚀 同时保存到state_management的全局状态
        import src.tools.state_management as sm
        sm._current_state_plan = plan

        # 更新模板使用统计
        template.usage_count += 1

        # 🚀 返回特殊格式，让系统知道要切换到执行引擎
        return {
            "success": True,
            "plan_generated": True,
            "plan": plan,
            "template_used": template_id,
            "message": f"✅ 模板计划生成成功！已生成{len(plan.steps)}个步骤的执行计划。\n\n📋 **计划概览**：{template.name}\n\n🚀 **重要**：计划已保存到系统状态，请立即切换到执行引擎开始执行第一步。",
            "next_action": "switch_to_execution_engine"
        }
        
    except Exception as e:
        return {
            "success": False,
            "plan": None,
            "template_used": template_id,
            "error": f"使用模板时出现错误: {str(e)}"
        }


def register_template(template: PlanTemplate):
    """注册模板到全局注册表"""
    _template_registry[template.template_id] = template
    print(f"📝 注册模板: {template.template_id} - {template.name}")


def get_registered_templates() -> Dict[str, str]:
    """获取已注册的模板列表（用于调试）"""
    return {tid: template.name for tid, template in _template_registry.items()}


def extract_customization_info(template: PlanTemplate, plan: UnifiedPlan) -> Dict[str, Any]:
    """
    从模板和计划中提取自定义信息

    Args:
        template: 原始模板
        plan: 生成的计划

    Returns:
        自定义信息字典
    """

    customization_info = {
        "template_flexibility": {
            "overall_level": getattr(template, 'difficulty_level', 'medium'),
            "modifiable_aspects": ["步骤名称", "工具选择", "参数配置", "步骤顺序"]
        },
        "step_customization": [],
        "available_tools": [],
        "customization_examples": [
            f"modify_plan_step('{plan.steps[0].step_id}', {{'name': '自定义名称'}})",
            f"add_plan_step('after:{plan.steps[0].step_id}', {{'step_id': 'new_step', 'name': '新步骤'}})",
            "get_plan_customization_suggestions('quality')"
        ],
        "general_suggestions": [
            "🎨 创意自由：可以完全改变任何步骤的执行方式",
            "🔧 工具替换：每个步骤都可以尝试不同的工具",
            "📝 内容调整：可以修改所有文本内容和参数",
            "⚡ 流程优化：可以添加、删除、重排步骤",
            "🎯 质量控制：可以调整每个步骤的质量级别"
        ]
    }

    # 分析每个步骤的自定义选项
    for step in plan.steps:
        # 查找对应的步骤模板
        step_template = None
        for st in template.step_templates:
            if st.template_step_id in step.step_id or step.step_id in st.template_step_id:
                step_template = st
                break

        step_info = {
            "step_id": step.step_id,
            "step_name": step.name,
            "current_tool": step.tool_to_use,
            "flexibility_level": "medium",
            "customization_options": [
                "修改步骤名称和描述",
                "调整工具参数",
                "更换执行工具"
            ]
        }

        # 如果找到了步骤模板，使用其灵活性信息
        if step_template:
            step_info.update({
                "flexibility_level": getattr(step_template, 'flexibility_level', 'medium'),
                "alternative_tools": getattr(step_template, 'alternative_tools', []),
                "customization_hints": getattr(step_template, 'customization_hints', []),
                "modifiable_aspects": getattr(step_template, 'modifiable_aspects', [])
            })

            # 收集所有可用工具
            all_tools = [step.tool_to_use] + getattr(step_template, 'alternative_tools', [])
            customization_info["available_tools"].extend(all_tools)

        customization_info["step_customization"].append(step_info)

    # 去重工具列表
    customization_info["available_tools"] = list(set(customization_info["available_tools"]))

    return customization_info


# 导出的工具列表
template_tools = [use_template]

# -*- coding: utf-8 -*-

"""
任务分析工具

分析用户任务，为规划提供决策支持。
"""

from typing import Dict, Any, Optional, List
import re


def analyze_task(task: str, context: Optional[str] = None) -> Dict[str, Any]:
    """
    分析用户任务
    
    Args:
        task: 用户任务描述
        context: 额外上下文
        
    Returns:
        任务分析结果
    """
    task_lower = task.lower()
    context_lower = (context or "").lower()
    combined_text = f"{task_lower} {context_lower}"
    
    # 检测任务类型
    task_type = detect_task_type(combined_text)
    
    # 检测复杂度
    complexity = detect_complexity(combined_text)
    
    # 检测是否有合适的模板
    has_template = detect_suitable_template(combined_text)
    
    # 估算时长
    estimated_duration = estimate_duration(task_type, complexity)
    
    return {
        "task_type": task_type,
        "complexity": complexity,
        "has_suitable_template": has_template,
        "estimated_duration": estimated_duration,
        "confidence": 0.8,  # 基础置信度
        "keywords": extract_keywords(combined_text)
    }


def detect_task_type(text: str) -> str:
    """检测任务类型"""
    if any(word in text for word in ["海报", "图片", "图像", "poster", "image"]):
        return "visual"
    elif any(word in text for word in ["音乐", "声音", "音频", "music", "audio"]):
        return "audio"
    elif any(word in text for word in ["视频", "动画", "video", "animation"]):
        return "video"
    elif any(word in text for word in ["系列", "批量", "多个", "series", "batch"]):
        return "batch"
    else:
        return "general"


def detect_complexity(text: str) -> str:
    """检测任务复杂度"""
    complexity_indicators = {
        "simple": ["一个", "简单", "basic", "simple"],
        "medium": ["几个", "一些", "medium", "moderate"],
        "complex": ["很多", "复杂", "系列", "批量", "complex", "series", "batch"]
    }
    
    for level, indicators in complexity_indicators.items():
        if any(indicator in text for indicator in indicators):
            return level
    
    return "medium"  # 默认中等复杂度


def detect_suitable_template(text: str) -> bool:
    """检测是否有合适的模板"""
    template_keywords = [
        "海报", "poster", "城市", "city",
        "鬼畜", "parody", "视频制作",
        "音乐生成", "music generation"
    ]
    
    return any(keyword in text for keyword in template_keywords)


def estimate_duration(task_type: str, complexity: str) -> int:
    """估算执行时长（秒）"""
    base_duration = {
        "visual": 60,
        "audio": 90,
        "video": 180,
        "batch": 300,
        "general": 120
    }
    
    complexity_multiplier = {
        "simple": 0.5,
        "medium": 1.0,
        "complex": 2.0
    }
    
    base = base_duration.get(task_type, 120)
    multiplier = complexity_multiplier.get(complexity, 1.0)
    
    return int(base * multiplier)


def extract_keywords(text: str) -> List[str]:
    """提取关键词"""
    # 简单的关键词提取
    words = re.findall(r'\b\w+\b', text)
    # 过滤常见词汇
    stop_words = {"的", "是", "在", "有", "和", "或", "但", "这", "那", "一个", "一些"}
    keywords = [word for word in words if len(word) > 1 and word not in stop_words]
    return keywords[:10]  # 返回前10个关键词

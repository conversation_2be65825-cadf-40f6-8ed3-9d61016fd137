# -*- coding: utf-8 -*-

"""
统一的规划工具

提供单一入口的任务规划功能，自动选择最佳策略。
"""

from typing import Dict, Any, Optional, List
from langchain_core.tools import tool
from pydantic import BaseModel, Field

from src.graph_v2.unified_models import UnifiedPlan
from src.tools.core.templates import recommend_and_create_from_template
from src.tools.utils.task_analysis import analyze_task


class PlanTaskInput(BaseModel):
    """规划任务的输入参数"""
    task: str = Field(..., description="用户任务描述")
    context: Optional[str] = Field(default=None, description="额外上下文信息")
    preferences: Optional[Dict[str, Any]] = Field(default=None, description="用户偏好设置")


@tool("plan_task", args_schema=PlanTaskInput)
def plan_task(
    task: str,
    context: Optional[str] = None,
    preferences: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    统一的任务规划工具
    
    自动分析任务并选择最佳规划策略（模板 vs 自定义）。
    
    Args:
        task: 用户任务描述
        context: 额外上下文信息
        preferences: 用户偏好设置
        
    Returns:
        包含计划和策略信息的字典
    """
    try:
        # 1. 分析任务
        analysis = analyze_task(task, context)
        
        # 2. 选择策略
        if analysis.get("has_suitable_template", False):
            # 使用模板策略
            result = recommend_and_create_from_template(task, analysis)
            result["strategy"] = "template"
        else:
            # 使用自定义规划策略
            result = create_custom_plan(task, analysis)
            result["strategy"] = "custom"
        
        # 3. 添加元信息
        result.update({
            "confidence": analysis.get("confidence", 0.8),
            "task_complexity": analysis.get("complexity", "medium"),
            "estimated_duration": analysis.get("estimated_duration", 300)
        })
        
        return result
        
    except Exception as e:
        return {
            "success": False,
            "error": f"规划失败: {str(e)}",
            "plan": None,
            "strategy": "error"
        }


def create_custom_plan(task: str, analysis: Dict[str, Any]) -> Dict[str, Any]:
    """创建自定义计划"""
    # 这里会调用原有的planner_tool逻辑
    # 但是简化了接口
    from src.tools.planning import planner_tool
    
    result = planner_tool.invoke({"task": task})
    
    return {
        "success": True,
        "plan": result["plan"],
        "source": "custom_planning"
    }


# 导出工具列表
core_planning_tools = [plan_task]

# -*- coding: utf-8 -*-

"""
简化的执行状态工具

提供清晰简单的执行状态查询和更新功能。
"""

from typing import Dict, Any, Optional
from langchain_core.tools import tool
from pydantic import BaseModel, Field

from src.graph_v2.types import State
from src.graph_v2.migration_utils import ensure_unified_plan


class ReportStepResultInput(BaseModel):
    """报告步骤结果的输入参数"""
    step_id: str = Field(..., description="步骤ID")
    result: Dict[str, Any] = Field(..., description="执行结果")
    status: str = Field(default="completed", description="步骤状态")


@tool("get_execution_status")
def get_execution_status(state: State) -> Dict[str, Any]:
    """
    获取当前执行状态
    
    简化版本的状态查询，返回最重要的信息。
    """
    plan = state.get("plan")
    if not plan:
        return {
            "status": "no_plan",
            "message": "没有活动的执行计划",
            "next_action": "create_plan"
        }
    
    try:
        unified_plan = ensure_unified_plan(plan)
        
        if unified_plan.is_complete():
            return {
                "status": "completed",
                "message": "所有步骤已完成",
                "progress": unified_plan.get_progress_info()
            }
        
        executable_steps = unified_plan.get_next_executable_steps()
        if executable_steps:
            next_step = executable_steps[0]
            return {
                "status": "ready",
                "message": f"准备执行: {next_step.name}",
                "next_step": {
                    "step_id": next_step.step_id,
                    "name": next_step.name,
                    "tool": next_step.tool_to_use
                },
                "progress": unified_plan.get_progress_info()
            }
        else:
            return {
                "status": "blocked",
                "message": "没有可执行的步骤",
                "progress": unified_plan.get_progress_info()
            }
            
    except Exception as e:
        return {
            "status": "error",
            "message": f"状态查询失败: {str(e)}"
        }


@tool("report_step_result", args_schema=ReportStepResultInput)
def report_step_result(
    state: State,
    step_id: str,
    result: Dict[str, Any],
    status: str = "completed"
) -> Dict[str, Any]:
    """
    报告步骤执行结果
    
    简化版本的结果报告，自动更新计划状态。
    """
    plan = state.get("plan")
    if not plan:
        return {
            "success": False,
            "error": "没有活动的计划"
        }
    
    try:
        unified_plan = ensure_unified_plan(plan)
        step = unified_plan.get_step(step_id)
        
        if not step:
            return {
                "success": False,
                "error": f"步骤 {step_id} 不存在"
            }
        
        # 更新步骤状态
        if status == "completed":
            step.mark_completed(result)
        elif status == "failed":
            step.mark_failed(result.get("error", "未知错误"))
        
        # 返回更新后的状态
        return {
            "success": True,
            "step_id": step_id,
            "new_status": step.status,
            "plan_progress": unified_plan.get_progress_info(),
            "plan": unified_plan
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"结果报告失败: {str(e)}"
        }


# 导出工具列表
core_execution_tools = [get_execution_status, report_step_result]

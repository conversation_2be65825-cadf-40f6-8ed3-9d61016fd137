# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
planning.py - 简化版

简化的规划工具，只保留核心的create_plan功能。
Master Agent根据任务复杂度自主决定是否使用规划工具。
"""

from typing import Dict, Any, Optional
from langchain_core.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable
from pydantic import BaseModel, Field
from langchain_core.output_parsers import PydanticOutputParser

from src.utils.prompt_utils import get_agent_prompt_template
from src.graph_v2.unified_models import UnifiedPlan
from src.graph_v2.models import Plan  # 用于LLM输出解析
from src.graph_v2.migration_utils import migrate_legacy_plan_to_unified

# Global variables for the planner chain and current plan
_planner_chain: Optional[Runnable] = None
_current_plan: Optional[UnifiedPlan] = None


class CreatePlanInput(BaseModel):
    task: str = Field(description="用户的任务描述，需要创建执行计划")


def _create_planner_chain(llm: Runnable) -> Runnable:
    """创建规划LLM链"""
    parser = PydanticOutputParser(pydantic_object=Plan)
    base_prompt = get_agent_prompt_template("planner_prompt_zh")
    prompt_template = base_prompt.partial(
        format_instructions=parser.get_format_instructions()
    )
    return prompt_template | llm | parser


@tool("create_plan", args_schema=CreatePlanInput)
def create_plan(task: str) -> str:
    """
    为复杂任务创建结构化执行计划

    当用户的任务需要多个步骤或涉及多种媒体类型时使用此工具。
    例如：制作视频、创建系列内容、复杂的创意项目等。

    适用场景：
    - 任务涉及多个步骤（如：先生成图片，再制作视频）
    - 需要创建系列内容（如：多个城市的海报）
    - 复杂的创意项目（如：完整的品牌设计）
    - 用户明确要求制定计划

    Args:
        task: 用户的任务描述

    Returns:
        str - 计划创建确认消息
    """
    global _planner_chain
    if _planner_chain is None:
        raise ValueError("Planning tools not initialized. Please call initialize_planning_tools() first.")
    
    try:
        # 使用LLM生成Legacy Plan
        legacy_plan = _planner_chain.invoke({
            "task": task, 
            "plan": "No existing plan."
        })
        
        # 设置原始任务
        legacy_plan.original_task = task
        
        # 转换为UnifiedPlan
        unified_plan = migrate_legacy_plan_to_unified(legacy_plan)
        
        print(f"📋 create_plan生成计划: {len(unified_plan.steps)}个步骤")
        for i, step in enumerate(unified_plan.steps, 1):
            print(f"   {i}. {step.name} ({step.tool_to_use})")

        # 🔧 关键修改：直接设置全局状态
        global _current_plan
        _current_plan = unified_plan

        return f"✅ 已创建包含{len(unified_plan.steps)}个步骤的执行计划。计划已保存，可以开始执行。"
        
    except Exception as e:
        print(f"❌ create_plan执行失败: {e}")
        # 返回空计划作为fallback
        fallback_plan = UnifiedPlan(original_task=task, steps=[])
        return {"plan": fallback_plan}


def get_current_plan() -> Optional[UnifiedPlan]:
    """
    获取当前的执行计划

    Returns:
        当前计划，如果没有则返回None
    """
    global _current_plan
    return _current_plan


def clear_current_plan():
    """清除当前计划"""
    global _current_plan
    _current_plan = None


def initialize_planning_tools(llm: Runnable):
    """
    初始化规划工具的LLM链
    必须在使用create_plan之前调用
    """
    global _planner_chain
    _planner_chain = _create_planner_chain(llm)


# 导出的工具列表
planning_tools = [create_plan]

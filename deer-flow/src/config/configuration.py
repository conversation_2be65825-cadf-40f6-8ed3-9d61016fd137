# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
from dataclasses import dataclass, fields
from typing import Any, Optional, Dict, List

from langchain_core.runnables import RunnableConfig


# Minimax TTS 完整系统音色配置
MINIMAX_SYSTEM_VOICES: Dict[str, Dict] = {
    # === 男性青年音色 ===
    "male-qn-qingse": {"name": "青涩青年音色", "gender": "male", "category": "youth"},
    "male-qn-jingying": {"name": "精英青年音色", "gender": "male", "category": "youth"},
    "male-qn-badao": {"name": "霸道青年音色", "gender": "male", "category": "youth"},
    "male-qn-daxuesheng": {"name": "青年大学生音色", "gender": "male", "category": "youth"},

    # === 女性青年音色 ===
    "female-shaonv": {"name": "少女音色", "gender": "female", "category": "youth"},
    "female-yujie": {"name": "御姐音色", "gender": "female", "category": "youth"},
    "female-chengshu": {"name": "成熟女性音色", "gender": "female", "category": "youth"},
    "female-tianmei": {"name": "甜美女性音色", "gender": "female", "category": "youth"},

    # === 主持人音色 ===
    "presenter_male": {"name": "男性主持人", "gender": "male", "category": "presenter"},
    "presenter_female": {"name": "女性主持人", "gender": "female", "category": "presenter"},

    # === 有声书音色 ===
    "audiobook_male_1": {"name": "男性有声书1", "gender": "male", "category": "audiobook"},
    "audiobook_male_2": {"name": "男性有声书2", "gender": "male", "category": "audiobook"},
    "audiobook_female_1": {"name": "女性有声书1", "gender": "female", "category": "audiobook"},
    "audiobook_female_2": {"name": "女性有声书2", "gender": "female", "category": "audiobook"},

    # === 精品版音色(beta) ===
    "male-qn-qingse-jingpin": {"name": "青涩青年音色-beta", "gender": "male", "category": "premium"},
    "male-qn-jingying-jingpin": {"name": "精英青年音色-beta", "gender": "male", "category": "premium"},
    "male-qn-badao-jingpin": {"name": "霸道青年音色-beta", "gender": "male", "category": "premium"},
    "male-qn-daxuesheng-jingpin": {"name": "青年大学生音色-beta", "gender": "male", "category": "premium"},
    "female-shaonv-jingpin": {"name": "少女音色-beta", "gender": "female", "category": "premium"},
    "female-yujie-jingpin": {"name": "御姐音色-beta", "gender": "female", "category": "premium"},
    "female-chengshu-jingpin": {"name": "成熟女性音色-beta", "gender": "female", "category": "premium"},
    "female-tianmei-jingpin": {"name": "甜美女性音色-beta", "gender": "female", "category": "premium"},

    # === 儿童音色 ===
    "clever_boy": {"name": "聪明男童", "gender": "male", "category": "child"},
    "cute_boy": {"name": "可爱男童", "gender": "male", "category": "child"},
    "lovely_girl": {"name": "萌萌女童", "gender": "female", "category": "child"},

    # === 角色扮演音色 ===
    "cartoon_pig": {"name": "卡通猪小琪", "gender": "neutral", "category": "character"},
    "bingjiao_didi": {"name": "病娇弟弟", "gender": "male", "category": "character"},
    "junlang_nanyou": {"name": "俊朗男友", "gender": "male", "category": "character"},
    "chunzhen_xuedi": {"name": "纯真学弟", "gender": "male", "category": "character"},
    "lengdan_xiongzhang": {"name": "冷淡学长", "gender": "male", "category": "character"},
    "badao_shaoye": {"name": "霸道少爷", "gender": "male", "category": "character"},
    "tianxin_xiaoling": {"name": "甜心小玲", "gender": "female", "category": "character"},
    "qiaopi_mengmei": {"name": "俏皮萌妹", "gender": "female", "category": "character"},
    "wumei_yujie": {"name": "妩媚御姐", "gender": "female", "category": "character"},
    "diadia_xuemei": {"name": "嗲嗲学妹", "gender": "female", "category": "character"},
    "danya_xuejie": {"name": "淡雅学姐", "gender": "female", "category": "character"},

    # === 英文角色 ===
    "Santa_Claus": {"name": "Santa Claus", "gender": "male", "category": "english"},
    "Grinch": {"name": "Grinch", "gender": "male", "category": "english"},
    "Rudolph": {"name": "Rudolph", "gender": "male", "category": "english"},
    "Arnold": {"name": "Arnold", "gender": "male", "category": "english"},
    "Charming_Santa": {"name": "Charming Santa", "gender": "male", "category": "english"},
    "Charming_Lady": {"name": "Charming Lady", "gender": "female", "category": "english"},
    "Sweet_Girl": {"name": "Sweet Girl", "gender": "female", "category": "english"},
    "Cute_Elf": {"name": "Cute Elf", "gender": "neutral", "category": "english"},
    "Attractive_Girl": {"name": "Attractive Girl", "gender": "female", "category": "english"},
    "Serene_Woman": {"name": "Serene Woman", "gender": "female", "category": "english"}
}

# Minimax TTS 参数范围配置
MINIMAX_PARAM_RANGES = {
    "speed": {"min": 0.5, "max": 2.0, "default": 1.0, "description": "语速，取值越大语速越快"},
    "vol": {"min": 0.1, "max": 10.0, "default": 1.0, "description": "音量，取值越大音量越高"},
    "pitch": {"min": -12, "max": 12, "default": 0, "description": "语调，0为原音色输出，取值需为整数"}
}

# Minimax TTS 支持的情感类型
MINIMAX_EMOTIONS = ["happy", "sad", "angry", "fearful", "disgusted", "surprised", "calm"]

# 推荐的声音组合
MINIMAX_VOICE_PAIRS = {
    "professional": {"male": "presenter_male", "female": "presenter_female", "desc": "专业主持人组合"},
    "youth": {"male": "male-qn-jingying", "female": "female-yujie", "desc": "青年组合"},
    "audiobook": {"male": "audiobook_male_1", "female": "audiobook_female_1", "desc": "有声书组合"},
    "character": {"male": "junlang_nanyou", "female": "tianxin_xiaoling", "desc": "角色扮演组合"},
    "premium": {"male": "male-qn-jingying-jingpin", "female": "female-yujie-jingpin", "desc": "精品版组合"}
}


@dataclass(kw_only=True)
class Configuration:
    """The configurable fields."""

    max_plan_iterations: int = 1  # Maximum number of plan iterations
    max_step_num: int = 3  # Maximum number of steps in a plan
    mcp_settings: dict = None  # MCP settings, including dynamic loaded tools
    suno_api_key: Optional[str] = None
    suno_base_url: Optional[str] = None
    # Minimax TTS API配置
    minimax_api_key: Optional[str] = None
    minimax_group_id: Optional[str] = None
    minimax_default_model: Optional[str] = "speech-02-hd"

    # 默认声音配置
    minimax_default_female_voice: Optional[str] = "female-yujie"
    minimax_default_male_voice: Optional[str] = "male-qn-qingse"

    # 音频参数默认值
    minimax_default_speed: Optional[float] = 1.0  # 语速范围[0.5, 2.0]
    minimax_default_vol: Optional[float] = 1.0    # 音量范围(0, 10]
    minimax_default_pitch: Optional[int] = 0      # 语调范围[-12, 12]
    minimax_default_emotion: Optional[str] = "calm"  # 默认情感
    jmeng_proxy_api_key: Optional[str] = None
    # For Flux
    flux_api_key: Optional[str] = None
    flux_base_url: Optional[str] = None
    # For Kling
    kling_api_key: Optional[str] = None
    kling_api_base: Optional[str] = None
    # Tencent Cloud Object Storage (COS) configuration
    tencent_secret_id: Optional[str] = None
    tencent_secret_key: Optional[str] = None
    cos_region: Optional[str] = None
    cos_bucket: Optional[str] = None
    # OpenAI API configuration (for understanding tools)
    openai_api_key: Optional[str] = None
    openai_base_url: Optional[str] = None

    # 通义听悟API配置 (用于视频字幕提取的说话人识别)
    tongyi_tingwu_access_key_id: Optional[str] = None
    tongyi_tingwu_access_key_secret: Optional[str] = None
    tongyi_tingwu_app_key: Optional[str] = None

    # 字幕提取API选择 ("volcengine" | "tongyi" | "auto")
    subtitle_api_provider: Optional[str] = "tongyi"

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "Configuration":
        """Create a Configuration instance from a RunnableConfig."""
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )
        values: dict[str, Any] = {
            f.name: os.environ.get(f.name.upper(), configurable.get(f.name))
            for f in fields(cls)
            if f.init
        }
        return cls(**{k: v for k, v in values.items() if v})

    @staticmethod
    def get_minimax_voices_by_gender(gender: str) -> List[str]:
        """根据性别获取Minimax声音ID列表"""
        return [voice_id for voice_id, info in MINIMAX_SYSTEM_VOICES.items()
                if info["gender"] == gender]

    @staticmethod
    def get_minimax_voices_by_category(category: str) -> List[str]:
        """根据分类获取Minimax声音ID列表"""
        return [voice_id for voice_id, info in MINIMAX_SYSTEM_VOICES.items()
                if info["category"] == category]

    @staticmethod
    def get_minimax_voice_info(voice_id: str) -> Dict:
        """获取指定声音ID的详细信息"""
        return MINIMAX_SYSTEM_VOICES.get(voice_id, {})

    @staticmethod
    def validate_minimax_params(speed: float = None, vol: float = None,
                               pitch: int = None, emotion: str = None) -> Dict[str, bool]:
        """验证Minimax TTS参数是否在有效范围内"""
        result = {}

        if speed is not None:
            result["speed"] = (MINIMAX_PARAM_RANGES["speed"]["min"] <= speed <=
                              MINIMAX_PARAM_RANGES["speed"]["max"])

        if vol is not None:
            result["vol"] = (0 < vol <= MINIMAX_PARAM_RANGES["vol"]["max"])

        if pitch is not None:
            result["pitch"] = (MINIMAX_PARAM_RANGES["pitch"]["min"] <= pitch <=
                              MINIMAX_PARAM_RANGES["pitch"]["max"])

        if emotion is not None:
            result["emotion"] = emotion in MINIMAX_EMOTIONS

        return result

    @staticmethod
    def get_recommended_voice_pair(pair_type: str = "professional") -> Dict[str, str]:
        """获取推荐的声音组合"""
        return MINIMAX_VOICE_PAIRS.get(pair_type, MINIMAX_VOICE_PAIRS["professional"])

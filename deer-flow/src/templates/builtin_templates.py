# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
builtin_templates.py

This module defines the built-in templates that come with DeerFlow.
These templates cover common video creation scenarios.
"""

from src.graph_v2.template_models import PlanTemplate, StepTemplate, ParameterSchema, ParameterType
from src.tools.template_tools import register_template
from .story_rewrite_template import create_story_rewrite_template

# StepType已废弃，现在直接使用字符串指定tool_to_use


def create_ai_parody_video_template() -> PlanTemplate:
    """Create the AI parody video template."""
    return PlanTemplate(
        template_id="ai_parody_video",
        name="AI鬼畜视频制作",
        description="制作具有鬼畜效果的AI视频，包含素材收集、特效制作、音频处理和视频合成",
        category="video_creation",
        tags=["ai", "parody", "video", "entertainment", "鬼畜"],
        
        parameters={
            "character": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="主角名称（如：哪吒、孙悟空等）"
            ),
            "style": ParameterSchema(
                type=ParameterType.STRING,
                default="现代",
                description="视觉风格，可自由描述，如：现代、传统、卡通、写实、搞笑等"
            ),
            "duration": ParameterSchema(
                type=ParameterType.INTEGER,
                default=30,
                min_value=10,
                max_value=120,
                description="视频时长（秒）"
            ),
            "effect_intensity": ParameterSchema(
                type=ParameterType.STRING,
                default="中等",
                description="鬼畜效果强度，可自由描述，如：低、中等、高、极强、轻微、疯狂等"
            ),
            "background_music": ParameterSchema(
                type=ParameterType.BOOLEAN,
                default=True,
                description="是否添加背景音乐"
            )
        },
        
        step_templates=[
            StepTemplate(
                template_step_id="collect_materials",
                name="素材收集",
                description_template="收集{character}相关的图片和视频素材，风格为{style}",
                tool_to_use="visual_expert",
                input_template={
                    "task_description": "收集{character}的高质量图片素材，要求{style}风格，数量10-15张",
                    "search_query": "{character}",
                    "style_preference": "{style}",
                    "material_count": 12,
                    "quality_requirements": "high_resolution"
                },
                estimated_duration=60
            ),
            
            StepTemplate(
                template_step_id="create_base_character",
                name="角色形象设计",
                description_template="基于收集的素材，设计{character}的标准形象",
                tool_to_use="visual_expert",
                dependencies=["collect_materials"],
                input_template={
                    "task_description": "基于提供的素材，创建{character}的标准角色形象，风格为{style}，适合用于鬼畜视频",
                    "source_materials": "{{collect_materials.assets.images}}",
                    "character_name": "{character}",
                    "style": "{style}",
                    "output_format": "character_sheet"
                },
                estimated_duration=90
            ),
            
            StepTemplate(
                template_step_id="create_parody_effects",
                name="鬼畜特效制作",
                description_template="为{character}创建{effect_intensity}强度的鬼畜特效",
                tool_to_use="visual_expert",
                dependencies=["create_base_character"],
                input_template={
                    "task_description": "为角色创建鬼畜特效，包括表情变化、动作夸张、色彩变化等，强度为{effect_intensity}",
                    "source_images": "{{create_base_character.assets.images}}",
                    "effect_type": "parody",
                    "intensity": "{effect_intensity}",
                    "duration": "{duration}",
                    "frame_count": 30
                },
                estimated_duration=120
            ),
            
            StepTemplate(
                template_step_id="create_audio",
                name="音频制作",
                description_template="创建配套的音频内容，包括配音和背景音乐",
                tool_to_use="audio_expert",
                input_template={
                    "task_description": "为{character}鬼畜视频创建音频内容，包括夸张的配音效果和背景音乐",
                    "character_name": "{character}",
                    "style": "parody",
                    "duration": "{duration}",
                    "include_background_music": "{background_music}",
                    "voice_effects": ["pitch_shift", "speed_change", "echo"]
                },
                parallel_group="audio_video",
                estimated_duration=100
            ),
            
            StepTemplate(
                template_step_id="video_synthesis",
                name="视频合成",
                description_template="将视觉特效和音频合成为最终的{character}鬼畜视频",
                tool_to_use="video_expert",
                dependencies=["create_parody_effects", "create_audio"],
                input_template={
                    "task_description": "合成最终的鬼畜视频，同步视觉特效和音频",
                    "visual_effects": "{{create_parody_effects.assets.images}}",
                    "audio_track": "{{create_audio.assets.audio}}",
                    "duration": "{duration}",
                    "output_format": "mp4",
                    "quality": "high",
                    "sync_mode": "auto"
                },
                estimated_duration=80
            )
        ],
        
        estimated_duration=450,
        difficulty_level="medium"
    )


def create_product_promo_template() -> PlanTemplate:
    """Create the product promotion video template."""
    return PlanTemplate(
        template_id="product_promo_video",
        name="产品宣传视频",
        description="制作专业的产品宣传视频，包含产品展示、特性介绍和行动号召",
        category="video_creation",
        tags=["product", "marketing", "promotion", "business"],
        
        parameters={
            "product_name": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="产品名称"
            ),
            "product_type": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="产品类型，可自由描述，如：软件、硬件、服务、应用、游戏、工具等"
            ),
            "target_audience": ParameterSchema(
                type=ParameterType.STRING,
                default="大众",
                description="目标受众，可自由描述，如：大众、企业、消费者、开发者、学生、专业人士等"
            ),
            "video_style": ParameterSchema(
                type=ParameterType.STRING,
                default="专业",
                description="视频风格，可自由描述，如：专业、休闲、现代、简约、动感、科技感等"
            ),
            "duration": ParameterSchema(
                type=ParameterType.INTEGER,
                default=60,
                min_value=30,
                max_value=180,
                description="视频时长（秒）"
            )
        },
        
        step_templates=[
            StepTemplate(
                template_step_id="product_showcase",
                name="产品展示设计",
                description_template="设计{product_name}的产品展示画面，风格为{video_style}",
                tool_to_use="visual_expert",
                input_template={
                    "task_description": "创建{product_name}的产品展示图像，突出产品特色，风格为{video_style}，适合{target_audience}受众",
                    "product_name": "{product_name}",
                    "product_type": "{product_type}",
                    "style": "{video_style}",
                    "target_audience": "{target_audience}",
                    "layout": "hero_shot"
                },
                estimated_duration=90
            ),
            
            StepTemplate(
                template_step_id="feature_highlights",
                name="特性亮点设计",
                description_template="设计{product_name}的核心特性展示",
                tool_to_use="visual_expert",
                dependencies=["product_showcase"],
                input_template={
                    "task_description": "创建产品核心特性的可视化展示，包括功能图标、特性说明等",
                    "product_context": "{{product_showcase.result}}",
                    "style": "{video_style}",
                    "feature_count": 3,
                    "layout": "feature_grid"
                },
                estimated_duration=75
            ),
            
            StepTemplate(
                template_step_id="script_and_voiceover",
                name="脚本和配音",
                description_template="创建宣传脚本并生成专业配音",
                tool_to_use="audio_expert",
                input_template={
                    "task_description": "为{product_name}创建宣传脚本并生成配音，时长{duration}秒，适合{target_audience}",
                    "product_name": "{product_name}",
                    "product_type": "{product_type}",
                    "target_audience": "{target_audience}",
                    "duration": "{duration}",
                    "tone": "professional",
                    "include_cta": True
                },
                parallel_group="content_creation",
                estimated_duration=100
            ),
            
            StepTemplate(
                template_step_id="final_video_assembly",
                name="视频最终合成",
                description_template="将所有元素合成为完整的{product_name}宣传视频",
                tool_to_use="video_expert",
                dependencies=["product_showcase", "feature_highlights", "script_and_voiceover"],
                input_template={
                    "task_description": "合成完整的产品宣传视频，包含产品展示、特性介绍和配音",
                    "product_showcase": "{{product_showcase.assets.images}}",
                    "feature_highlights": "{{feature_highlights.assets.images}}",
                    "voiceover": "{{script_and_voiceover.assets.audio}}",
                    "duration": "{duration}",
                    "style": "{video_style}",
                    "output_quality": "high"
                },
                estimated_duration=90
            )
        ],
        
        estimated_duration=355,
        difficulty_level="easy"
    )


def create_city_poster_series_template() -> PlanTemplate:
    """Create the city poster series template."""
    return PlanTemplate(
        template_id="city_poster_series",
        name="城市主题海报系列",
        description="制作一系列风格一致的城市主题海报，适合旅游宣传或城市品牌推广",
        category="image_series",
        tags=["poster", "city", "series", "tourism", "branding"],
        
        parameters={
            "cities": ParameterSchema(
                type=ParameterType.LIST,
                required=True,
                description="城市列表（如：['北京', '上海', '广州']）"
            ),
            "theme": ParameterSchema(
                type=ParameterType.STRING,
                default="旅游",
                description="海报主题，可自由描述，如：旅游、美食、文化、现代、传统、历史、科技等"
            ),
            "style": ParameterSchema(
                type=ParameterType.STRING,
                default="现代",
                description="设计风格，可自由描述，如：现代、复古、简约、艺术、摄影、手绘等"
            ),
            "color_scheme": ParameterSchema(
                type=ParameterType.STRING,
                default="鲜艳",
                description="色彩方案，可自由描述，如：鲜艳、柔和、单色、暖色调、冷色调等"
            )
        },
        
        step_templates=[
            StepTemplate(
                template_step_id="establish_style_guide",
                name="建立风格指南",
                description_template="为{theme}主题的城市海报系列建立统一的{style}风格指南",
                tool_to_use="visual_expert",
                input_template={
                    "task_description": "创建城市海报系列的风格指南，主题为{theme}，风格为{style}，色彩方案为{color_scheme}",
                    "theme": "{theme}",
                    "style": "{style}",
                    "color_scheme": "{color_scheme}",
                    "output_type": "style_guide",
                    "include_typography": True,
                    "include_color_palette": True
                },
                estimated_duration=120
            ),
            
            StepTemplate(
                template_step_id="create_poster_series",
                name="批量生成海报",
                description_template="基于风格指南为每个城市创建海报",
                tool_to_use="visual_expert",
                dependencies=["establish_style_guide"],
                input_template={
                    "task_description": "为城市列表中的每个城市创建海报，保持风格一致性",
                    "cities": "{cities}",
                    "style_guide": "{{establish_style_guide.assets.style_guide}}",
                    "theme": "{theme}",
                    "batch_mode": True,
                    "consistency_check": True
                },
                estimated_duration=180
            ),
            
            StepTemplate(
                template_step_id="quality_review",
                name="质量检查和优化",
                description_template="检查海报系列的一致性和质量，进行必要的调整",
                tool_to_use="visual_expert",
                dependencies=["create_poster_series"],
                input_template={
                    "task_description": "检查海报系列的视觉一致性，调整不符合风格指南的元素",
                    "poster_series": "{{create_poster_series.assets.images}}",
                    "style_guide": "{{establish_style_guide.assets.style_guide}}",
                    "check_consistency": True,
                    "auto_adjust": True
                },
                estimated_duration=90
            )
        ],
        
        estimated_duration=390,
        difficulty_level="easy"
    )


def create_zhao_benshan_xiangsheng_template() -> PlanTemplate:
    """Create the Zhao Benshan xiangsheng template."""
    return PlanTemplate(
        template_id="zhao_benshan_xiangsheng",
        name="赵本山小品生成器",
        description="基于赵本山和宋丹丹的声音克隆，生成东北风格小品视频。支持自定义剧情，智能匹配视频片段，自动合成最终作品。",
        category="entertainment",
        tags=["xiangsheng", "zhao_benshan", "northeast", "comedy", "video", "tts"],

        parameters={
            "topic": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="小品主题，如 '看病'、'买彩票'、'相亲'、'找工作'"
            ),
            "style": ParameterSchema(
                type=ParameterType.STRING,
                default="经典",
                description="小品风格，可以自由描述，如：经典、现代、搞笑、荒诞、怀旧、幽默等"
            ),
            "duration": ParameterSchema(
                type=ParameterType.STRING,
                default="中等",
                description="视频时长，可以自由描述，如：短、中等、长、1分钟、3分钟、60秒等"
            )
        },

        step_templates=[
            StepTemplate(
                template_step_id="create_script",
                name="创作小品剧本",
                description_template="主agent直接创作关于{topic}的赵本山风格小品剧本",
                tool_to_use="direct_generation",  # 🚀 特殊标记：让主agent直接生成内容
                input_template={
                    "instruction": """请你作为一个专业的小品编剧，创作一个赵本山风格的东北小品剧本。

**创作要求**：
- 主题：{topic}
- 风格：{style}（经典赵本山风格）
- 时长：{duration}（约3-5分钟的对话内容）

**角色设定**：
- 医生：专业但有点迂腐，说话带点书面语，偶尔冒出几句东北话
- 患者（赵本山）：东北大叔，幽默风趣，有点固执，爱钻牛角尖
- 患者家属（宋丹丹）：东北大嫂，泼辣直爽，关心家人，话多

**剧本结构**：
1. 开场：患者家属带患者看病，简单介绍情况
2. 发展：医生询问病情，患者表达困惑，产生误会
3. 高潮：误会加深，三人对话越来越搞笑
4. 结尾：真相大白，皆大欢喜

**语言要求**：
- 大量使用东北方言和俚语
- 对话要生活化、接地气
- 包含经典的"包袱"和笑点
- 体现东北人的幽默和智慧

**输出格式**：
医生：[台词内容]
患者：[台词内容]
患者家属：[台词内容]

请直接开始创作，生成一个完整的、有趣的小品剧本。"""
                },
                estimated_duration=300,

                # 🚀 新增：高自由度配置
                alternative_tools=["visual_expert", "audio_expert"],
                customization_hints=[
                    "可以调整角色性格和对话风格",
                    "可以修改剧本长度和复杂度",
                    "可以添加当代元素或经典回忆",
                    "可以调整幽默风格：温馨、荒诞、讽刺等",
                    "可以融入用户的个人经历或地域特色"
                ],
                flexibility_level="high",
                modifiable_aspects=["name", "tool", "parameters", "description", "style"],
                quality_options={
                    "quick": {"complexity": "simple", "length": "short"},
                    "standard": {"complexity": "medium", "length": "medium"},
                    "premium": {"complexity": "rich", "length": "detailed"}
                }
            ),
            StepTemplate(
                template_step_id="generate_audio",
                name="生成角色语音",
                description_template="使用克隆声音生成小品对话音频",
                tool_to_use="audio_expert",
                dependencies=["create_script"],
                input_template={
                    "task_description": """基于创作的小品剧本，生成多人TTS语音文件。

声音资产：
- 赵本山（患者）：voice_id = zhaobenshan_voice_clone_test_001
- 宋丹丹（患者家属）：voice_id = cloned_voice_from_host_audio_20240730
- 医生：使用默认专业男声

要求：
1. 为每段台词生成独立的音频文件
2. 保持角色声音特色和东北口音
3. 根据情感调整语调和语速
4. 确保音频质量清晰，便于后续视频合成

输出：按顺序编号的音频文件，包含角色标识和时长信息。"""
                },
                estimated_duration=600,

                # 🚀 新增：音频处理的灵活性
                alternative_tools=["create_plan", "visual_expert"],
                customization_hints=[
                    "可以调整每个角色的语音特色和情感表达",
                    "可以添加背景音乐和环境音效",
                    "可以优化对话的节奏和停顿",
                    "可以添加笑声、掌声等互动音效",
                    "可以为不同场景设置不同的音频氛围"
                ],
                flexibility_level="high",
                modifiable_aspects=["voice_selection", "emotion_control", "background_audio", "quality_settings"],
                quality_options={
                    "quick": {"quality": "standard", "effects": "minimal"},
                    "standard": {"quality": "high", "effects": "moderate"},
                    "premium": {"quality": "professional", "effects": "rich"}
                }
            ),
            StepTemplate(
                template_step_id="match_video",
                name="匹配视频片段",
                description_template="智能匹配现有视频素材",
                tool_to_use="visual_expert",
                dependencies=["generate_audio"],
                input_template={
                    "prompt": """基于生成的剧本和音频，智能匹配最合适的视频片段。

匹配规则：
1. 角色匹配：根据台词角色选择对应视频分类
2. 情感匹配：根据台词情感选择合适的视频片段
3. 内容匹配：根据台词内容选择最贴切的场景

输出视频序列的详细匹配方案，包括每个片段的使用理由。""",

                    # 🚀 预制的详细视频素材库
                    "video_assets": {
                        "doctor_clips": [
                            {
                                "id": "doctor_01",
                                "title": "自我介绍广告",
                                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/6dccee7f-d492-4593-8456-25d45652ed19.mp4",
                                "duration": "17.2秒",
                                "emotion": "专业、自信",
                                "content_type": "介绍"
                            },
                            {
                                "id": "doctor_02",
                                "title": "诊断更年期症状",
                                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/ae9ee98c-9f9a-43b7-9df5-d9c6746166f0.mp4",
                                "duration": "13.8秒",
                                "emotion": "严肃、专业",
                                "content_type": "诊断"
                            },
                            {
                                "id": "doctor_03",
                                "title": "安慰患者无病",
                                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/e0696fd5-fde6-4bf7-8cd4-11556d9c262f.mp4",
                                "duration": "18.4秒",
                                "emotion": "温和、安慰",
                                "content_type": "安慰"
                            },
                            {
                                "id": "doctor_04",
                                "title": "调解家庭矛盾",
                                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/8200078b-5d87-4490-9658-2115fda0031b.mp4",
                                "duration": "10.2秒",
                                "emotion": "调解、中性",
                                "content_type": "调解"
                            },
                            {
                                "id": "doctor_05",
                                "title": "询问详细情况",
                                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/734fb258-0c8c-44b9-9e7c-635f58da9258.mp4",
                                "duration": "10.7秒",
                                "emotion": "询问、关切",
                                "content_type": "询问"
                            }
                        ],
                        "wife_clips": [
                            {
                                "id": "wife_01",
                                "title": "挂急诊求医",
                                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/e27f5201-59ee-4996-88c2-8033931ffea3.mp3",
                                "duration": "11.3秒",
                                "emotion": "焦急、担心",
                                "content_type": "求助"
                            },
                            {
                                "id": "wife_02",
                                "title": "不想告诉真相",
                                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/1119192d-a067-47c1-9174-bcb0f7def27c.mp4",
                                "duration": "10.8秒",
                                "emotion": "犹豫、纠结",
                                "content_type": "隐瞒"
                            },
                            {
                                "id": "wife_03",
                                "title": "感情上的愧疚",
                                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/7ccaae10-089f-44f2-92ef-541048837cac.mp4",
                                "duration": "17.4秒",
                                "emotion": "愧疚、难过",
                                "content_type": "忏悔"
                            }
                        ],
                        "patient_clips": [
                            {
                                "id": "patient_01",
                                "title": "内心困惑表达",
                                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/2c03e13f-62d7-4943-93d4-960769128fda.mp4",
                                "duration": "16.5秒",
                                "emotion": "困惑、迷茫",
                                "content_type": "表达"
                            },
                            {
                                "id": "patient_02",
                                "title": "哲学性思考",
                                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/103e9db4-d44e-4f77-96bc-9d931b608d3c.mp4",
                                "duration": "5.8秒",
                                "emotion": "深思、哲学",
                                "content_type": "思考"
                            },
                            {
                                "id": "patient_03",
                                "title": "症状表现描述",
                                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/d286027e-8871-47d7-8ee9-894c17d47146.mp4",
                                "duration": "4.0秒",
                                "emotion": "描述、平静",
                                "content_type": "描述"
                            },
                            {
                                "id": "patient_04",
                                "title": "坚持自己观点",
                                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/fb2bfaa0-e8c1-4bc7-803d-5859ecd96cfb.mp4",
                                "duration": "5.4秒",
                                "emotion": "坚持、固执",
                                "content_type": "坚持"
                            }
                        ]
                    }
                },
                estimated_duration=240
            ),
            StepTemplate(
                template_step_id="compose_video",
                name="合成最终视频",
                description_template="将音频和视频合成完整小品作品",
                tool_to_use="video_expert",
                dependencies=["match_video"],
                input_template={
                    "task_description": """将匹配的视频片段和TTS音频合成为完整的小品视频。

合成要求：
1. 视频序列：按剧本顺序排列视频片段，确保时长与音频匹配
2. 音频处理：将TTS音频与视频同步，调整音量平衡
3. 视觉效果：添加字幕显示台词，保持视频清晰度
4. 背景音乐：添加轻松的东北风格背景音乐
5. 转场效果：添加适当的转场，保持视频流畅

时长控制：根据duration参数调整最终视频长度

最终输出：MP4格式，1080p清晰度的完整小品视频。"""
                },
                estimated_duration=900
            )
        ],

        estimated_duration=2040,  # 34分钟
        difficulty_level="advanced"
    )


def create_crosstalk_video_template() -> PlanTemplate:
    """Create the Crosstalk (相声) video template with master-agent-handled script generation.

    约束对齐：
    - 剧本改写/文化适配(create_script)由主Agent自行完成，本模板不包含该步骤
    - 合成语音步骤(synth_dialogue_audio)直接返回完整时间戳（无需单独timestamp步骤）
    - 现成素材匹配(generate_scene_descriptions简化为素材匹配)，不生成场景描述，仅做匹配
    """
    return PlanTemplate(
        template_id="crosstalk_video",
        name="相声 Cross Talk 视频生成",
        description="基于已生成的相声剧本进行多说话人TTS并输出毫秒级时间戳，匹配现成视频素材并合成成片。",
        category="entertainment",
        tags=["crosstalk", "dialogue", "tts", "video", "timestamps", "matching"],

        parameters={
            # 由主Agent先行生成剧本文本，再通过use_template传入
            "script_text": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="完整相声剧本文本（含角色与台词标识，如：[Tone] 角色: 台词）"
            ),
            "dou_gen_name": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="逗哏角色名"
            ),
            "peng_gen_name": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="捧哏角色名"
            ),
            # 风格/时长偏好供工具内部策略参考
            "style": ParameterSchema(
                type=ParameterType.STRING,
                default="经典",
                description="相声风格偏好，可自由描述，如：经典、现代、荒诞、传统、创新等"
            ),
            "duration": ParameterSchema(
                type=ParameterType.STRING,
                default="中等",
                description="时长偏好，可自由描述，如：短、中等、长、5分钟、10分钟等"
            ),
            "include_bgm": ParameterSchema(
                type=ParameterType.BOOLEAN,
                default=True,
                description="是否在最终成片中添加BGM"
            ),
            # 现成素材路径或索引（工程侧按需选择其一）
            "materials_dir": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="现成视频素材的根目录（或已构建的片段库目录）"
            )
        },

        step_templates=[
            # 1) 多角色TTS + 分段 + 输出时间戳
            StepTemplate(
                template_step_id="synth_dialogue_audio",
                name="多角色语音合成与时间戳生成",
                description_template="基于剧本为{dou_gen_name}/{peng_gen_name}生成多人TTS，并输出seg目录与毫秒级时间戳",
                tool_to_use="audio_expert",
                input_template={
                    "task_description": "解析剧本逐行识别角色/语调，生成分段音频与timestamps.json",
                    "script": "{script_text}",
                    "roles": ["{dou_gen_name}", "{peng_gen_name}"],
                    "tone_set": ["Natural", "Emphatic", "Confused"],
                    "include_background_music": "{include_bgm}",
                    "export_segments": True,
                    "export_timestamps": True
                },
                estimated_duration=600
            ),

            # 2) 现成素材匹配（不生成场景描述，仅做匹配/挑选）
            StepTemplate(
                template_step_id="match_existing_materials",
                name="匹配现成视频素材",
                description_template="根据对话与时间戳，在{materials_dir}中匹配合适的视频片段",
                tool_to_use="video_expert",
                dependencies=["synth_dialogue_audio"],
                input_template={
                    "task_description": "基于角色/语义/时长匹配现成片段，输出匹配结果清单",
                    "materials_dir": "{materials_dir}",
                    "timestamps": "{{synth_dialogue_audio.assets.timestamps}}",
                    "dialogue_meta": "{{synth_dialogue_audio.assets.files}}",
                    "matching_strategy": "role_and_semantic"
                },
                estimated_duration=240
            ),

            # 3) 精确合成
            StepTemplate(
                template_step_id="compose_video",
                name="视频合成与精确对齐",
                description_template="按时间戳精确剪辑并合成最终视频，叠加必要的字幕与BGM",
                tool_to_use="video_expert",
                dependencies=["match_existing_materials"],
                input_template={
                    "task_description": "用timestamps对齐seg音频与已匹配的视频片段，输出成片",
                    "timestamps": "{{synth_dialogue_audio.assets.timestamps}}",
                    "matched_segments": "{{match_existing_materials.assets.files}}",
                    "audio_segments": "{{synth_dialogue_audio.assets.audio}}",
                    "include_bgm": "{include_bgm}",
                    "output_format": "mp4",
                    "resolution": "1080p"
                },
                estimated_duration=900
            )
        ],

        estimated_duration=600 + 240 + 900,
        difficulty_level="advanced"
    )


def create_flexible_zhao_benshan_template_wrapper() -> PlanTemplate:
    """Create a wrapper for the flexible Zhao Benshan template to work with the existing system."""
    return PlanTemplate(
        template_id="flexible_zhao_benshan_xiangsheng",
        name="高自由度赵本山小品生成器",
        description="完全可定制的赵本山风格小品创作模板，主agent拥有完全的创作自由度。支持动态步骤调整、工具替换、参数优化等高级功能。",
        category="entertainment",
        tags=["xiangsheng", "zhao_benshan", "northeast", "comedy", "video", "tts", "flexible", "customizable"],

        parameters={
            "topic": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="小品主题，可以在创作过程中调整和细化"
            ),
            "style": ParameterSchema(
                type=ParameterType.STRING,
                default="经典",
                description="风格偏好，可自由描述，支持混合风格或创造新风格，如：经典、现代、荒诞、自定义等"
            ),
            "duration": ParameterSchema(
                type=ParameterType.STRING,
                default="中等",
                description="时长偏好，可自由描述，可根据内容需要动态调整，如：短、中等、长、自定义等"
            ),
            "quality_level": ParameterSchema(
                type=ParameterType.STRING,
                default="标准",
                description="质量要求，可自由描述，影响处理精度和时间投入，如：快速、标准、高级、大师级等"
            ),
            "creativity_level": ParameterSchema(
                type=ParameterType.STRING,
                default="中等",
                description="创意自由度，可自由描述，决定AI的创新程度，如：保守、中等、高、实验性等"
            )
        },

        step_templates=[
            StepTemplate(
                template_step_id="flexible_content_creation",
                name="灵活内容创作",
                description_template="创作关于{topic}的{style}风格小品内容，质量级别：{quality_level}，创意程度：{creativity_level}",
                tool_to_use="use_flexible_template",
                input_template={
                    "template_id": "flexible_zhao_benshan_xiangsheng",
                    "user_params": {
                        "topic": "{topic}",
                        "style": "{style}",
                        "duration": "{duration}"
                    },
                    "user_preferences": {
                        "quality": "{quality_level}",
                        "creativity": "{creativity_level}"
                    },
                    "context": "赵本山风格小品创作，支持完全自定义"
                },
                estimated_duration=1800  # 30分钟，因为包含自定义过程
            )
        ],

        estimated_duration=1800,  # 30分钟
        difficulty_level="advanced"
    )


def load_builtin_templates():
    """Load and register all built-in templates."""
    templates = [
        create_ai_parody_video_template(),
        create_product_promo_template(),
        create_city_poster_series_template(),
        create_zhao_benshan_xiangsheng_template(),
        create_flexible_zhao_benshan_template_wrapper(),
        create_crosstalk_video_template(),
        create_story_rewrite_template()
    ]

    for template in templates:
        register_template(template)

    print(f"Loaded {len(templates)} built-in templates")
    return templates

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
flexible_template_system.py

高自由度的模板系统，支持主agent的完全自定义和动态调整。
"""

from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import json

class FlexibilityLevel(Enum):
    """模板灵活性级别"""
    RIGID = "rigid"           # 严格模板，不可修改
    GUIDED = "guided"         # 引导模板，可以调整
    FLEXIBLE = "flexible"     # 灵活模板，大幅修改
    CREATIVE = "creative"     # 创意模板，完全自由


@dataclass
class FlexibleStepTemplate:
    """
    高自由度的步骤模板
    
    支持：
    1. 动态步骤名称生成
    2. 智能工具选择
    3. 自适应参数调整
    4. 条件分支执行
    """
    
    # 基础信息
    step_id: str
    category: str  # 步骤类别：content_creation, audio_processing, video_editing, etc.
    
    # 灵活配置
    flexibility_level: FlexibilityLevel = FlexibilityLevel.FLEXIBLE
    
    # 目标描述（而非固定名称）
    objective: str  # 这一步要达成什么目标
    success_criteria: List[str] = field(default_factory=list)  # 成功标准
    
    # 工具建议（而非固定工具）
    suggested_tools: List[str] = field(default_factory=list)  # 建议的工具
    alternative_tools: List[str] = field(default_factory=list)  # 备选工具
    
    # 参数指导（而非固定参数）
    parameter_guidelines: Dict[str, Any] = field(default_factory=dict)  # 参数指导
    customization_hints: List[str] = field(default_factory=list)  # 自定义提示
    
    # 依赖关系
    dependencies: List[str] = field(default_factory=list)
    optional_dependencies: List[str] = field(default_factory=list)
    
    # 质量控制
    quality_checks: List[str] = field(default_factory=list)  # 质量检查点
    fallback_strategies: List[str] = field(default_factory=list)  # 失败后的备选策略


@dataclass
class FlexibleTemplate:
    """
    高自由度的模板定义
    
    特点：
    1. 提供指导而非约束
    2. 支持动态调整
    3. 智能适应用户需求
    """
    
    template_id: str
    name: str
    description: str
    category: str
    
    # 灵活性配置
    overall_flexibility: FlexibilityLevel = FlexibilityLevel.FLEXIBLE
    allow_step_reordering: bool = True
    allow_step_addition: bool = True
    allow_step_removal: bool = True
    allow_tool_substitution: bool = True
    
    # 步骤模板
    step_templates: List[FlexibleStepTemplate] = field(default_factory=list)
    
    # 参数配置
    required_params: Dict[str, Any] = field(default_factory=dict)
    optional_params: Dict[str, Any] = field(default_factory=dict)
    dynamic_params: Dict[str, Any] = field(default_factory=dict)  # 可动态生成的参数
    
    # 智能提示
    customization_suggestions: List[str] = field(default_factory=list)
    common_modifications: List[str] = field(default_factory=list)
    expert_tips: List[str] = field(default_factory=list)


class FlexibleTemplateRenderer:
    """
    灵活模板渲染器
    
    支持：
    1. 动态步骤生成
    2. 智能参数推断
    3. 自适应优化
    """
    
    def __init__(self):
        self.customization_history = []  # 记录用户的自定义历史
    
    def render_adaptive_plan(self, 
                           template: FlexibleTemplate, 
                           user_params: Dict[str, Any],
                           user_preferences: Optional[Dict[str, Any]] = None,
                           context: Optional[str] = None) -> Dict[str, Any]:
        """
        渲染自适应计划
        
        Args:
            template: 灵活模板
            user_params: 用户参数
            user_preferences: 用户偏好
            context: 上下文信息
        """
        
        # 1. 分析用户需求和偏好
        analysis = self._analyze_user_requirements(user_params, user_preferences, context)
        
        # 2. 生成自适应步骤
        adaptive_steps = self._generate_adaptive_steps(template, analysis)
        
        # 3. 优化工具选择
        optimized_steps = self._optimize_tool_selection(adaptive_steps, analysis)
        
        # 4. 生成自定义建议
        customization_suggestions = self._generate_customization_suggestions(template, analysis)
        
        return {
            "plan": {
                "steps": optimized_steps,
                "flexibility_info": {
                    "modifiable_aspects": self._get_modifiable_aspects(template),
                    "customization_suggestions": customization_suggestions,
                    "alternative_approaches": self._get_alternative_approaches(template, analysis)
                }
            },
            "analysis": analysis
        }
    
    def _analyze_user_requirements(self, params, preferences, context):
        """分析用户需求"""
        return {
            "complexity_level": self._assess_complexity(params, context),
            "quality_expectations": self._assess_quality_expectations(preferences),
            "time_constraints": self._assess_time_constraints(preferences),
            "creative_freedom": self._assess_creative_freedom(preferences),
            "technical_preferences": self._assess_technical_preferences(preferences)
        }
    
    def _generate_adaptive_steps(self, template, analysis):
        """生成自适应步骤"""
        adaptive_steps = []
        
        for step_template in template.step_templates:
            # 根据分析结果调整步骤
            adaptive_step = {
                "step_id": step_template.step_id,
                "objective": step_template.objective,
                "suggested_name": self._generate_dynamic_name(step_template, analysis),
                "recommended_tool": self._select_optimal_tool(step_template, analysis),
                "alternative_tools": step_template.alternative_tools,
                "parameters": self._generate_adaptive_parameters(step_template, analysis),
                "customization_options": self._get_customization_options(step_template),
                "success_criteria": step_template.success_criteria,
                "flexibility_level": step_template.flexibility_level.value
            }
            adaptive_steps.append(adaptive_step)
        
        return adaptive_steps
    
    def _generate_dynamic_name(self, step_template, analysis):
        """动态生成步骤名称"""
        base_objective = step_template.objective
        
        # 根据分析结果调整名称
        if analysis["quality_expectations"] == "high":
            return f"精心{base_objective}"
        elif analysis["creative_freedom"] == "high":
            return f"创意{base_objective}"
        elif analysis["time_constraints"] == "tight":
            return f"快速{base_objective}"
        else:
            return base_objective
    
    def _select_optimal_tool(self, step_template, analysis):
        """选择最优工具"""
        suggested_tools = step_template.suggested_tools
        
        if not suggested_tools:
            return None
        
        # 根据分析结果选择最合适的工具
        if analysis["quality_expectations"] == "high" and "visual_expert" in suggested_tools:
            return "visual_expert"
        elif analysis["time_constraints"] == "tight" and "quick_generator" in suggested_tools:
            return "quick_generator"
        else:
            return suggested_tools[0]  # 默认选择第一个
    
    def _generate_adaptive_parameters(self, step_template, analysis):
        """生成自适应参数"""
        base_params = step_template.parameter_guidelines.copy()
        
        # 根据分析结果调整参数
        if analysis["quality_expectations"] == "high":
            base_params["quality_level"] = "premium"
        elif analysis["time_constraints"] == "tight":
            base_params["speed_priority"] = True
        
        return base_params
    
    def _get_customization_options(self, step_template):
        """获取自定义选项"""
        options = []
        
        if step_template.flexibility_level in [FlexibilityLevel.FLEXIBLE, FlexibilityLevel.CREATIVE]:
            options.extend([
                "修改步骤名称和描述",
                "更换执行工具",
                "调整参数配置",
                "添加质量检查",
                "设置备选策略"
            ])
        
        if step_template.flexibility_level == FlexibilityLevel.CREATIVE:
            options.extend([
                "完全重新设计步骤",
                "分解为多个子步骤",
                "与其他步骤合并",
                "添加创新元素"
            ])
        
        return options
    
    def _get_modifiable_aspects(self, template):
        """获取可修改的方面"""
        aspects = []
        
        if template.allow_step_reordering:
            aspects.append("步骤顺序")
        if template.allow_step_addition:
            aspects.append("添加新步骤")
        if template.allow_step_removal:
            aspects.append("删除步骤")
        if template.allow_tool_substitution:
            aspects.append("工具替换")
        
        return aspects
    
    def _generate_customization_suggestions(self, template, analysis):
        """生成自定义建议"""
        suggestions = template.customization_suggestions.copy()
        
        # 根据分析结果添加个性化建议
        if analysis["creative_freedom"] == "high":
            suggestions.extend([
                "考虑添加创新元素或独特风格",
                "尝试非传统的执行方法",
                "融入个人特色和偏好"
            ])
        
        if analysis["quality_expectations"] == "high":
            suggestions.extend([
                "增加质量检查步骤",
                "使用高级工具和参数",
                "添加多轮优化流程"
            ])
        
        return suggestions
    
    def _get_alternative_approaches(self, template, analysis):
        """获取替代方案"""
        approaches = []
        
        # 基于模板和分析生成替代方案
        if template.category == "content_creation":
            approaches.extend([
                "分阶段创作方法",
                "协作式创作流程",
                "迭代优化方法"
            ])
        
        return approaches
    
    # 辅助方法
    def _assess_complexity(self, params, context):
        """评估复杂度"""
        # 简化实现
        return "medium"
    
    def _assess_quality_expectations(self, preferences):
        """评估质量期望"""
        return preferences.get("quality", "medium") if preferences else "medium"
    
    def _assess_time_constraints(self, preferences):
        """评估时间约束"""
        return preferences.get("urgency", "normal") if preferences else "normal"
    
    def _assess_creative_freedom(self, preferences):
        """评估创意自由度"""
        return preferences.get("creativity", "medium") if preferences else "medium"
    
    def _assess_technical_preferences(self, preferences):
        """评估技术偏好"""
        return preferences.get("technical_level", "standard") if preferences else "standard"


# 全局渲染器实例
flexible_renderer = FlexibleTemplateRenderer()

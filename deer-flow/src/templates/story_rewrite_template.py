# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
小品故事改写模板

基于用户上传的视频，提取原始脚本，根据用户要求改写故事内容，
生成新的配音和字幕，最终合成新视频。

工作流程：
1. 视频脚本提取 - 使用video_subtitle_extraction工具
2. 脚本改写 - 主agent直接处理
3. 新配音生成 - 使用multi_speaker_tts工具  
4. 视频合成 - 使用creatomate_video_tool_v2工具
"""

from src.graph_v2.template_models import (
    PlanTemplate, StepTemplate, ParameterSchema, ParameterType
)

def create_story_rewrite_template() -> PlanTemplate:
    """创建小品故事改写模板"""
    
    return PlanTemplate(
        template_id="story_rewrite",
        name="小品故事改写",
        description="基于用户上传的视频，提取原始脚本，根据用户要求改写故事内容，生成新的配音和字幕，最终合成新视频",
        category="视频创作",
        tags=["视频处理", "脚本改写", "配音", "字幕", "故事创作"],
        
        parameters={
            "video_file": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="需要改写的原始视频文件路径或URL"
            ),
            "rewrite_requirements": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="改写要求，详细描述希望如何修改故事内容，如：改成现代背景、增加幽默元素、改变角色关系等"
            ),
            "voice_style": ParameterSchema(
                type=ParameterType.STRING,
                default="保持原风格",
                description="配音风格要求，如：保持原风格、更加幽默、更加严肃、年轻化等"
            ),
            "subtitle_style": ParameterSchema(
                type=ParameterType.STRING,
                default="标准",
                description="字幕样式，如：标准、彩色、大字体、动态效果等"
            )
        },
        
        step_templates=[
            # 步骤1：提取视频脚本
            StepTemplate(
                template_step_id="extract_script",
                name="提取视频脚本",
                description_template="从上传的视频中提取音视频脚本和时间戳信息",
                tool_to_use="video_subtitle_extraction",
                input_template={
                    "media_url": "{video_file}",
                    "language": "zh-CN",
                    "speaker_detection": True,
                    "output_format": "json",
                    "extract_audio_segments": True,
                    "save_to_cos": False,
                    "task_description": """请提取视频中的完整对话脚本，包含：
1. 每句话的准确时间戳（开始时间和结束时间）
2. 说话人识别和角色命名
3. 对话内容的完整文本
4. 语音的情感和语调信息

输出格式要求：
- JSON格式，包含时间轴信息
- 每句对话包含：时间戳、说话人、文本内容
- 便于后续脚本改写和配音生成"""
                },
                estimated_duration=120,
                dependencies=[]
            ),
            
            # 步骤2：改写故事脚本
            StepTemplate(
                template_step_id="rewrite_script",
                name="改写故事脚本",
                description_template="根据用户要求改写原始脚本，保持时间戳结构",
                tool_to_use="direct_generation",
                dependencies=["extract_script"],
                input_template={
                    "instruction": """请根据用户的改写要求，对原始脚本进行创意改写。

**改写要求**：{rewrite_requirements}

**重要原则**：
1. **保持时间戳结构**：改写后的脚本必须保持原有的时间轴结构
2. **保持对话节奏**：新对话的长度应与原对话相近，确保时间同步
3. **角色一致性**：保持原有的角色数量和基本设定
4. **情感连贯性**：确保改写后的情感变化自然流畅

**输出格式**：
```json
{
  "rewritten_script": [
    {
      "start_time": "00:00:01.000",
      "end_time": "00:00:03.500", 
      "speaker": "角色名",
      "original_text": "原始对话",
      "rewritten_text": "改写后的对话",
      "emotion": "情感标注（开心/严肃/惊讶等）",
      "voice_style": "语音风格要求"
    }
  ],
  "summary": "改写说明和主要变化"
}
```

**创作要求**：
- 根据改写要求调整故事情节
- 保持对话的自然性和趣味性
- 确保改写后的内容符合时长限制
- 为每句话标注情感色彩和语音风格

请开始改写脚本。"""
                },
                estimated_duration=300
            ),

            # 步骤3：生成新配音
            StepTemplate(
                template_step_id="generate_new_audio",
                name="生成新配音",
                description_template="为改写后的脚本生成多角色TTS配音",
                tool_to_use="multi_speaker_tts",
                dependencies=["rewrite_script"],
                input_template={
                    "dialogue_list": "从改写脚本中提取的对话列表",
                    "voice_style": "{voice_style}",
                    "task_description": """根据改写后的脚本生成多角色TTS配音。

**配音要求**：
- 配音风格：{voice_style}
- 根据脚本中的角色分配不同的声音
- 保持与原视频相似的语速和节奏
- 体现脚本中标注的情感色彩

**技术要求**：
1. 为每个角色选择合适的声音ID
2. 根据情感标注调整语调和语速
3. 确保音频质量清晰
4. 保持时间戳的准确性

**输入格式**：
从改写脚本的JSON中提取dialogue_list，格式为：
[
  {
    "speaker": "角色名",
    "text": "改写后的对话",
    "emotion": "情感标注",
    "speed": 1.0
  }
]

请生成完整的多角色配音文件。"""
                },
                estimated_duration=240
            ),

            # 步骤4：合成最终视频
            StepTemplate(
                template_step_id="compose_final_video",
                name="合成最终视频",
                description_template="将新配音、新字幕与原视频画面合成",
                tool_to_use="creatomate_video_tool_v2",
                dependencies=["generate_new_audio"],
                input_template={
                    "input_mode": "natural_language",
                    "task_description": """合成最终的改写视频。

**合成要求**：
1. **视频画面**：使用原视频的画面（静音处理）
2. **音频轨道**：使用新生成的TTS配音
3. **字幕轨道**：添加改写后的脚本作为字幕
4. **时间同步**：确保音频、字幕与画面完美同步

**字幕样式**：{subtitle_style}

**技术参数**：
- 保持原视频的分辨率和帧率
- 字幕位置：底部居中
- 字幕字体：清晰易读
- 背景音乐：可选择保留原视频的背景音（音量降低）

**输出要求**：
- 生成高质量的MP4视频文件
- 确保音画同步准确
- 字幕显示时机精确

**输入资源**：
- 原视频：{video_file}
- 新配音：从上一步生成的TTS音频
- 字幕文本：从改写脚本中提取

请开始视频合成。""",
                    "subtitle_style": "{subtitle_style}",
                    "original_video": "{video_file}"
                },
                estimated_duration=300
            )
        ],
        
        estimated_duration=960,  # 16分钟
        difficulty_level="intermediate"
    )

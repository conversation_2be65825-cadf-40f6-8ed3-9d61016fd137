#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
flexible_zhao_benshan_template.py

高自由度的赵本山小品模板，支持主agent的完全自定义。
"""

from .flexible_template_system import (
    FlexibleTemplate, 
    FlexibleStepTemplate, 
    FlexibilityLevel
)


def create_flexible_zhao_benshan_template() -> FlexibleTemplate:
    """
    创建高自由度的赵本山小品模板
    
    特点：
    1. 主agent可以自由调整所有步骤
    2. 支持动态工具选择
    3. 智能参数推荐
    4. 完全的创意自由
    """
    
    return FlexibleTemplate(
        template_id="flexible_zhao_benshan_xiangsheng",
        name="高自由度赵本山小品生成器",
        description="完全可定制的赵本山风格小品创作模板，主agent拥有完全的创作自由度",
        category="entertainment_creation",
        
        # 最高自由度配置
        overall_flexibility=FlexibilityLevel.CREATIVE,
        allow_step_reordering=True,
        allow_step_addition=True,
        allow_step_removal=True,
        allow_tool_substitution=True,
        
        step_templates=[
            # 步骤1：内容创作（完全自由）
            FlexibleStepTemplate(
                step_id="content_creation",
                category="content_creation",
                flexibility_level=FlexibilityLevel.CREATIVE,
                
                objective="创作小品内容",
                success_criteria=[
                    "剧本具有东北特色和幽默感",
                    "角色对话生动有趣",
                    "情节结构完整",
                    "包含经典包袱和笑点"
                ],
                
                suggested_tools=[
                    "visual_expert",      # 专业创作
                    "create_plan",        # 结构化创作
                    "audio_expert"        # 对话优化
                ],
                alternative_tools=[
                    "quick_generator",    # 快速生成
                    "collaborative_tool", # 协作创作
                    "template_mixer"      # 模板混合
                ],
                
                parameter_guidelines={
                    "content_type": "小品剧本",
                    "style_keywords": ["东北话", "幽默", "生活化", "接地气"],
                    "character_archetypes": ["赵本山式患者", "宋丹丹式家属", "专业医生"],
                    "tone_options": ["轻松幽默", "温馨感人", "荒诞搞笑", "现实讽刺"],
                    "length_flexibility": "可根据需要调整长度",
                    "quality_levels": ["快速版", "标准版", "精品版", "大师版"]
                },
                
                customization_hints=[
                    "可以调整角色设定和性格特点",
                    "可以修改情节发展和冲突设置",
                    "可以添加当代元素或经典回忆",
                    "可以融入用户的个人经历或偏好",
                    "可以调整幽默风格和笑点密度",
                    "可以设置不同的结局方向"
                ],
                
                quality_checks=[
                    "检查东北方言的准确性",
                    "验证笑点的有效性",
                    "确保角色性格一致性",
                    "评估情节的合理性"
                ],
                
                fallback_strategies=[
                    "如果创作卡顿，可以先生成大纲再细化",
                    "如果风格不对，可以参考经典小品片段",
                    "如果缺乏灵感，可以结合时事热点",
                    "如果质量不佳，可以多轮迭代优化"
                ]
            ),
            
            # 步骤2：音频处理（高度灵活）
            FlexibleStepTemplate(
                step_id="audio_processing",
                category="audio_processing",
                flexibility_level=FlexibilityLevel.FLEXIBLE,
                
                objective="生成角色语音和音效",
                success_criteria=[
                    "声音克隆质量高",
                    "情感表达到位",
                    "音频清晰无杂音",
                    "语速和节奏合适"
                ],
                
                suggested_tools=[
                    "audio_expert",       # 专业音频处理
                    "voice_cloning_tool", # 声音克隆
                    "audio_mixer"         # 音频混合
                ],
                alternative_tools=[
                    "tts_engine",         # 文本转语音
                    "audio_enhancer",     # 音频增强
                    "sound_effects_lib"   # 音效库
                ],
                
                parameter_guidelines={
                    "voice_assets": {
                        "zhaobenshan": "zhaobenshan_voice_clone_test_001",
                        "songdandan": "cloned_voice_from_host_audio_20240730",
                        "doctor": "professional_male_voice"
                    },
                    "emotion_control": ["开心", "困惑", "焦急", "愤怒", "温和"],
                    "speed_control": ["慢速", "正常", "快速", "变速"],
                    "background_options": ["无背景", "轻音乐", "环境音", "音效"],
                    "quality_settings": ["标准", "高清", "专业级"]
                },
                
                customization_hints=[
                    "可以调整每个角色的语音特色",
                    "可以添加背景音乐和环境音效",
                    "可以设置不同场景的音频氛围",
                    "可以优化对话的节奏和停顿",
                    "可以添加笑声、掌声等互动音效"
                ],
                
                dependencies=["content_creation"],
                optional_dependencies=["script_analysis", "emotion_mapping"]
            ),
            
            # 步骤3：视觉匹配（智能灵活）
            FlexibleStepTemplate(
                step_id="visual_matching",
                category="video_processing",
                flexibility_level=FlexibilityLevel.FLEXIBLE,
                
                objective="匹配和优化视频素材",
                success_criteria=[
                    "视频与音频同步",
                    "画面与内容匹配",
                    "视觉效果流畅",
                    "整体风格统一"
                ],
                
                suggested_tools=[
                    "visual_expert",      # 视觉专家
                    "video_matcher",      # 视频匹配
                    "scene_analyzer"      # 场景分析
                ],
                alternative_tools=[
                    "ai_video_generator", # AI视频生成
                    "stock_video_search", # 素材库搜索
                    "custom_animation"    # 自定义动画
                ],
                
                parameter_guidelines={
                    "matching_strategy": ["智能匹配", "手动选择", "混合模式"],
                    "video_style": ["真实", "卡通", "混合", "艺术化"],
                    "transition_effects": ["淡入淡出", "切换", "特效", "无过渡"],
                    "visual_enhancement": ["色彩调整", "清晰度优化", "风格滤镜"]
                },
                
                customization_hints=[
                    "可以替换不合适的视频片段",
                    "可以调整视频的播放速度",
                    "可以添加视觉特效和转场",
                    "可以优化画面的色彩和亮度",
                    "可以添加字幕和标注"
                ],
                
                dependencies=["audio_processing"],
                optional_dependencies=["content_creation"]
            ),
            
            # 步骤4：最终合成（完全自定义）
            FlexibleStepTemplate(
                step_id="final_composition",
                category="video_editing",
                flexibility_level=FlexibilityLevel.CREATIVE,
                
                objective="合成最终作品",
                success_criteria=[
                    "音画完美同步",
                    "整体效果流畅",
                    "质量达到预期",
                    "符合用户需求"
                ],
                
                suggested_tools=[
                    "video_expert",       # 视频专家
                    "final_editor",       # 最终编辑
                    "quality_enhancer"    # 质量增强
                ],
                alternative_tools=[
                    "quick_composer",     # 快速合成
                    "professional_suite", # 专业套件
                    "cloud_renderer"      # 云端渲染
                ],
                
                parameter_guidelines={
                    "output_quality": ["720p", "1080p", "4K", "自适应"],
                    "format_options": ["MP4", "AVI", "MOV", "WebM"],
                    "compression": ["无压缩", "轻度", "标准", "高压缩"],
                    "additional_elements": ["片头", "片尾", "水印", "字幕", "特效"]
                },
                
                customization_hints=[
                    "可以添加开场和结尾动画",
                    "可以调整整体的色调和风格",
                    "可以优化音频的平衡和音量",
                    "可以添加互动元素或彩蛋",
                    "可以生成多个版本供选择",
                    "可以针对不同平台优化格式"
                ],
                
                dependencies=["visual_matching"],
                optional_dependencies=["content_creation", "audio_processing"],
                
                quality_checks=[
                    "检查音画同步精度",
                    "验证输出质量",
                    "测试播放兼容性",
                    "评估用户满意度"
                ]
            )
        ],
        
        # 参数配置（高度灵活）
        required_params={
            "topic": {
                "type": "string",
                "description": "小品主题",
                "flexibility": "可以在创作过程中调整和细化"
            }
        },
        
        optional_params={
            "style": {
                "type": "string", 
                "options": ["classic", "modern", "absurd", "custom"],
                "description": "风格偏好",
                "flexibility": "可以混合多种风格或创造新风格"
            },
            "duration": {
                "type": "string",
                "options": ["short", "medium", "long", "custom"],
                "description": "时长偏好", 
                "flexibility": "可以根据内容需要动态调整"
            },
            "quality_level": {
                "type": "string",
                "options": ["quick", "standard", "premium", "masterpiece"],
                "description": "质量要求"
            }
        },
        
        dynamic_params={
            "user_preferences": "根据对话动态收集用户偏好",
            "creative_direction": "基于用户反馈调整创作方向",
            "technical_requirements": "根据输出需求调整技术参数"
        },
        
        # 智能建议
        customization_suggestions=[
            "🎭 角色创新：可以添加新角色或调整现有角色性格",
            "📝 剧情扩展：可以增加子情节或改变故事走向", 
            "🎵 音效升级：可以添加背景音乐、音效或环境声",
            "🎬 视觉创新：可以使用特效、动画或创意转场",
            "⚡ 节奏调整：可以改变整体节奏或重点段落的处理",
            "🎯 个性定制：可以融入个人元素或特殊要求"
        ],
        
        common_modifications=[
            "调整角色对话风格和语言特色",
            "修改情节发展和冲突设置",
            "优化笑点分布和幽默效果",
            "改变视频风格和视觉效果",
            "调整音频质量和背景音乐",
            "自定义开头结尾和整体结构"
        ],
        
        expert_tips=[
            "💡 创作技巧：先确定核心冲突，再围绕冲突设计笑点",
            "🎪 表演建议：注意语言节奏和情感层次的变化",
            "🔧 技术优化：音频质量比视频质量更重要",
            "🎨 视觉设计：保持画面简洁，突出人物表演",
            "⏰ 时长控制：每个笑点间隔30-60秒最佳",
            "🎯 受众考虑：可以针对不同年龄群体调整内容"
        ]
    )

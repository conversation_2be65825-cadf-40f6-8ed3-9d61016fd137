#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
赵本山小品生成模板

这是一个复杂的多媒体模板，用于生成赵本山风格的小品视频。
包含剧本创作、多人TTS、视频片段匹配、视频合成等多个步骤。
"""

from typing import Dict, Any, List
from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
from src.templates.template_definitions import Template, StepTemplate


def create_zhao_benshan_xiangsheng_template() -> Template:
    """
    创建赵本山小品生成模板
    
    这个模板实现了完整的小品视频生成流程：
    1. 剧本创作 - 根据用户需求生成小品剧本
    2. 角色分配 - 将台词分配给不同角色
    3. TTS语音合成 - 使用克隆声音生成对话
    4. 视频片段匹配 - 智能匹配现有视频素材
    5. 视频合成 - 将音频和视频合成最终作品
    """
    
    # 预定义的视频资产
    video_assets = {
        "doctor_clips": [
            {
                "id": "doctor_01",
                "title": "自我介绍广告",
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/6dccee7f-d492-4593-8456-25d45652ed19.mp4",
                "duration": "17.2秒",
                "emotion": "专业、自信",
                "content_type": "介绍"
            },
            {
                "id": "doctor_02",
                "title": "诊断更年期症状", 
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/ae9ee98c-9f9a-43b7-9df5-d9c6746166f0.mp4",
                "duration": "13.8秒",
                "emotion": "严肃、专业",
                "content_type": "诊断"
            },
            {
                "id": "doctor_03",
                "title": "安慰患者无病",
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/e0696fd5-fde6-4bf7-8cd4-11556d9c262f.mp4", 
                "duration": "18.4秒",
                "emotion": "温和、安慰",
                "content_type": "安慰"
            },
            {
                "id": "doctor_04",
                "title": "调解家庭矛盾",
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/8200078b-5d87-4490-9658-2115fda0031b.mp4",
                "duration": "10.2秒", 
                "emotion": "调解、中性",
                "content_type": "调解"
            },
            {
                "id": "doctor_05",
                "title": "询问详细情况",
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/734fb258-0c8c-44b9-9e7c-635f58da9258.mp4",
                "duration": "10.7秒",
                "emotion": "询问、关切", 
                "content_type": "询问"
            }
        ],
        "wife_clips": [
            {
                "id": "wife_01",
                "title": "挂急诊求医",
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/e27f5201-59ee-4996-88c2-8033931ffea3.mp4",
                "duration": "11.3秒",
                "emotion": "焦急、担心",
                "content_type": "求助"
            },
            {
                "id": "wife_02",
                "title": "不想告诉真相", 
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/1119192d-a067-47c1-9174-bcb0f7def27c.mp4",
                "duration": "10.8秒",
                "emotion": "犹豫、纠结",
                "content_type": "隐瞒"
            },
            {
                "id": "wife_03",
                "title": "感情上的愧疚",
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/7ccaae10-089f-44f2-92ef-541048837cac.mp4",
                "duration": "17.4秒",
                "emotion": "愧疚、难过",
                "content_type": "忏悔"
            },
            {
                "id": "wife_04",
                "title": "揭示彩票真相",
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/80383245-cd84-4f4a-a9b0-ad328ec6aaf3.mp4",
                "duration": "5.6秒",
                "emotion": "坦白、释然",
                "content_type": "揭示"
            },
            {
                "id": "wife_05",
                "title": "紧急求助医生",
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/c6e56fd9-c1f1-4190-a053-5b16604aecce.mp4",
                "duration": "6.0秒",
                "emotion": "紧急、恳求",
                "content_type": "求助"
            }
        ],
        "patient_clips": [
            {
                "id": "patient_01",
                "title": "内心困惑表达",
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/2c03e13f-62d7-4943-93d4-960769128fda.mp4",
                "duration": "16.5秒",
                "emotion": "困惑、迷茫",
                "content_type": "表达"
            },
            {
                "id": "patient_02", 
                "title": "哲学性思考",
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/103e9db4-d44e-4f77-96bc-9d931b608d3c.mp4",
                "duration": "5.8秒",
                "emotion": "深思、哲学",
                "content_type": "思考"
            },
            {
                "id": "patient_03",
                "title": "症状表现描述",
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/d286027e-8871-47d7-8ee9-894c17d47146.mp4",
                "duration": "4.0秒",
                "emotion": "描述、平静",
                "content_type": "描述"
            },
            {
                "id": "patient_04",
                "title": "坚持自己观点",
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/fb2bfaa0-e8c1-4bc7-803d-5859ecd96cfb.mp4",
                "duration": "5.4秒",
                "emotion": "坚持、固执",
                "content_type": "坚持"
            },
            {
                "id": "patient_05",
                "title": "康复后的感激",
                "url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/deerflow_assets/4cf69452-64dc-425c-b345-6214fb875919.mp4",
                "duration": "5.6秒",
                "emotion": "感激、高兴",
                "content_type": "感谢"
            }
        ]
    }
    
    # 声音克隆资产
    voice_assets = {
        "zhaobenshan": {
            "voice_id": "zhaobenshan_voice_clone_test_001",
            "sample_url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/%E8%AF%B4%E8%AF%9D%E4%BA%BA1_%E4%B8%BB%E6%8C%81%E4%BA%BA_59.0%E7%A7%92.wav",
            "character": "患者",
            "style": "东北话、幽默"
        },
        "songdandan": {
            "voice_id": "cloned_voice_from_host_audio_20240730", 
            "sample_url": "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/%E8%AF%B4%E8%AF%9D%E4%BA%BA3_%E4%B8%BB%E6%8C%81%E4%BA%BA_57.5%E7%A7%92.wav",
            "character": "患者家属",
            "style": "东北话、泼辣"
        }
    }
    
    return Template(
        template_id="zhao_benshan_xiangsheng",
        name="赵本山小品生成器",
        description="基于赵本山和宋丹丹的声音克隆，生成东北风格小品视频。支持自定义剧情，智能匹配视频片段，自动合成最终作品。",
        category="娱乐创作",
        step_templates=[
            # 步骤1：剧本创作
            StepTemplate(
                step_id="create_script",
                name="创作小品剧本：{{topic}}",
                description="根据用户提供的主题'{{topic}}'，创作一个赵本山风格的东北小品剧本。剧本应该包含医生、患者、患者家属三个角色，体现东北人的幽默和智慧。",
                tool_to_use="visual_expert",
                inputs={
                    "prompt": """创作一个赵本山风格的东北小品剧本，主题是：{{topic}}

要求：
1. 角色设定：
   - 医生：专业但有点迂腐，说话带点书面语
   - 患者（赵本山）：东北大叔，幽默风趣，有点固执
   - 患者家属（宋丹丹）：东北大嫂，泼辣直爽，关心家人

2. 剧本结构：
   - 开场：患者家属带患者看病
   - 发展：医生询问病情，患者表达困惑
   - 高潮：揭示真相或误会
   - 结尾：问题解决，皆大欢喜

3. 语言风格：
   - 东北方言特色
   - 幽默对话
   - 生活化场景
   - 包含包袱和笑点

4. 输出格式：
医生：[台词内容]
患者：[台词内容] 
患者家属：[台词内容]

请确保剧本有趣、贴近生活，体现东北人的幽默智慧。"""
                },
                dependencies=[]
            ),
            
            # 步骤2：角色台词分配
            StepTemplate(
                step_id="assign_roles",
                name="分配角色台词",
                description="将创作的剧本按角色分配台词，为后续的TTS语音合成做准备。",
                tool_to_use="visual_expert",
                inputs={
                    "prompt": """基于前面创作的小品剧本，将台词按角色分配并格式化。

要求：
1. 提取每个角色的所有台词
2. 按时间顺序排列
3. 标注情感色彩
4. 估算每段台词的时长

输出格式：
```json
{
  "script_segments": [
    {
      "order": 1,
      "character": "医生",
      "voice_id": "default_doctor_voice",
      "text": "台词内容",
      "emotion": "专业、严肃",
      "estimated_duration": "5秒"
    },
    {
      "order": 2, 
      "character": "患者",
      "voice_id": "zhaobenshan_voice_clone_test_001",
      "text": "台词内容",
      "emotion": "困惑、幽默",
      "estimated_duration": "8秒"
    },
    {
      "order": 3,
      "character": "患者家属", 
      "voice_id": "cloned_voice_from_host_audio_20240730",
      "text": "台词内容",
      "emotion": "焦急、关切",
      "estimated_duration": "6秒"
    }
  ]
}
```

请确保台词分配准确，情感标注恰当。"""
                },
                dependencies=["create_script"]
            ),
            
            # 步骤3：TTS语音合成
            StepTemplate(
                step_id="generate_tts",
                name="生成多人TTS语音",
                description="使用克隆声音为每个角色生成对应的语音文件。",
                tool_to_use="audio_expert",
                inputs={
                    "task_description": """基于角色台词分配，生成多人TTS语音文件。

声音资产：
- 赵本山（患者）：voice_id = zhaobenshan_voice_clone_test_001
- 宋丹丹（患者家属）：voice_id = cloned_voice_from_host_audio_20240730  
- 医生：使用默认专业男声

要求：
1. 为每段台词生成独立的音频文件
2. 保持角色声音特色
3. 根据情感调整语调和语速
4. 确保音频质量清晰

输出：
- 按顺序编号的音频文件
- 每个文件包含角色标识
- 提供音频文件的URL和时长信息"""
                },
                dependencies=["assign_roles"]
            ),
            
            # 步骤4：智能视频片段匹配
            StepTemplate(
                step_id="match_video_clips", 
                name="智能匹配视频片段",
                description="根据台词内容和情感，智能匹配最合适的现有视频片段。",
                tool_to_use="visual_expert",
                inputs={
                    "prompt": f"""基于生成的台词和TTS音频，智能匹配最合适的视频片段。

可用视频资产：
{video_assets}

匹配规则：
1. 角色匹配：
   - 医生台词 → 选择doctor_clips
   - 患者台词 → 选择patient_clips  
   - 患者家属台词 → 选择wife_clips

2. 情感匹配：
   - 根据台词的情感色彩选择对应情感的视频片段
   - 考虑内容类型的匹配度

3. 时长匹配：
   - 尽量选择时长接近的视频片段
   - 可以通过剪辑调整时长

输出格式：
```json
{{
  "video_sequence": [
    {{
      "order": 1,
      "audio_file": "tts_audio_1.wav",
      "video_clip": {{
        "id": "doctor_01",
        "url": "视频URL",
        "original_duration": "17.2秒",
        "trim_start": "0秒",
        "trim_end": "8秒",
        "match_reason": "情感和内容匹配度高"
      }}
    }}
  ]
}}
```

请确保匹配合理，视频和音频协调。"""
                },
                dependencies=["generate_tts"]
            ),
            
            # 步骤5：视频合成
            StepTemplate(
                step_id="compose_final_video",
                name="合成最终小品视频",
                description="将匹配的视频片段和TTS音频合成为完整的小品视频作品。",
                tool_to_use="video_expert", 
                inputs={
                    "task_description": """将视频片段和TTS音频合成为完整的小品视频。

合成要求：
1. 视频序列：
   - 按照剧本顺序排列视频片段
   - 确保视频片段时长与音频匹配
   - 添加适当的转场效果

2. 音频处理：
   - 将TTS音频与视频同步
   - 调整音量平衡
   - 添加背景音乐（轻松的东北风格）

3. 视觉效果：
   - 添加字幕显示台词
   - 保持视频清晰度
   - 统一色调和风格

4. 最终输出：
   - 生成完整的小品视频
   - 时长控制在2-5分钟
   - 格式：MP4，1080p

请确保视频流畅，音画同步，效果自然。"""
                },
                dependencies=["match_video_clips"]
            )
        ],
        required_params=["topic"],
        optional_params=["style", "duration_preference", "background_music"]
    )

# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
migration_utils.py

迁移工具，帮助从Legacy Plan和Enhanced Plan转换到Unified Plan。
"""

from typing import Any, Dict, Union
import uuid
from datetime import datetime

from .models import Plan as LegacyPlan, Step as LegacyStep
from .unified_models import UnifiedPlan, UnifiedStep


def migrate_legacy_plan_to_unified(legacy_plan: LegacyPlan) -> UnifiedPlan:
    """
    将Legacy Plan迁移到Unified Plan。
    
    Args:
        legacy_plan: Legacy Plan对象
        
    Returns:
        UnifiedPlan对象
    """
    # 转换步骤
    unified_steps = []
    for legacy_step in legacy_plan.steps:
        unified_step = migrate_legacy_step_to_unified(legacy_step)
        unified_steps.append(unified_step)
    
    # 创建统一计划
    unified_plan = UnifiedPlan(
        original_task=legacy_plan.original_task,
        steps=unified_steps,
        is_from_template=False
    )
    
    return unified_plan


def migrate_legacy_step_to_unified(legacy_step: LegacyStep) -> UnifiedStep:
    """
    将Legacy Step迁移到Unified Step。
    
    Args:
        legacy_step: Legacy Step对象
        
    Returns:
        UnifiedStep对象
    """
    # 生成语义化的step_id
    step_id = f"step_{legacy_step.step_id}"
    
    # 转换依赖关系（从int转为str）
    dependencies = [f"step_{dep_id}" for dep_id in legacy_step.dependencies]
    
    unified_step = UnifiedStep(
        step_id=step_id,
        name=f"步骤 {legacy_step.step_id}",  # 生成默认名称
        description=legacy_step.description,
        tool_to_use=legacy_step.tool_to_use,
        inputs=legacy_step.inputs,
        status=legacy_step.status,
        result=legacy_step.result,
        dependencies=dependencies
    )
    
    return unified_step


# Enhanced模型迁移函数已移除，因为Enhanced模型已被废弃


def auto_migrate_plan(plan: Any) -> UnifiedPlan:
    """
    自动检测计划类型并迁移到Unified Plan。
    
    Args:
        plan: 任意类型的计划对象
        
    Returns:
        UnifiedPlan对象
        
    Raises:
        ValueError: 如果无法识别计划类型
    """
    # 如果已经是UnifiedPlan，直接返回
    if isinstance(plan, UnifiedPlan):
        return plan
    
    # 检测Legacy Plan
    if isinstance(plan, LegacyPlan):
        return migrate_legacy_plan_to_unified(plan)
    
    # Enhanced Plan检测已移除（Enhanced模型已废弃）
    
    # 尝试从字典创建
    if isinstance(plan, dict):
        return migrate_dict_to_unified(plan)
    
    # 无法识别的类型
    raise ValueError(f"无法识别的计划类型: {type(plan)}")


def migrate_dict_to_unified(plan_dict: Dict[str, Any]) -> UnifiedPlan:
    """
    从字典创建Unified Plan。
    
    Args:
        plan_dict: 计划字典
        
    Returns:
        UnifiedPlan对象
    """
    # 提取基本信息
    original_task = plan_dict.get("original_task", "未知任务")
    steps_data = plan_dict.get("steps", [])
    
    # 转换步骤
    unified_steps = []
    for i, step_data in enumerate(steps_data):
        if isinstance(step_data, dict):
            unified_step = migrate_step_dict_to_unified(step_data, i)
            unified_steps.append(unified_step)
    
    # 创建计划
    unified_plan = UnifiedPlan(
        plan_id=plan_dict.get("plan_id", str(uuid.uuid4())),
        original_task=original_task,
        steps=unified_steps,
        template_id=plan_dict.get("template_id") or plan_dict.get("source_template"),
        template_params=plan_dict.get("template_params"),
        is_from_template=plan_dict.get("is_from_template", False)
    )
    
    return unified_plan


def migrate_step_dict_to_unified(step_dict: Dict[str, Any], index: int) -> UnifiedStep:
    """
    从字典创建Unified Step。
    
    Args:
        step_dict: 步骤字典
        index: 步骤索引（用于生成默认ID）
        
    Returns:
        UnifiedStep对象
    """
    # 生成step_id
    step_id = step_dict.get("step_id")
    if step_id is None:
        # 如果没有step_id，生成一个
        step_id = f"step_{index + 1}"
    elif isinstance(step_id, int):
        # 如果是数字ID，转换为字符串
        step_id = f"step_{step_id}"
    else:
        # 确保是字符串
        step_id = str(step_id)
    
    # 处理依赖关系
    dependencies = step_dict.get("dependencies", [])
    if dependencies and isinstance(dependencies[0], int):
        # 转换数字依赖为字符串
        dependencies = [f"step_{dep_id}" for dep_id in dependencies]
    
    unified_step = UnifiedStep(
        step_id=step_id,
        name=step_dict.get("name", f"步骤 {index + 1}"),
        description=step_dict.get("description", ""),
        tool_to_use=step_dict.get("tool_to_use", ""),
        inputs=step_dict.get("inputs", {}),
        status=step_dict.get("status", "pending"),
        result=step_dict.get("result"),
        dependencies=dependencies,
        max_retries=step_dict.get("max_retries", 3),
        retry_count=step_dict.get("retry_count", 0)
    )
    
    return unified_step


def ensure_unified_plan(plan: Any) -> UnifiedPlan:
    """
    确保返回UnifiedPlan对象。
    
    Args:
        plan: 任意类型的计划对象
        
    Returns:
        UnifiedPlan对象
    """
    try:
        return auto_migrate_plan(plan)
    except Exception as e:
        # 如果迁移失败，创建一个空的计划
        print(f"⚠️ 计划迁移失败: {e}")
        return UnifiedPlan(
            original_task="迁移失败的计划",
            steps=[]
        )

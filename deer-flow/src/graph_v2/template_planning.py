#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
template_planning.py

模板规划会话管理，支持启动时指定模板和渐进式参数收集。
"""

from typing import Dict, Any, Optional, List
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm
import json

from .template_models import PlanTemplate, ParameterSchema, ParameterType
from .template_renderer import template_renderer
from .unified_models import UnifiedPlan, UnifiedStep
from src.tools.template_tools import _template_registry

console = Console()


class TemplatePlanningSession:
    """
    模板规划会话
    
    支持：
    1. 启动时指定模板
    2. 渐进式参数收集
    3. 交互式计划调整
    4. 确认后执行
    """
    
    def __init__(self, template_id: str, initial_params: Optional[Dict[str, Any]] = None):
        self.template_id = template_id
        self.template = _template_registry.get(template_id)
        if not self.template:
            raise ValueError(f"模板 '{template_id}' 不存在")
        
        self.params = initial_params or {}
        self.plan: Optional[UnifiedPlan] = None
        self.status = "collecting_params"  # collecting_params, plan_ready, confirmed
        
    def display_template_info(self):
        """显示模板信息"""
        console.print(Panel(
            f"[bold blue]{self.template.name}[/bold blue]\n\n"
            f"[dim]{self.template.description}[/dim]\n\n"
            f"[yellow]分类:[/yellow] {self.template.category}\n"
            f"[yellow]预计时长:[/yellow] {self.template.estimated_duration // 60}分钟\n"
            f"[yellow]难度:[/yellow] {getattr(self.template, 'difficulty_level', 'medium')}",
            title="📋 模板信息",
            border_style="blue"
        ))
    
    def get_missing_params(self) -> List[str]:
        """获取缺失的必需参数"""
        required_params = []
        for param_name, param_schema in self.template.parameters.items():
            if param_schema.required and param_name not in self.params:
                required_params.append(param_name)
        return required_params
    
    def display_param_requirements(self):
        """显示参数需求"""
        missing_params = self.get_missing_params()
        
        if not missing_params:
            console.print("[green]✅ 所有必需参数已收集完成[/green]")
            return
        
        console.print("\n[yellow]🔧 需要收集参数:[/yellow]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("参数名", style="cyan")
        table.add_column("类型", style="green")
        table.add_column("必需", style="red")
        table.add_column("描述", style="dim")
        table.add_column("选项/示例", style="blue")
        
        for param_name, param_schema in self.template.parameters.items():
            if param_name in self.params:
                continue  # 跳过已有参数
                
            param_type = param_schema.type.value if hasattr(param_schema.type, 'value') else str(param_schema.type)
            required = "✅" if param_schema.required else "⭕"
            
            # 构建选项/示例信息
            options_info = ""
            if hasattr(param_schema, 'options') and param_schema.options:
                options_info = f"选项: {', '.join(param_schema.options)}"
            elif hasattr(param_schema, 'default') and param_schema.default:
                options_info = f"默认: {param_schema.default}"
            
            table.add_row(
                param_name,
                param_type,
                required,
                param_schema.description,
                options_info
            )
        
        console.print(table)
    
    def collect_params_interactively(self):
        """交互式收集参数"""
        missing_params = self.get_missing_params()
        
        if not missing_params:
            return True
        
        console.print("\n[yellow]💬 请提供缺失的参数，或者在对话中自然描述你的需求[/yellow]")
        console.print("[dim]例如: '主题是看病，风格要经典的，时长中等就行'[/dim]")
        
        return False
    
    def parse_natural_params(self, user_input: str) -> Dict[str, Any]:
        """从自然语言中解析参数"""
        extracted_params = {}
        
        # 简单的关键词匹配（可以后续用NLP增强）
        for param_name, param_schema in self.template.parameters.items():
            if param_name in self.params:
                continue
                
            # 根据参数类型和描述进行匹配
            if param_name == "topic" or "主题" in param_schema.description:
                # 匹配主题相关的词
                if "主题是" in user_input:
                    topic = user_input.split("主题是")[1].split("，")[0].split(" ")[0]
                    extracted_params["topic"] = topic.strip()
                elif "关于" in user_input:
                    topic = user_input.split("关于")[1].split("的")[0]
                    extracted_params["topic"] = topic.strip()
            
            elif param_name == "style" or "风格" in param_schema.description:
                if "风格" in user_input:
                    if "经典" in user_input or "classic" in user_input.lower():
                        extracted_params["style"] = "classic"
                    elif "现代" in user_input or "modern" in user_input.lower():
                        extracted_params["style"] = "modern"
                    elif "荒诞" in user_input or "absurd" in user_input.lower():
                        extracted_params["style"] = "absurd"
            
            elif param_name == "duration" or "时长" in param_schema.description:
                if "时长" in user_input:
                    if "短" in user_input or "short" in user_input.lower():
                        extracted_params["duration"] = "short"
                    elif "长" in user_input or "long" in user_input.lower():
                        extracted_params["duration"] = "long"
                    elif "中" in user_input or "medium" in user_input.lower():
                        extracted_params["duration"] = "medium"
        
        return extracted_params
    
    def add_params(self, new_params: Dict[str, Any]) -> bool:
        """添加参数并检查是否完整"""
        self.params.update(new_params)
        
        missing_params = self.get_missing_params()
        if not missing_params:
            console.print("[green]✅ 参数收集完成，正在生成计划...[/green]")
            self.generate_plan()
            return True
        else:
            console.print(f"[yellow]还需要参数: {', '.join(missing_params)}[/yellow]")
            return False
    
    def generate_plan(self) -> UnifiedPlan:
        """生成执行计划"""
        try:
            self.plan = template_renderer.render_plan(self.template, self.params)
            self.status = "plan_ready"
            console.print("[green]✅ 计划生成成功[/green]")
            return self.plan
        except Exception as e:
            console.print(f"[red]❌ 计划生成失败: {e}[/red]")
            raise
    
    def display_plan(self):
        """显示执行计划"""
        if not self.plan:
            console.print("[red]❌ 还没有生成计划[/red]")
            return
        
        console.print(Panel(
            f"[bold green]📋 执行计划[/bold green]\n\n"
            f"[yellow]任务:[/yellow] {self.plan.original_task}\n"
            f"[yellow]总步骤:[/yellow] {len(self.plan.steps)}个\n"
            f"[yellow]预计时长:[/yellow] {self.template.estimated_duration // 60}分钟",
            title="计划概览",
            border_style="green"
        ))
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("步骤", style="cyan", width=8)
        table.add_column("名称", style="green")
        table.add_column("工具", style="blue")
        table.add_column("预计时长", style="yellow")
        table.add_column("描述", style="dim")
        
        for i, step in enumerate(self.plan.steps, 1):
            estimated_time = f"{getattr(step, 'estimated_duration', 300) // 60}分钟"
            description = step.description[:50] + "..." if len(step.description) > 50 else step.description
            
            table.add_row(
                str(i),
                step.name,
                step.tool_to_use,
                estimated_time,
                description
            )
        
        console.print(table)
        
        console.print("\n[yellow]💡 你可以说:[/yellow]")
        console.print("   [dim]• '修改第2步，让语音更有感情'[/dim]")
        console.print("   [dim]• '在第3步前添加音效处理'[/dim]")
        console.print("   [dim]• '删除第4步'[/dim]")
        console.print("   [dim]• '计划看起来不错，开始执行吧'[/dim]")
    
    def modify_step(self, step_number: int, modification: str) -> bool:
        """修改指定步骤"""
        if not self.plan or step_number < 1 or step_number > len(self.plan.steps):
            console.print(f"[red]❌ 无效的步骤编号: {step_number}[/red]")
            return False
        
        step = self.plan.steps[step_number - 1]
        console.print(f"[yellow]🔧 正在调整第{step_number}步: {step.name}[/yellow]")
        
        # 简单的修改逻辑（可以后续用LLM增强）
        if "更" in modification and "搞笑" in modification:
            step.description += "，特别强调幽默效果"
        elif "更" in modification and "感情" in modification:
            step.description += "，增强情感表达"
        elif "东北方言" in modification:
            step.description += "，强调东北方言特色"
        elif "音效" in modification:
            step.description += "，添加音效处理"
        else:
            step.description += f"，{modification}"
        
        console.print(f"[green]✅ 已更新: {step.name}[/green]")
        return True
    
    def is_ready_to_execute(self) -> bool:
        """检查是否准备好执行"""
        return self.status == "plan_ready" and self.plan is not None
    
    def confirm_execution(self) -> bool:
        """确认开始执行"""
        if not self.is_ready_to_execute():
            return False
        
        self.status = "confirmed"
        console.print("[green]🚀 计划已确认，准备开始执行...[/green]")
        return True


# 全局规划状态
class GlobalPlanningState:
    def __init__(self):
        self.template_session: Optional[TemplatePlanningSession] = None
        self.planning_mode: bool = False
    
    def enter_template_mode(self, template_id: str, initial_params: Optional[Dict[str, Any]] = None):
        """进入模板规划模式"""
        try:
            self.template_session = TemplatePlanningSession(template_id, initial_params)
            self.planning_mode = True
            return True
        except ValueError as e:
            console.print(f"[red]❌ {e}[/red]")
            return False
    
    def exit_template_mode(self):
        """退出模板规划模式"""
        self.template_session = None
        self.planning_mode = False
    
    def get_current_plan(self) -> Optional[UnifiedPlan]:
        """获取当前计划"""
        if self.template_session and self.template_session.plan:
            return self.template_session.plan
        return None


# 全局实例
global_planning_state = GlobalPlanningState()

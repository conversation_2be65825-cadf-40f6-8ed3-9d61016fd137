# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
execution_engine.py

执行引擎：负责驱动Master Agent持续执行多步计划直到完成。
执行引擎不直接调用工具，而是循环调用Master Agent，由Master Agent负责具体的工具调用。
"""

from typing import Dict, Any
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig

from .types import State
from .nodes import master_agent_node
from .unified_models import UnifiedPlan
from .migration_utils import ensure_unified_plan
from .state_handler import get_current_step_info, get_plan_progress, build_step_instruction


def execution_engine_node(state: State, config: RunnableConfig = None) -> State:
    """
    执行引擎节点：驱动Master Agent持续执行计划直到完成
    
    职责：
    1. 循环调用Master Agent
    2. 监控计划执行进度
    3. 强制继续执行未完成的计划
    4. 防止Master Agent提前停止
    
    Args:
        state: 当前状态，包含计划和消息历史
        
    Returns:
        更新后的状态，计划应该已完成或达到最大迭代次数
    """
    plan = state.get("plan")

    # 安全检查
    if not plan:
        return state

    try:
        # 确保我们有UnifiedPlan
        unified_plan = ensure_unified_plan(plan)
        # 更新state中的plan为转换后的UnifiedPlan
        state["plan"] = unified_plan
        plan = unified_plan
    except Exception as e:
        print(f"   ❌ 计划转换失败: {e}")
        return state

    if plan.is_complete():
        return state
    
    # 执行配置
    max_iterations = 20  # 防止无限循环
    iteration = 0
    
    print(f"🔄 执行引擎启动：开始执行计划 (共{len(plan.steps)}个步骤)")
    
    # 主执行循环
    while not plan.is_complete() and iteration < max_iterations:
        iteration += 1
        print(f"   执行循环 {iteration}: 准备执行下一步...")

        # 获取下一个可执行步骤
        executable_steps = plan.get_next_executable_steps()
        if not executable_steps:
            print(f"   ⚠️  没有可执行步骤，退出循环")
            break

        next_step = executable_steps[0]
        print(f"   下一步: {next_step.step_id} - {next_step.name}")

        # 设置当前步骤，让Master Agent知道要执行什么
        state["current_step"] = next_step.step_id

        # 🔧 改进：使用新的指令构建机制
        step_info = {
            "step_id": next_step.step_id,
            "name": next_step.name,
            "description": next_step.description,
            "tool_to_use": next_step.tool_to_use
        }
        step_instruction = build_step_instruction(step_info)
        state["messages"].append(HumanMessage(content=step_instruction))

        # 调用Master Agent执行步骤
        try:
            state = master_agent_node(state, config)  # 🔧 修复：传递config参数
        except Exception as e:
            print(f"   ❌ Master Agent执行出错: {e}")
            # 标记步骤失败
            next_step.status = "failed"
            if hasattr(next_step, 'add_error'):
                next_step.add_error(str(e), "execution_error")
            # 添加错误消息并继续（或退出）
            error_msg = f"步骤 {next_step.step_id} 执行失败: {str(e)}"
            state["messages"].append(AIMessage(content=error_msg))
            break
        
        # 获取更新后的计划，优先使用全局计划
        plan = state.get("plan")
        if not plan:
            # 尝试从全局状态获取计划
            from src.tools.planning import get_current_plan
            global_plan = get_current_plan()
            if global_plan:
                state["plan"] = global_plan
                plan = global_plan
                print("   🔄 从全局状态恢复计划")
            else:
                print("   ⚠️  计划丢失，退出执行循环")
                break

        # 🔧 改进：使用新的状态检测机制
        current_step_id = state.get("current_step")
        if current_step_id:
            # 获取更新后的计划状态
            plan = state.get("plan")
            if plan:
                step = plan.get_step(current_step_id)
                if step:
                    print(f"   📊 步骤 {current_step_id} 状态: {step.status}")

                    if step.status == "completed":
                        print(f"   ✅ 步骤 {current_step_id} 执行成功")
                        # 清除current_step，准备下一轮
                        state["current_step"] = None
                    elif step.status == "failed":
                        print(f"   ❌ 步骤 {current_step_id} 执行失败")
                        # 检查是否可以重试
                        if hasattr(step, 'should_retry_on_failure') and step.should_retry_on_failure():
                            print(f"   🔄 步骤 {current_step_id} 将重试")
                            if hasattr(step, 'reset_for_retry'):
                                step.reset_for_retry()
                            else:
                                step.status = "pending"  # 简单重置
                            state["current_step"] = None  # 重新执行
                        else:
                            print(f"   💀 步骤 {current_step_id} 无法重试，停止执行")
                            break
                    else:
                        # 步骤仍在pending或in_progress状态
                        print(f"   ⏳ 步骤 {current_step_id} 仍在执行中...")

                        # 🔧 改进：检查是否调用了新的汇报工具
                        last_message = state["messages"][-1] if state["messages"] else None
                        has_reported = False

                        if isinstance(last_message, AIMessage):
                            # 检查是否调用了report_step_completion工具
                            if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                                for tool_call in last_message.tool_calls:
                                    if tool_call.get("name") == "report_step_completion":
                                        has_reported = True
                                        break

                        if not has_reported:
                            # Master Agent没有汇报状态，发送提醒
                            print(f"   ⚠️  Master Agent未汇报步骤状态")
                            reminder_msg = (
                                f"⚠️ 请务必调用 report_step_completion 工具汇报步骤 {current_step_id} 的执行状态！\n"
                                f"参数：step_id=\"{current_step_id}\", status=\"completed\"或\"failed\", result_summary=\"执行结果描述\""
                            )
                            state["messages"].append(HumanMessage(content=reminder_msg))
                            continue
                else:
                    print(f"   ❌ 找不到步骤 {current_step_id}")
                    state["current_step"] = None

        # 检查整体进度
        completed_steps = len([s for s in plan.steps if s.status == "completed"])
        total_steps = len(plan.steps)
        print(f"   📊 总进度: {completed_steps}/{total_steps} 步骤完成")
        
        # 检查是否有新的用户消息（用户中断）
        if _has_new_user_message(state, iteration):
            print("   👤 检测到用户新消息，退出执行引擎")
            break
    
    # 执行完成处理
    if iteration >= max_iterations:
        print(f"   ⚠️  达到最大迭代次数 ({max_iterations})，强制退出")
        timeout_msg = "执行时间过长，已自动停止。如需继续，请重新发起请求。"
        state["messages"].append(AIMessage(content=timeout_msg))
    elif plan and plan.is_complete():
        print("   🎉 计划执行完成！")
    
    return state


def should_use_execution_engine(state: State) -> bool:
    """
    判断是否需要使用执行引擎

    规则：
    1. 没有计划 → 不需要
    2. 空计划 → 不需要
    3. 单步计划 → 不需要（Master Agent可以处理）
    4. 模板计划 → 需要（预定义的多步流程）
    5. 多步自定义计划 → 需要

    Args:
        state: 当前状态

    Returns:
        是否需要使用执行引擎
    """
    plan = state.get("plan")

    # 🚀 检查全局状态中的计划（临时解决方案）
    if not plan:
        from src.tools.state_management import get_current_state_plan
        global_plan = get_current_state_plan()
        if global_plan:
            print("🔍 路由判断: 发现全局状态中的计划，更新到state")
            state["plan"] = global_plan
            plan = global_plan

    # 没有计划
    if not plan:
        print("🔍 路由判断: 没有计划 → Master Agent")
        return False

    try:
        # 确保我们有UnifiedPlan来进行判断
        unified_plan = ensure_unified_plan(plan)

        # 空计划
        if not unified_plan.steps:
            print("🔍 路由判断: 空计划 → Master Agent")
            return False

        # 计划已完成
        if unified_plan.is_complete():
            print("🔍 路由判断: 计划已完成 → Master Agent")
            return False

        step_count = len(unified_plan.steps)
        print(f"🔍 路由判断: 计划有{step_count}个步骤")

        # 模板计划，无论步骤数都需要执行引擎（模板有特殊的执行逻辑）
        if unified_plan.is_from_template:
            print("🔍 路由判断: 模板计划 → 执行引擎")
            return True

        # 单步自定义计划，Master Agent可以处理
        if step_count == 1:
            print("🔍 路由判断: 单步自定义计划 → Master Agent")
            return False

        # 多步自定义计划，需要执行引擎
        if step_count >= 2:
            print(f"🔍 路由判断: {step_count}步自定义计划 → 执行引擎")
            return True

        print("🔍 路由判断: 默认 → Master Agent")
        return False

    except Exception as e:
        print(f"🔍 路由判断: 转换失败 ({e}) → Master Agent")
        # 如果转换失败，保守地不使用执行引擎
        return False


def _has_new_user_message(state: State, current_iteration: int) -> bool:
    """
    检查是否有新的用户消息（用户中断）
    
    简单实现：检查最近的消息是否是用户消息
    更复杂的实现可以跟踪消息数量变化
    """
    if not state.get("messages"):
        return False
    
    # 检查最后几条消息中是否有用户消息
    recent_messages = state["messages"][-3:]  # 检查最近3条消息
    
    for msg in reversed(recent_messages):
        if hasattr(msg, 'type') and msg.type == "human":
            # 简单判断：如果有用户消息且不是强制继续消息
            if not msg.content.startswith("计划尚未完成"):
                return True
    
    return False


def generate_execution_summary(plan, execution_log: list) -> str:
    """
    生成执行完成的友好总结
    
    Args:
        plan: 执行完成的计划
        execution_log: 执行日志
        
    Returns:
        友好的总结文本
    """
    if not plan:
        return "执行完成。"
    
    completed_steps = len([s for s in plan.steps if s.status == "completed"])
    total_steps = len(plan.steps)
    
    if completed_steps == total_steps:
        summary = f"🎉 所有任务已完成！成功执行了 {total_steps} 个步骤：\n\n"
        
        for i, step in enumerate(plan.steps, 1):
            if step.status == "completed":
                summary += f"{i}. ✅ {step.name}\n"
            else:
                summary += f"{i}. ❌ {step.name} (失败)\n"
        
        if hasattr(plan, 'is_from_template') and plan.is_from_template:
            summary += f"\n📋 基于模板: {getattr(plan, 'source_template', '未知')}"
        
        return summary
    else:
        return f"执行完成。{completed_steps}/{total_steps} 个步骤成功完成。"

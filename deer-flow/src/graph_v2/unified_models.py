# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
unified_models.py

统一的Plan模型设计，替代Legacy Plan和Enhanced Plan。

设计原则：
1. 简单优先：保留核心功能，移除过度设计
2. 向前兼容：能处理现有的所有用例
3. 易于使用：开发者不需要考虑模型转换
4. 性能友好：减少不必要的复杂性
"""

from typing import List, Dict, Any, Optional, Literal
from pydantic import BaseModel, Field
from datetime import datetime
import uuid

# 重用现有的状态定义
StepStatus = Literal["pending", "in_progress", "completed", "failed", "skipped"]


class UnifiedStep(BaseModel):
    """
    统一的步骤模型，融合Legacy和Enhanced的优点。
    
    核心设计决策：
    - 使用字符串step_id（语义化，便于调试）
    - 保留核心字段，移除过度复杂的功能
    - 支持基本的重试和依赖管理
    """
    # 核心标识
    step_id: str = Field(..., description="步骤ID，语义化字符串（如'collect_materials'）")
    name: str = Field(..., description="步骤名称，用于显示")
    description: str = Field(..., description="步骤描述")
    
    # 执行配置
    tool_to_use: str = Field(..., description="要使用的工具名称")
    inputs: Dict[str, Any] = Field(default_factory=dict, description="工具输入参数")
    
    # 状态管理
    status: StepStatus = Field(default="pending", description="执行状态")
    result: Optional[Dict[str, Any]] = Field(default=None, description="执行结果")
    
    # 依赖关系（简化）
    dependencies: List[str] = Field(default_factory=list, description="依赖的步骤ID列表")
    
    # 重试配置（简化）
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_count: int = Field(default=0, description="当前重试次数")
    
    # 时间戳
    created_at: Optional[datetime] = Field(default_factory=datetime.now, description="创建时间")
    started_at: Optional[datetime] = Field(default=None, description="开始执行时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    
    def can_execute(self, completed_step_ids: set) -> bool:
        """检查步骤是否可以执行"""
        if self.status != "pending":
            return False
        
        # 检查依赖是否都已完成
        for dep_id in self.dependencies:
            if dep_id not in completed_step_ids:
                return False
        
        return True
    
    def should_retry(self) -> bool:
        """检查是否应该重试"""
        return (self.status == "failed" and 
                self.retry_count < self.max_retries)
    
    def reset_for_retry(self):
        """重置步骤以便重试"""
        self.status = "pending"
        self.retry_count += 1
        self.started_at = None
        self.completed_at = None
    
    def mark_started(self):
        """标记步骤开始执行"""
        self.status = "in_progress"
        self.started_at = datetime.now()
    
    def mark_completed(self, result: Dict[str, Any]):
        """标记步骤完成"""
        self.status = "completed"
        self.result = result
        self.completed_at = datetime.now()
    
    def mark_failed(self, error_info: Dict[str, Any]):
        """标记步骤失败"""
        self.status = "failed"
        self.result = {"error": error_info}
        self.completed_at = datetime.now()
    
    def get_execution_duration(self) -> Optional[float]:
        """获取执行时长（秒）"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    def get_user_friendly_status(self) -> str:
        """获取用户友好的状态描述"""
        if self.status == "pending":
            if self.retry_count > 0:
                return f"等待重试 (第{self.retry_count}次)"
            return "等待执行"
        elif self.status == "in_progress":
            return "正在执行..."
        elif self.status == "completed":
            duration = self.get_execution_duration()
            if duration:
                return f"已完成 (耗时{duration:.1f}秒)"
            return "已完成"
        elif self.status == "failed":
            if self.should_retry():
                return f"执行失败，准备重试 (第{self.retry_count}次)"
            return f"执行失败 (已重试{self.retry_count}次)"
        elif self.status == "skipped":
            return "已跳过"
        else:
            return self.status


class UnifiedPlan(BaseModel):
    """
    统一的计划模型，替代Legacy Plan和Enhanced Plan。
    
    核心设计决策：
    - 保留必要功能，移除过度复杂的特性
    - 简化执行上下文管理
    - 统一接口，便于使用
    """
    # 核心标识
    plan_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="计划唯一ID")
    original_task: str = Field(..., description="原始任务描述")
    
    # 计划结构
    steps: List[UnifiedStep] = Field(..., description="步骤列表")
    
    # 模板信息（可选）
    template_id: Optional[str] = Field(default=None, description="来源模板ID")
    template_params: Optional[Dict[str, Any]] = Field(default=None, description="模板参数")
    is_from_template: bool = Field(default=False, description="是否来自模板")
    
    # 执行状态
    current_step_index: int = Field(default=0, description="当前步骤索引")
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    # 执行结果存储
    step_results: Dict[str, Any] = Field(default_factory=dict, description="步骤结果存储")
    
    def get_step(self, step_id: str) -> Optional[UnifiedStep]:
        """根据ID获取步骤"""
        for step in self.steps:
            if step.step_id == step_id:
                return step
        return None
    
    def get_next_executable_steps(self) -> List[UnifiedStep]:
        """获取下一批可执行的步骤"""
        completed_step_ids = {s.step_id for s in self.steps if s.status == "completed"}
        executable_steps = []
        
        for step in self.steps:
            if step.can_execute(completed_step_ids):
                executable_steps.append(step)
        
        return executable_steps
    
    def get_current_step(self) -> Optional[UnifiedStep]:
        """获取当前应该执行的步骤"""
        executable_steps = self.get_next_executable_steps()
        return executable_steps[0] if executable_steps else None
    
    def is_complete(self) -> bool:
        """检查计划是否完成"""
        return all(step.status in ["completed", "skipped"] for step in self.steps)
    
    def has_failed(self) -> bool:
        """检查是否有失败的步骤"""
        return any(step.status == "failed" and not step.should_retry() for step in self.steps)
    
    def get_progress_info(self) -> Dict[str, Any]:
        """获取进度信息"""
        total_steps = len(self.steps)
        completed_steps = len([s for s in self.steps if s.status == "completed"])
        failed_steps = len([s for s in self.steps if s.status == "failed"])
        pending_steps = len([s for s in self.steps if s.status == "pending"])
        
        return {
            "total_steps": total_steps,
            "completed_steps": completed_steps,
            "failed_steps": failed_steps,
            "pending_steps": pending_steps,
            "progress_percentage": (completed_steps / total_steps * 100) if total_steps > 0 else 0,
            "is_complete": self.is_complete(),
            "has_failed": self.has_failed()
        }
    
    def get_execution_summary(self) -> str:
        """获取执行摘要"""
        progress = self.get_progress_info()
        
        if progress["is_complete"]:
            return f"✅ 计划执行完成！共完成 {progress['completed_steps']} 个步骤"
        elif progress["has_failed"]:
            return f"❌ 计划执行失败，{progress['failed_steps']} 个步骤失败"
        else:
            return f"🔄 执行进行中：{progress['completed_steps']}/{progress['total_steps']} 步骤完成"
    
    def update_step_result(self, step_id: str, result: Any):
        """更新步骤结果"""
        self.step_results[step_id] = result
        step = self.get_step(step_id)
        if step:
            step.mark_completed({"result": result, "success": True})
    
    def mark_step_failed(self, step_id: str, error_info: Dict[str, Any]):
        """标记步骤失败"""
        step = self.get_step(step_id)
        if step:
            step.mark_failed(error_info)


# 便于迁移的类型别名
Plan = UnifiedPlan
Step = UnifiedStep

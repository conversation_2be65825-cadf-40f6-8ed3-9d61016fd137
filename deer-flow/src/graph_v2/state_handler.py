# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
state_handler.py

状态处理模块，负责处理Master Agent的状态更新汇报。

这个模块解决了原有状态管理工具无法被正确调用的问题，
通过解析Master Agent的工具调用结果来更新计划状态。
"""

import json
import re
from typing import Dict, Any, Optional
from datetime import datetime
from langchain_core.messages import AIMessage

from .types import State
from .unified_models import UnifiedPlan
from .migration_utils import ensure_unified_plan


def process_master_agent_result(state: State, result: Dict[str, Any]) -> State:
    """
    处理Master Agent的执行结果，检查并应用状态更新。

    Args:
        state: 当前状态
        result: Master Agent的执行结果

    Returns:
        更新后的状态
    """
    # 🔧 新增：检查工具调用结果中的plan更新
    plan_updates = extract_plan_updates(result)
    if plan_updates:
        # 应用计划更新
        for plan_update in plan_updates:
            apply_plan_update(state, plan_update)

    # 检查是否有状态更新指令
    status_updates = extract_status_updates(result)

    if status_updates:
        # 应用状态更新
        for update in status_updates:
            apply_status_update(state, update)

    return state


def extract_plan_updates(result: Dict[str, Any]) -> list:
    """
    从Master Agent的执行结果中提取计划更新。

    Args:
        result: Master Agent的执行结果

    Returns:
        计划更新列表
    """
    plan_updates = []

    # 检查消息中的工具调用结果
    messages = result.get("messages", [])
    for message in messages:
        # 检查工具消息（工具调用的返回结果）
        if hasattr(message, 'type') and message.type == "tool":
            try:
                # 尝试解析工具返回的内容
                if hasattr(message, 'content') and message.content:
                    import json
                    import ast

                    content = message.content
                    print(f"🔍 检查工具消息内容: {type(content)} - {str(content)[:100]}...")

                    # 方法1: 尝试JSON解析
                    if isinstance(content, str):
                        if content.startswith('{'):
                            try:
                                tool_result = json.loads(content)
                                if "plan" in tool_result:
                                    print("✅ 通过JSON解析找到计划")
                                    plan_updates.append({
                                        "action": "update_plan",
                                        "plan": tool_result["plan"]
                                    })
                                    continue
                            except json.JSONDecodeError:
                                pass

                        # 方法2: 尝试ast.literal_eval解析
                        if content.startswith("{'plan'"):
                            try:
                                tool_result = ast.literal_eval(content)
                                if "plan" in tool_result:
                                    print("✅ 通过AST解析找到计划")
                                    plan_updates.append({
                                        "action": "update_plan",
                                        "plan": tool_result["plan"]
                                    })
                                    continue
                            except (ValueError, SyntaxError):
                                pass

                        # 方法3: 检查是否包含计划对象的字符串表示
                        if "UnifiedPlan" in content and "plan_id" in content:
                            print("⚠️ 发现计划对象字符串，但无法直接解析")
                            # 这种情况需要特殊处理

                    # 方法4: 直接是字典（理论上不会发生，但保留）
                    elif isinstance(content, dict) and "plan" in content:
                        print("✅ 直接字典格式找到计划")
                        plan_updates.append({
                            "action": "update_plan",
                            "plan": content["plan"]
                        })

            except Exception as e:
                print(f"❌ 工具消息解析失败: {e}")
                continue

    # 🔧 备用方案：检查result中是否直接包含plan
    if "plan" in result:
        plan_updates.append({
            "action": "update_plan",
            "plan": result["plan"]
        })

    return plan_updates


def apply_plan_update(state: State, plan_update: Dict[str, Any]) -> bool:
    """
    应用计划更新到状态中。

    Args:
        state: 当前状态
        plan_update: 计划更新信息

    Returns:
        是否成功应用更新
    """
    try:
        if plan_update["action"] == "update_plan":
            new_plan = plan_update["plan"]
            if new_plan:
                state["plan"] = new_plan
                print(f"✅ 计划已更新: {len(new_plan.steps) if hasattr(new_plan, 'steps') else '未知'}个步骤")
                return True
        return False
    except Exception as e:
        print(f"❌ 计划更新失败: {e}")
        return False


def extract_status_updates(result: Dict[str, Any]) -> list:
    """
    从Master Agent的执行结果中提取状态更新指令。
    
    Args:
        result: Master Agent的执行结果
        
    Returns:
        状态更新指令列表
    """
    updates = []
    
    # 检查消息中的工具调用
    messages = result.get("messages", [])
    for message in messages:
        if isinstance(message, AIMessage) and hasattr(message, 'tool_calls'):
            for tool_call in message.tool_calls:
                if tool_call.get("name") == "report_step_completion":
                    # 解析汇报信息
                    args = tool_call.get("args", {})
                    update = {
                        "step_id": args.get("step_id"),
                        "status": args.get("status"),
                        "result_summary": args.get("result_summary"),
                        "timestamp": datetime.now().isoformat()
                    }
                    updates.append(update)
        
        # 也检查消息内容中的状态更新指令（备用方案）
        if isinstance(message, AIMessage):
            content_updates = extract_updates_from_content(message.content)
            updates.extend(content_updates)
    
    return updates


def extract_updates_from_content(content: str) -> list:
    """
    从消息内容中提取状态更新指令（备用解析方案）。
    
    Args:
        content: 消息内容
        
    Returns:
        状态更新指令列表
    """
    updates = []
    
    # 查找JSON格式的状态更新指令
    json_pattern = r'\{[^}]*"action":\s*"update_step_status"[^}]*\}'
    matches = re.findall(json_pattern, content)
    
    for match in matches:
        try:
            update_data = json.loads(match)
            if update_data.get("action") == "update_step_status":
                updates.append({
                    "step_id": update_data.get("step_id"),
                    "status": update_data.get("status"),
                    "result_summary": update_data.get("result_summary"),
                    "timestamp": update_data.get("timestamp", datetime.now().isoformat())
                })
        except json.JSONDecodeError:
            continue
    
    return updates


def apply_status_update(state: State, update: Dict[str, Any]) -> bool:
    """
    应用状态更新到计划中。
    
    Args:
        state: 当前状态
        update: 状态更新信息
        
    Returns:
        是否成功更新
    """
    plan = state.get("plan")
    if not plan:
        return False
    
    try:
        # 确保我们有UnifiedPlan
        unified_plan = ensure_unified_plan(plan)
        state["plan"] = unified_plan

        # 查找并更新步骤
        step = unified_plan.get_step(update["step_id"])
        if not step:
            print(f"⚠️ 找不到步骤: {update['step_id']}")
            return False
        
        # 更新步骤状态
        step.status = update["status"]
        
        # 使用UnifiedPlan的内置方法更新状态
        if update["status"] == "completed":
            result_data = {
                "summary": update.get("result_summary", ""),
                "timestamp": update["timestamp"],
                "success": True
            }
            step.mark_completed(result_data)
            unified_plan.update_step_result(update["step_id"], result_data)
        elif update["status"] == "failed":
            error_info = {
                "summary": update.get("result_summary", ""),
                "timestamp": update["timestamp"],
                "success": False
            }
            step.mark_failed(error_info)
        else:
            # 手动状态更新
            step.status = update["status"]
            if update.get("result_summary"):
                step.result = {
                    "summary": update["result_summary"],
                    "timestamp": update["timestamp"]
                }
        
        print(f"✅ 步骤状态已更新: {update['step_id']} -> {update['status']}")
        return True
        
    except Exception as e:
        print(f"❌ 状态更新失败: {e}")
        return False


def get_current_step_info(state: State) -> Optional[Dict[str, Any]]:
    """
    获取当前应该执行的步骤信息。
    
    Args:
        state: 当前状态
        
    Returns:
        当前步骤信息，如果没有则返回None
    """
    plan = state.get("plan")
    if not plan:
        return None
    
    try:
        unified_plan = ensure_unified_plan(plan)
        executable_steps = unified_plan.get_next_executable_steps()
        
        if executable_steps:
            step = executable_steps[0]
            return {
                "step_id": step.step_id,
                "name": step.name,
                "description": step.description,
                "tool_to_use": step.tool_to_use,
                "status": step.status
            }
    except Exception as e:
        print(f"❌ 获取步骤信息失败: {e}")
    
    return None


def get_plan_progress(state: State) -> Dict[str, Any]:
    """
    获取计划执行进度信息。
    
    Args:
        state: 当前状态
        
    Returns:
        进度信息
    """
    plan = state.get("plan")
    if not plan:
        return {"error": "没有可用的计划"}
    
    try:
        unified_plan = ensure_unified_plan(plan)

        # 使用UnifiedPlan的内置进度跟踪
        progress_info = unified_plan.get_progress_info()
        progress_info["next_step"] = get_current_step_info(state)

        return progress_info
        
    except Exception as e:
        return {"error": f"获取进度信息失败: {e}"}


def build_step_instruction(step_info: Dict[str, Any]) -> str:
    """
    构建给Master Agent的步骤执行指令。

    Args:
        step_info: 步骤信息

    Returns:
        执行指令文本
    """

    tool_to_use = step_info.get('tool_to_use', '')

    # 🚀 特殊处理：direct_generation表示主agent直接生成内容
    if tool_to_use == "direct_generation":
        # 从步骤的inputs中获取详细指令
        inputs = step_info.get('inputs', {})
        instruction = inputs.get('instruction', step_info['description'])

        return f"""
🎯 请直接完成以下创作任务：

**步骤ID**: {step_info['step_id']}
**任务**: {step_info['description']}

**详细要求**:
{instruction}

**重要说明**:
- 请直接生成所需的内容，不要调用其他工具
- 完成后，请调用 `report_step_completion` 工具汇报结果：
  - step_id: "{step_info['step_id']}"
  - status: "completed"
  - result_summary: 生成内容的简要描述

请立即开始创作。
"""

    # 普通工具调用指令
    inputs = step_info.get('inputs', {})

    # 🚀 构建详细的指令，包含模板预制内容
    instruction_parts = [
        f"🎯 请执行以下步骤：",
        f"",
        f"**步骤ID**: {step_info['step_id']}",
        f"**任务描述**: {step_info['description']}",
        f"**使用工具**: {step_info['tool_to_use']}"
    ]

    # 🚀 添加模板预制的详细内容
    if inputs:
        instruction_parts.append(f"")
        instruction_parts.append(f"**详细要求和预制资源**:")

        for key, value in inputs.items():
            if isinstance(value, str):
                instruction_parts.append(f"")
                instruction_parts.append(f"**{key}**:")
                instruction_parts.append(value)
            elif isinstance(value, dict):
                instruction_parts.append(f"")
                instruction_parts.append(f"**{key}**:")
                instruction_parts.append(f"```json")
                import json
                instruction_parts.append(json.dumps(value, indent=2, ensure_ascii=False))
                instruction_parts.append(f"```")
            elif isinstance(value, list):
                instruction_parts.append(f"")
                instruction_parts.append(f"**{key}**:")
                for i, item in enumerate(value, 1):
                    instruction_parts.append(f"{i}. {item}")

    instruction_parts.extend([
        f"",
        f"**重要**: 执行完成后，请务必调用 `report_step_completion` 工具汇报结果：",
        f"- step_id: \"{step_info['step_id']}\"",
        f"- status: \"completed\" (成功) 或 \"failed\" (失败)",
        f"- result_summary: 执行结果的简要描述",
        f"",
        f"请立即开始执行此步骤。"
    ])

    return "\n".join(instruction_parts)

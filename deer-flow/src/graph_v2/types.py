# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
types.py for Graph V2

Defines the simplified state structure for the new Master Agent-centric workflow.
"""

from typing import TypedDict, Annotated, Optional, Dict, Any
from langgraph.graph.message import add_messages

from .unified_models import UnifiedPlan


class State(TypedDict):
    """
    The state for the V2 graph. It's designed around a central, structured
    plan that the Master Agent perceives and acts upon via specialized tools.

    Attributes:
        messages: The history of messages (human, ai, tool) in the conversation.
        plan: A structured object representing the sequence of steps to execute.
              This is the "single source of truth" for the agent's task list.

        # Template system fields (Phase 1 addition)
        template_id: Optional template identifier when using template mode.
        template_params: Parameters for template instantiation.
        template_mode: Whether the current session is using template mode.
        execution_mode: The execution strategy ("auto", "template", "custom").
        template_context: Additional context for template processing.
    """
    messages: Annotated[list, add_messages]
    plan: Optional[UnifiedPlan]

    # Template system fields
    template_id: Optional[str]
    template_params: Optional[Dict[str, Any]]
    template_mode: bool
    execution_mode: str  # "auto", "template", "custom", "hybrid"
    template_context: Optional[Dict[str, Any]]

    # Execution engine fields
    current_step: Optional[str]  # 当前正在执行的步骤ID（执行引擎设置）
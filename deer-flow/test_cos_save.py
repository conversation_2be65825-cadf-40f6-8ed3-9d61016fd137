#!/usr/bin/env python3
"""
测试COS保存功能
验证JSON和音频文件都能正确上传到COS
"""

import logging
import sys
import os
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_cos_save():
    """测试COS保存功能"""
    logger.info("🎯 测试COS保存功能...")
    
    try:
        # 设置环境变量
        os.environ["TONGYI_TINGWU_ACCESS_KEY_ID"] = "LTAI5tBTpMAPNJ3Z6jpZyTSo"
        os.environ["TONGYI_TINGWU_ACCESS_KEY_SECRET"] = "******************************"
        os.environ["TONGYI_TINGWU_APP_KEY"] = "wbp1hepOKEWDiQEC"
        os.environ["TENCENT_SECRET_ID"] = "AKIDeDg1CC1gAXlsqofNvJZnqnaBbqvpZ8Wi"
        os.environ["TENCENT_SECRET_KEY"] = "57DFLWYRAl8LUUN5bJ1ldP0RksqeIl39"
        os.environ["COS_REGION"] = "ap-guangzhou"
        os.environ["COS_BUCKET"] = "duomotai-1317512395"
        
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入配置和工具
        from config.configuration import Configuration
        from tools.audio.video_subtitle_extraction import get_video_subtitle_extraction_tool
        
        # 创建配置
        config = Configuration.from_runnable_config()
        logger.info("✅ 配置加载成功")
        
        # 创建工具
        tool = get_video_subtitle_extraction_tool(config)
        
        if tool is None:
            logger.error("❌ 工具创建失败")
            return False
        
        logger.info("✅ 视频字幕提取工具创建成功")
        
        # 测试视频URL - 使用一个较短的视频进行测试
        video_url = "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/bilibili_BV1Jq4y1C7F8_GAOQING.mp4"
        
        # 准备测试参数 - 启用COS保存
        test_params = {
            "media_url": video_url,
            "language": "zh-CN",
            "auto_speaker_identification": True,
            "output_format": "json",
            "precision_level": "sentence",
            "extract_audio_segments": True,
            "save_to_cos": True,  # 🔥 启用COS保存
            "cos_bucket_prefix": "test-cos-save-tongyi"
        }
        
        logger.info("🚀 开始测试COS保存功能...")
        logger.info("📋 测试参数:")
        for key, value in test_params.items():
            logger.info(f"   {key}: {value}")
        
        logger.info("\n" + "="*80)
        logger.info("📤 正在调用工具，启用COS保存...")
        logger.info("预计需要5-8分钟完成处理（包含COS上传）")
        logger.info("="*80)
        
        # 调用工具
        result = tool.invoke(test_params)
        
        logger.info("✅ 工具调用完成！")
        
        # 分析和展示结果
        if isinstance(result, str):
            if result.startswith("❌"):
                logger.error(f"❌ 工具执行失败: {result}")
                return False
            else:
                logger.info("✅ 工具执行成功")
                
                # 保存完整结果
                with open('cos_save_test_result.txt', 'w', encoding='utf-8') as f:
                    f.write(result)
                logger.info("💾 完整结果已保存到: cos_save_test_result.txt")
                
                # 尝试解析JSON结果
                try:
                    result_data = json.loads(result)
                    
                    # 保存JSON格式
                    with open('cos_save_test_result.json', 'w', encoding='utf-8') as f:
                        json.dump(result_data, f, ensure_ascii=False, indent=2)
                    logger.info("💾 JSON结果已保存到: cos_save_test_result.json")
                    
                    # 分析COS保存结果
                    analyze_cos_save_result(result_data)
                    
                except json.JSONDecodeError:
                    logger.info("📄 结果不是JSON格式，显示原始内容:")
                    logger.info("="*80)
                    print(result)
                    logger.info("="*80)
                
                return True
        else:
            logger.info(f"✅ 工具执行成功，结果类型: {type(result)}")
            logger.info(f"📄 结果内容:")
            print(result)
            return True
            
    except Exception as e:
        logger.error(f"❌ COS保存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_cos_save_result(result_data):
    """分析COS保存结果"""
    logger.info("\n" + "="*80)
    logger.info("☁️ COS保存结果分析")
    logger.info("="*80)
    
    try:
        # 检查COS URLs
        if "cos_urls" in result_data:
            cos_urls = result_data["cos_urls"]
            logger.info("📁 COS保存结果:")
            
            # JSON文件
            if "json_result" in cos_urls:
                json_url = cos_urls["json_result"]
                logger.info(f"   📄 JSON文件: {json_url}")
                logger.info(f"      可直接访问: ✅")
            
            # 音频文件
            if "audio_segments" in cos_urls:
                audio_segments = cos_urls["audio_segments"]
                total_audio_files = 0
                
                logger.info(f"   🎵 音频文件:")
                for speaker_name, segments in audio_segments.items():
                    logger.info(f"      {speaker_name}: {len(segments)} 个文件")
                    total_audio_files += len(segments)
                    
                    # 显示前3个文件示例
                    for i, segment in enumerate(segments[:3]):
                        cos_url = segment.get("cos_url", "")
                        text = segment.get("text", "")[:30]
                        logger.info(f"         {i+1}. {cos_url}")
                        logger.info(f"            文本: {text}...")
                    
                    if len(segments) > 3:
                        logger.info(f"         ... 还有{len(segments)-3}个文件")
                
                logger.info(f"   📊 总计: {total_audio_files} 个音频文件")
            
            # 汇总信息
            if "summary" in cos_urls:
                summary = cos_urls["summary"]
                logger.info(f"   📋 汇总信息: {summary}")
        
        # 检查文件路径是否已更新
        if "audio_segments" in result_data:
            audio_segments = result_data["audio_segments"]
            logger.info(f"\n🔍 文件路径检查:")
            
            for speaker_name, speaker_data in audio_segments.items():
                if isinstance(speaker_data, dict) and "segments" in speaker_data:
                    segments = speaker_data["segments"]
                    
                    # 检查前几个文件路径
                    for i, segment in enumerate(segments[:3]):
                        file_path = segment.get("file_path", "")
                        if file_path.startswith("http"):
                            logger.info(f"   ✅ {speaker_name}_{i+1}: COS URL")
                        else:
                            logger.info(f"   ❌ {speaker_name}_{i+1}: 本地路径 {file_path}")
        
        # 生成使用指南
        logger.info(f"\n💡 使用指南:")
        logger.info("   1. JSON文件包含完整的字幕和说话人信息")
        logger.info("   2. 音频文件可用于声音克隆和AI训练")
        logger.info("   3. 所有文件都有永久的HTTP访问链接")
        logger.info("   4. 可以直接分享给其他人使用")
        
        # 检查是否真的保存成功
        if "cos_urls" in result_data and result_data["cos_urls"]:
            logger.info(f"\n🎉 COS保存完全成功！")
            logger.info("✅ JSON和音频文件都已上传到云存储")
            logger.info("✅ 文件路径已更新为HTTP URL")
            logger.info("✅ 可以永久访问和分享")
        else:
            logger.warning(f"\n⚠️ COS保存可能有问题")
            logger.info("🔧 请检查COS配置和权限")
            
    except Exception as e:
        logger.error(f"❌ COS结果分析失败: {e}")

def main():
    """主函数"""
    logger.info("🎯 开始COS保存功能测试...")
    logger.info("目标: 验证JSON和音频文件都能正确上传到COS")
    
    success = test_cos_save()
    
    logger.info(f"\n{'='*80}")
    logger.info("🎯 COS保存测试总结")
    logger.info(f"{'='*80}")
    
    if success:
        logger.info("🎉 COS保存测试成功！")
        logger.info("")
        logger.info("📁 **生成的文件:**")
        logger.info("   • cos_save_test_result.txt - 完整原始结果")
        logger.info("   • cos_save_test_result.json - JSON格式结果")
        logger.info("")
        logger.info("☁️ **COS保存功能:**")
        logger.info("   • JSON结果文件已上传到COS")
        logger.info("   • 所有音频片段已上传到COS")
        logger.info("   • 文件路径已更新为HTTP URL")
        logger.info("   • 可以永久访问和分享")
        logger.info("")
        logger.info("🎭 **完整解决方案:**")
        logger.info("   • 通义听悟API: 准确识别说话人")
        logger.info("   • COS存储: 永久保存所有文件")
        logger.info("   • HTTP访问: 随时随地可用")
        logger.info("   • AI二创: 完美的素材准备")
    else:
        logger.error("❌ COS保存测试失败")
        logger.info("🔧 请检查COS配置和网络连接")

if __name__ == "__main__":
    main()

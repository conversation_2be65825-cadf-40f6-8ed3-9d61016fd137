#!/usr/bin/env python3
"""
直接处理视频字幕提取
不通过模板系统，直接调用工具
"""

import logging
import sys
import os
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def direct_video_process():
    """直接处理视频"""
    logger.info("🎯 直接处理视频字幕提取...")
    
    try:
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入配置和工具
        from config.configuration import Configuration
        from tools.audio.video_subtitle_extraction import get_video_subtitle_extraction_tool
        
        # 创建配置
        config = Configuration.from_runnable_config()
        logger.info("✅ 配置加载成功")
        
        # 检查通义听悟配置
        logger.info("🔍 检查通义听悟配置:")
        logger.info(f"   subtitle_api_provider: {getattr(config, 'subtitle_api_provider', 'NOT_FOUND')}")
        logger.info(f"   tongyi_tingwu_access_key_id: {getattr(config, 'tongyi_tingwu_access_key_id', 'NOT_FOUND')}")
        logger.info(f"   tongyi_tingwu_access_key_secret: {getattr(config, 'tongyi_tingwu_access_key_secret', 'NOT_FOUND')}")
        logger.info(f"   tongyi_tingwu_app_key: {getattr(config, 'tongyi_tingwu_app_key', 'NOT_FOUND')}")
        
        # 创建工具
        tool = get_video_subtitle_extraction_tool(config)
        
        if tool is None:
            logger.error("❌ 工具创建失败")
            return False
        
        logger.info("✅ 视频字幕提取工具创建成功")
        
        # 您的视频URL
        video_url = "https://duomotai-**********.cos.ap-guangzhou.myqcloud.com/bilibili_BV1Km5kzHEim_GAOQING.mp4"
        
        # 准备参数
        params = {
            "media_url": video_url,
            "language": "zh-CN",
            "auto_speaker_identification": True,
            "output_format": "json",
            "precision_level": "sentence",
            "extract_audio_segments": True,
            "save_to_cos": True,  # 保存到COS
            "cos_bucket_prefix": "wawa-writing-story"
        }
        
        logger.info("🚀 开始处理视频...")
        logger.info("📋 处理参数:")
        for key, value in params.items():
            logger.info(f"   {key}: {value}")
        
        logger.info("\n" + "="*80)
        logger.info("📤 正在处理视频，请耐心等待...")
        logger.info("预计需要3-5分钟完成")
        logger.info("="*80)
        
        # 调用工具
        result = tool.invoke(params)
        
        logger.info("✅ 视频处理完成！")
        
        # 分析结果
        if isinstance(result, str):
            if result.startswith("❌"):
                logger.error(f"❌ 处理失败: {result}")
                return False
            else:
                logger.info("✅ 处理成功")
                
                # 保存结果
                with open('wawa_writing_video_result.txt', 'w', encoding='utf-8') as f:
                    f.write(result)
                logger.info("💾 完整结果已保存到: wawa_writing_video_result.txt")
                
                # 尝试解析JSON
                try:
                    result_data = json.loads(result)
                    
                    # 保存JSON格式
                    with open('wawa_writing_video_result.json', 'w', encoding='utf-8') as f:
                        json.dump(result_data, f, ensure_ascii=False, indent=2)
                    logger.info("💾 JSON结果已保存到: wawa_writing_video_result.json")
                    
                    # 分析结果
                    analyze_video_result(result_data)
                    
                except json.JSONDecodeError:
                    logger.info("📄 结果不是JSON格式，显示原始内容:")
                    logger.info("="*80)
                    print(result[:1000] + "..." if len(result) > 1000 else result)
                    logger.info("="*80)
                
                return True
        else:
            logger.info(f"✅ 处理成功，结果类型: {type(result)}")
            logger.info(f"📄 结果: {str(result)[:500]}...")
            return True
            
    except Exception as e:
        logger.error(f"❌ 视频处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_video_result(result_data):
    """分析视频处理结果"""
    logger.info("\n" + "="*80)
    logger.info("📊 视频处理结果分析")
    logger.info("="*80)
    
    try:
        # 基本信息
        logger.info("📋 基本信息:")
        if "media_info" in result_data:
            media_info = result_data["media_info"]
            logger.info(f"   视频时长: {media_info.get('duration', 'N/A')}秒")
            logger.info(f"   视频格式: {media_info.get('format', 'N/A')}")
            logger.info(f"   是否有音频: {media_info.get('has_audio', 'N/A')}")
        
        # 说话人信息
        if "speaker_info" in result_data:
            speaker_info = result_data["speaker_info"]
            logger.info(f"\n👥 说话人信息:")
            logger.info(f"   识别到的说话人数量: {len(speaker_info)}")
            for speaker_id, speaker_name in speaker_info.items():
                logger.info(f"   {speaker_id}: {speaker_name}")
        
        # 字幕分析
        if "subtitles" in result_data:
            subtitles = result_data["subtitles"]
            logger.info(f"\n📝 字幕分析:")
            logger.info(f"   总字幕数量: {len(subtitles)}")
            
            # 显示前几句字幕
            logger.info(f"   前5句字幕示例:")
            for i, subtitle in enumerate(subtitles[:5]):
                start_time = subtitle.get("start_time", 0) / 1000.0
                end_time = subtitle.get("end_time", 0) / 1000.0
                text = subtitle.get("text", "")
                speaker_name = subtitle.get("speaker_name", "unknown")
                
                logger.info(f"      {i+1}. [{start_time:.1f}s-{end_time:.1f}s] {speaker_name}: {text[:50]}...")
        
        # COS存储信息
        if "cos_urls" in result_data:
            cos_urls = result_data["cos_urls"]
            logger.info(f"\n☁️ COS存储:")
            
            if "json_result" in cos_urls:
                logger.info(f"   📄 JSON结果: {cos_urls['json_result']}")
            
            if "audio_segments" in cos_urls:
                audio_segments = cos_urls["audio_segments"]
                total_files = sum(len(segments) for segments in audio_segments.values())
                logger.info(f"   🎤 音频文件: {total_files} 个")
        
        # 生成改写建议
        logger.info(f"\n💡 蛙蛙写作改写建议:")
        logger.info("   1. 保持原有的幽默节奏和东北方言特色")
        logger.info("   2. 将故事背景改为AI写作平台的使用场景")
        logger.info("   3. 角色可以设定为：用户、AI助手、平台客服等")
        logger.info("   4. 保留经典的包袱和笑点结构")
        logger.info("   5. 融入现代科技元素，如AI写作、智能创作等")
        
    except Exception as e:
        logger.error(f"❌ 结果分析失败: {e}")

def main():
    """主函数"""
    logger.info("🎯 开始直接视频处理...")
    logger.info("目标: 为蛙蛙写作故事改写提取视频素材")
    
    success = direct_video_process()
    
    logger.info(f"\n{'='*80}")
    logger.info("🎯 视频处理总结")
    logger.info(f"{'='*80}")
    
    if success:
        logger.info("🎉 视频处理成功！")
        logger.info("")
        logger.info("📁 **生成的文件:**")
        logger.info("   • wawa_writing_video_result.txt - 完整原始结果")
        logger.info("   • wawa_writing_video_result.json - JSON格式结果")
        logger.info("")
        logger.info("🎭 **下一步工作:**")
        logger.info("   1. 分析原视频的对话结构和节奏")
        logger.info("   2. 设计蛙蛙写作的故事情节")
        logger.info("   3. 保持东北方言的幽默特色")
        logger.info("   4. 生成新的配音和字幕")
        logger.info("")
        logger.info("🚀 **准备就绪:**")
        logger.info("   • 原视频字幕和时间戳已提取")
        logger.info("   • 说话人信息已识别")
        logger.info("   • 音频片段已分离")
        logger.info("   • 可以开始故事改写工作")
    else:
        logger.error("❌ 视频处理失败")
        logger.info("🔧 请检查网络连接和配置")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
直接测试导入
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_direct_multi_speaker_tts():
    """直接测试多人TTS工具"""
    print("🎭 直接测试多人TTS工具...")
    
    try:
        # 直接导入工具函数
        from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
        print("✅ get_multi_speaker_tts_tool导入成功")
        
        # 导入配置
        from src.config.configuration import Configuration
        config = Configuration.from_runnable_config()
        print("✅ Configuration导入成功")
        
        # 创建工具实例
        tool = get_multi_speaker_tts_tool(config=config)
        print(f"✅ 工具创建成功: {tool.name if tool else 'None'}")
        
        if tool:
            print(f"📋 工具类型: {type(tool)}")
            print(f"📋 工具描述长度: {len(tool.description)} 字符")
        
        return tool is not None
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 直接导入测试...")
    result = test_direct_multi_speaker_tts()
    print(f"\n📊 结果: {'✅ 成功' if result else '❌ 失败'}")

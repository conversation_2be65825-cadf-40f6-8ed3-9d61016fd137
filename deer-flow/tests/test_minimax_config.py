#!/usr/bin/env python3
"""
测试Minimax配置集成
"""

import os
import asyncio
import logging
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_configuration():
    """测试Configuration类中的Minimax配置"""
    logger.info("🔧 测试Minimax配置...")
    
    try:
        from src.config.configuration import Configuration
        
        # 创建配置实例
        config = Configuration.from_runnable_config()
        
        logger.info(f"✅ 配置创建成功")
        logger.info(f"📋 Minimax API Key: {'已设置' if config.minimax_api_key else '未设置'}")
        logger.info(f"📋 Minimax Group ID: {'已设置' if config.minimax_group_id else '未设置'}")
        logger.info(f"📋 默认模型: {config.minimax_default_model}")
        logger.info(f"📋 默认女声: {config.minimax_default_female_voice}")
        logger.info(f"📋 默认男声: {config.minimax_default_male_voice}")
        
        return config.minimax_api_key and config.minimax_group_id
        
    except Exception as e:
        logger.error(f"❌ 配置测试失败: {e}")
        return False

async def test_tool_with_config():
    """测试多人TTS工具使用配置"""
    logger.info("🎭 测试多人TTS工具配置集成...")
    
    try:
        from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
        from src.config.configuration import Configuration
        
        # 创建配置
        config = Configuration.from_runnable_config()
        
        # 创建工具
        tool = get_multi_speaker_tts_tool(config)
        
        if tool is None:
            logger.warning("⚠️ 工具未启用（可能是配置缺失）")
            return False
        
        logger.info(f"✅ 工具创建成功: {tool.name}")
        
        # 测试简单的对话（不提供voice_mapping，使用默认声音分配）
        test_input = {
            "dialogue_script": [
                {
                    "speaker": "Alice",
                    "text": "你好，我是Alice。",
                    "emotion": "happy"
                },
                {
                    "speaker": "Bob",
                    "text": "很高兴认识你，我是Bob。",
                    "emotion": "neutral"
                }
            ],
            # 不提供voice_mapping，测试自动分配
            "generate_timestamps": True,
            "enable_concurrent": True
        }
        
        logger.info("📤 测试自动声音分配功能...")
        result = await tool.arun(tool_input=test_input)
        
        logger.info("📥 工具执行完成")
        logger.info(f"📄 执行结果: {result}")
        
        if "✅" in str(result):
            logger.info("🎉 配置集成测试成功！")
            return True
        else:
            logger.error(f"❌ 工具执行失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 工具配置测试失败: {e}", exc_info=True)
        return False

async def main():
    """主测试函数"""
    logger.info("🎯 开始Minimax配置集成测试...")
    
    # 测试步骤
    tests = [
        ("配置类测试", test_configuration),
        ("工具配置集成测试", test_tool_with_config)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*50}")
    logger.info("📊 配置集成测试总结")
    logger.info(f"{'='*50}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 Minimax配置集成完全成功！")
        logger.info("🚀 现在所有TTS工具都可以使用统一的配置了！")
        logger.info("💡 特色功能：")
        logger.info("   • 自动声音分配：不提供voice_mapping时自动为不同说话人分配男女声")
        logger.info("   • 统一配置管理：所有Minimax相关工具共享配置")
        logger.info("   • 环境变量支持：支持从环境变量和配置文件读取")
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    asyncio.run(main())

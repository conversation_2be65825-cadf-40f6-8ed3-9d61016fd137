#!/usr/bin/env python3
"""
验证当前Audio Agent的工具
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(dotenv_path='.env')

def verify_current_audio_agent():
    """验证当前Audio Agent的工具"""
    
    print("🔍 验证当前Audio Agent的工具...")
    
    try:
        from src.agents.agents import audio_creator_agent
        
        print(f"✅ Audio Agent导入成功")
        
        # 获取Agent的图定义
        graph_def = audio_creator_agent.get_graph()
        
        # 查找tools节点
        tools_node = None
        for node_id, node in graph_def.nodes.items():
            if node_id == 'tools':
                tools_node = node
                break
        
        if tools_node:
            print(f"✅ 找到tools节点")
            
            # 获取工具列表
            tools_data = tools_node.data
            if hasattr(tools_data, 'tools_by_name'):
                tools_by_name = tools_data.tools_by_name
                
                print(f"📋 当前Agent工具数量: {len(tools_by_name)}")
                print(f"📋 当前Agent工具列表:")
                
                for i, (tool_name, tool) in enumerate(tools_by_name.items()):
                    print(f"   {i+1}. {tool_name}")
                
                # 检查multi_speaker_tts
                has_multi_tts = 'multi_speaker_tts' in tools_by_name
                print(f"\n📋 multi_speaker_tts在当前Agent中: {'✅ 是' if has_multi_tts else '❌ 否'}")
                
                if has_multi_tts:
                    multi_tts_tool = tools_by_name['multi_speaker_tts']
                    print(f"📋 multi_speaker_tts工具详情:")
                    print(f"   • 名称: {multi_tts_tool.name}")
                    print(f"   • 类型: {type(multi_tts_tool)}")
                    print(f"   • 描述长度: {len(multi_tts_tool.description)} 字符")
                    print(f"   • 有参数模式: {hasattr(multi_tts_tool, 'args_schema')}")
                
                return has_multi_tts
            else:
                print(f"❌ tools节点没有tools_by_name属性")
                return False
        else:
            print(f"❌ 未找到tools节点")
            return False
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_invocation():
    """测试Agent调用（不实际执行）"""
    
    print("\n🧪 测试Agent调用准备...")
    
    try:
        from src.agents.agents import audio_creator_agent
        
        # 准备测试输入
        test_input = {
            "messages": [("human", "请为我创建一个简单的相声对话")]
        }
        
        print(f"✅ 测试输入准备完成")
        print(f"📋 测试输入: {test_input}")
        
        # 检查Agent是否可以调用
        if hasattr(audio_creator_agent, 'invoke'):
            print(f"✅ Agent有invoke方法，可以调用")
            
            # 不实际调用，只是确认方法存在
            print(f"📋 invoke方法: {type(audio_creator_agent.invoke)}")
            
            return True
        else:
            print(f"❌ Agent没有invoke方法")
            return False
        
    except Exception as e:
        print(f"❌ 测试准备失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 验证当前Audio Agent状态...")
    
    result1 = verify_current_audio_agent()
    result2 = test_agent_invocation()
    
    print(f"\n📊 验证结果:")
    print(f"当前Agent工具: {'✅ 包含multi_speaker_tts' if result1 else '❌ 不包含multi_speaker_tts'}")
    print(f"Agent可调用性: {'✅ 可以调用' if result2 else '❌ 无法调用'}")
    
    if result1 and result2:
        print(f"\n🎉 验证成功！")
        print(f"🎭 multi_speaker_tts工具已正确集成到Audio Agent中")
        print(f"🚀 Audio Agent可以正常处理相声剧本任务")
        print(f"")
        print(f"💡 如果您仍然看到只有4个工具的情况，可能是：")
        print(f"   • 浏览器缓存问题 - 请刷新页面")
        print(f"   • 不同的Agent实例 - 请重启应用")
        print(f"   • 模块缓存问题 - 请重新加载模块")
    else:
        print(f"\n⚠️ 验证失败，需要进一步检查")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试路由修复

验证修复后的路由逻辑是否能正确识别多步计划并使用执行引擎。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
from src.graph_v2.types import State
from src.graph_v2.builder import should_continue
from src.graph_v2.execution_engine import should_use_execution_engine
from langchain_core.messages import AIMessage, HumanMessage


def test_routing_with_no_plan():
    """测试无计划时的路由"""
    print("🧪 测试无计划时的路由...")
    
    try:
        # 创建无计划的状态
        state = State(
            messages=[
                HumanMessage(content="你好"),
                AIMessage(content="你好！我可以帮助您什么？")
            ],
            plan=None
        )
        
        # 测试路由判断
        route = should_continue(state)
        assert route == "__end__"  # 应该结束对话
        
        print("   ✅ 无计划时正确路由到结束")
        return True
        
    except Exception as e:
        print(f"   ❌ 无计划路由测试失败: {e}")
        return False


def test_routing_with_tool_calls():
    """测试有工具调用时的路由"""
    print("\n🧪 测试有工具调用时的路由...")
    
    try:
        # 创建有工具调用的状态
        ai_message_with_tools = AIMessage(
            content="我来为您创建计划",
            tool_calls=[{
                "name": "create_plan",
                "args": {"task": "为10个城市制作海报"},
                "id": "call_123"
            }]
        )
        
        state = State(
            messages=[
                HumanMessage(content="为10个城市制作海报"),
                ai_message_with_tools
            ],
            plan=None
        )
        
        # 测试路由判断
        route = should_continue(state)
        assert route == "master_agent"  # 应该继续Master Agent
        
        print("   ✅ 有工具调用时正确路由到Master Agent")
        return True
        
    except Exception as e:
        print(f"   ❌ 工具调用路由测试失败: {e}")
        return False


def test_routing_with_single_step_plan():
    """测试单步计划的路由"""
    print("\n🧪 测试单步计划的路由...")
    
    try:
        # 创建单步计划
        single_step_plan = UnifiedPlan(
            original_task="画一张图片",
            steps=[
                UnifiedStep(
                    step_id="single_step",
                    name="画图",
                    description="画一张图片",
                    tool_to_use="visual_expert"
                )
            ]
        )
        
        state = State(
            messages=[
                HumanMessage(content="画一张图片"),
                AIMessage(content="我已经为您创建了计划")
            ],
            plan=single_step_plan
        )
        
        # 测试should_use_execution_engine
        use_engine = should_use_execution_engine(state)
        assert use_engine == False  # 单步计划不需要执行引擎
        
        # 测试路由判断
        route = should_continue(state)
        assert route == "__end__"  # 应该结束对话
        
        print("   ✅ 单步计划正确路由到结束")
        return True
        
    except Exception as e:
        print(f"   ❌ 单步计划路由测试失败: {e}")
        return False


def test_routing_with_multi_step_plan():
    """测试多步计划的路由"""
    print("\n🧪 测试多步计划的路由...")
    
    try:
        # 创建多步计划
        multi_step_plan = UnifiedPlan(
            original_task="为10个城市制作海报",
            steps=[
                UnifiedStep(
                    step_id="step_1",
                    name="制作巴黎海报",
                    description="为巴黎制作海报",
                    tool_to_use="visual_expert"
                ),
                UnifiedStep(
                    step_id="step_2",
                    name="制作纽约海报",
                    description="为纽约制作海报",
                    tool_to_use="visual_expert"
                ),
                UnifiedStep(
                    step_id="step_3",
                    name="制作东京海报",
                    description="为东京制作海报",
                    tool_to_use="visual_expert"
                )
            ]
        )
        
        state = State(
            messages=[
                HumanMessage(content="为10个城市制作海报"),
                AIMessage(content="我已经为您创建了包含3个步骤的计划")
            ],
            plan=multi_step_plan
        )
        
        # 测试should_use_execution_engine
        use_engine = should_use_execution_engine(state)
        assert use_engine == True  # 多步计划需要执行引擎
        
        # 测试路由判断
        route = should_continue(state)
        assert route == "execution_engine"  # 应该路由到执行引擎
        
        print("   ✅ 多步计划正确路由到执行引擎")
        return True
        
    except Exception as e:
        print(f"   ❌ 多步计划路由测试失败: {e}")
        return False


def test_routing_with_completed_plan():
    """测试已完成计划的路由"""
    print("\n🧪 测试已完成计划的路由...")
    
    try:
        # 创建已完成的计划
        completed_plan = UnifiedPlan(
            original_task="为2个城市制作海报",
            steps=[
                UnifiedStep(
                    step_id="step_1",
                    name="制作巴黎海报",
                    description="为巴黎制作海报",
                    tool_to_use="visual_expert",
                    status="completed"
                ),
                UnifiedStep(
                    step_id="step_2",
                    name="制作纽约海报",
                    description="为纽约制作海报",
                    tool_to_use="visual_expert",
                    status="completed"
                )
            ]
        )
        
        state = State(
            messages=[
                HumanMessage(content="为2个城市制作海报"),
                AIMessage(content="所有海报都已完成！")
            ],
            plan=completed_plan
        )
        
        # 测试should_use_execution_engine
        use_engine = should_use_execution_engine(state)
        assert use_engine == False  # 已完成的计划不需要执行引擎
        
        # 测试路由判断
        route = should_continue(state)
        assert route == "__end__"  # 应该结束对话
        
        print("   ✅ 已完成计划正确路由到结束")
        return True
        
    except Exception as e:
        print(f"   ❌ 已完成计划路由测试失败: {e}")
        return False


def test_template_plan_routing():
    """测试模板计划的路由"""
    print("\n🧪 测试模板计划的路由...")
    
    try:
        # 创建模板计划
        template_plan = UnifiedPlan(
            original_task="使用模板制作城市海报",
            steps=[
                UnifiedStep(
                    step_id="template_step",
                    name="模板步骤",
                    description="使用模板制作海报",
                    tool_to_use="visual_expert"
                )
            ],
            is_from_template=True,
            template_id="city_poster_template"
        )
        
        state = State(
            messages=[
                HumanMessage(content="使用模板制作城市海报"),
                AIMessage(content="我已经使用模板为您创建了计划")
            ],
            plan=template_plan
        )
        
        # 测试should_use_execution_engine
        use_engine = should_use_execution_engine(state)
        assert use_engine == True  # 模板计划需要执行引擎
        
        # 测试路由判断
        route = should_continue(state)
        assert route == "execution_engine"  # 应该路由到执行引擎
        
        print("   ✅ 模板计划正确路由到执行引擎")
        return True
        
    except Exception as e:
        print(f"   ❌ 模板计划路由测试失败: {e}")
        return False


def main():
    """运行所有路由测试"""
    print("🚀 开始测试路由修复\n")
    
    tests = [
        ("无计划路由", test_routing_with_no_plan),
        ("工具调用路由", test_routing_with_tool_calls),
        ("单步计划路由", test_routing_with_single_step_plan),
        ("多步计划路由", test_routing_with_multi_step_plan),
        ("已完成计划路由", test_routing_with_completed_plan),
        ("模板计划路由", test_template_plan_routing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有路由测试通过！")
        print("\n📋 修复总结:")
        print("✅ 修复了路由时机问题")
        print("✅ 工具调用时继续Master Agent")
        print("✅ 多步计划正确路由到执行引擎")
        print("✅ 单步计划和已完成计划正确结束")
        print("✅ 模板计划正确使用执行引擎")
        
        print("\n🚀 现在应该能正确使用执行引擎了！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
        return False


if __name__ == "__main__":
    main()

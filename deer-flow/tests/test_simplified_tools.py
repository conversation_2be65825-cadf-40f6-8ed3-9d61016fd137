#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试简化后的工具体系

验证新的工具架构是否正常工作。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
from src.graph_v2.types import State


def test_simplified_planning_tools():
    """测试简化的规划工具"""
    print("🧪 测试简化的规划工具...")
    
    try:
        from src.tools.planning import create_plan, initialize_planning_tools, planning_tools
        
        # 检查工具列表
        assert len(planning_tools) == 1
        assert planning_tools[0] == create_plan
        
        print("   ✅ 规划工具导入成功")
        print(f"   工具数量: {len(planning_tools)} (预期: 1)")
        print(f"   工具名称: {[tool.name for tool in planning_tools]}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 规划工具测试失败: {e}")
        return False


def test_simplified_template_tools():
    """测试简化的模板工具"""
    print("\n🧪 测试简化的模板工具...")
    
    try:
        from src.tools.template_tools import use_template, template_tools
        
        # 检查工具列表
        assert len(template_tools) == 1
        assert template_tools[0] == use_template
        
        print("   ✅ 模板工具导入成功")
        print(f"   工具数量: {len(template_tools)} (预期: 1)")
        print(f"   工具名称: {[tool.name for tool in template_tools]}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模板工具测试失败: {e}")
        return False


def test_simplified_state_management():
    """测试简化的状态管理工具"""
    print("\n🧪 测试简化的状态管理工具...")
    
    try:
        from src.tools.state_management import (
            get_plan_status,
            get_next_step,
            report_step_completion,
            state_management_tools
        )
        
        # 检查工具列表
        assert len(state_management_tools) == 3
        expected_tool_names = {"get_plan_status", "get_next_step", "report_step_completion"}
        actual_tool_names = {tool.name for tool in state_management_tools}
        assert actual_tool_names == expected_tool_names
        
        print("   ✅ 状态管理工具导入成功")
        print(f"   工具数量: {len(state_management_tools)} (预期: 3)")
        print(f"   工具名称: {[tool.name for tool in state_management_tools]}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 状态管理工具测试失败: {e}")
        return False


def test_state_management_functionality():
    """测试状态管理工具的功能"""
    print("\n🧪 测试状态管理功能...")
    
    try:
        from src.tools.state_management import get_plan_status, get_next_step
        
        # 测试空状态
        empty_state = {}
        status_result = get_plan_status.func(empty_state)
        assert "没有执行计划" in status_result
        
        next_step_result = get_next_step.func(empty_state)
        assert "没有执行计划" in next_step_result
        
        print("   ✅ 空状态处理正确")
        
        # 测试有计划的状态
        test_plan = UnifiedPlan(
            original_task="测试任务",
            steps=[
                UnifiedStep(
                    step_id="step1",
                    name="步骤1",
                    description="测试步骤1",
                    tool_to_use="visual_expert"
                )
            ]
        )
        
        state_with_plan = {"plan": test_plan}
        
        status_result = get_plan_status.func(state_with_plan)
        assert "测试任务" in status_result
        
        next_step_result = get_next_step.func(state_with_plan)
        assert "step1" in next_step_result
        
        print("   ✅ 计划状态处理正确")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 状态管理功能测试失败: {e}")
        return False


def test_nodes_integration():
    """测试nodes.py的工具集成"""
    print("\n🧪 测试nodes.py工具集成...")
    
    try:
        # 模拟nodes.py中的工具导入
        from src.tools.experts import get_visual_expert_tool, get_audio_expert_tool, get_video_expert_tool
        from src.tools.planning import create_plan, initialize_planning_tools
        from src.tools.state_management import state_management_tools
        from src.tools.template_tools import use_template
        from src.tools.understanding import (
            get_multimodal_understanding_tool,
            get_image_understanding_tool,
            get_video_understanding_tool,
            get_audio_understanding_tool
        )
        
        print("   ✅ 所有工具导入成功")
        
        # 检查工具数量
        planning_tools = [create_plan]
        template_tools = [use_template]
        
        print(f"   规划工具: {len(planning_tools)} (预期: 1)")
        print(f"   状态管理工具: {len(state_management_tools)} (预期: 3)")
        print(f"   模板工具: {len(template_tools)} (预期: 1)")
        
        total_core_tools = len(planning_tools) + len(state_management_tools) + len(template_tools)
        print(f"   核心工具总数: {total_core_tools} (预期: 5)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ nodes.py工具集成测试失败: {e}")
        return False


def test_tool_descriptions():
    """测试工具描述是否清晰"""
    print("\n🧪 测试工具描述...")
    
    try:
        from src.tools.planning import create_plan
        from src.tools.template_tools import use_template
        from src.tools.state_management import get_plan_status, get_next_step, report_step_completion
        
        tools_to_check = [
            create_plan,
            use_template,
            get_plan_status,
            get_next_step,
            report_step_completion
        ]
        
        for tool in tools_to_check:
            assert hasattr(tool, 'description'), f"{tool.name} 缺少描述"
            assert len(tool.description) > 50, f"{tool.name} 描述太短"
            print(f"   ✅ {tool.name}: {tool.description[:60]}...")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 工具描述测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 开始测试简化后的工具体系\n")
    
    tests = [
        ("简化规划工具", test_simplified_planning_tools),
        ("简化模板工具", test_simplified_template_tools),
        ("简化状态管理工具", test_simplified_state_management),
        ("状态管理功能", test_state_management_functionality),
        ("nodes.py集成", test_nodes_integration),
        ("工具描述", test_tool_descriptions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！工具简化成功。")
        print("\n📊 简化效果：")
        print("- 规划工具：2个 → 1个")
        print("- 模板工具：4个 → 1个") 
        print("- 状态管理：10个 → 3个")
        print("- 总工具数：19个 → 8个 (减少58%)")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
        return False


if __name__ == "__main__":
    main()

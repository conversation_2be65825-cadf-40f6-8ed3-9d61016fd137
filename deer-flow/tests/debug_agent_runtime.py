#!/usr/bin/env python3
"""
调试Agent运行时工具
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(dotenv_path='.env')

def debug_agent_runtime():
    """调试Agent运行时工具"""
    
    print("🔍 检查Audio Agent运行时工具...")
    
    try:
        from src.agents.agents import audio_creator_agent
        
        print(f"✅ Audio Agent导入成功")
        print(f"📋 Agent类型: {type(audio_creator_agent)}")
        
        # 检查Agent的内部结构
        print(f"\n🔍 Agent内部结构:")
        print(f"📋 Agent属性: {[attr for attr in dir(audio_creator_agent) if not attr.startswith('_')]}")
        
        # 尝试获取Agent的配置
        if hasattr(audio_creator_agent, 'config'):
            config = audio_creator_agent.config
            print(f"📋 Agent config: {config}")
        
        # 尝试获取Agent的图结构
        if hasattr(audio_creator_agent, 'graph'):
            graph = audio_creator_agent.graph
            print(f"📋 Agent graph: {graph}")
            
            # 检查图的节点
            if hasattr(graph, 'nodes'):
                print(f"📋 Graph nodes: {list(graph.nodes.keys())}")
        
        # 尝试获取Agent的工具绑定
        if hasattr(audio_creator_agent, 'get_graph'):
            try:
                graph_def = audio_creator_agent.get_graph()
                print(f"📋 Graph definition: {graph_def}")
            except Exception as e:
                print(f"⚠️ 无法获取graph definition: {e}")
        
        # 检查是否有工具相关的属性
        for attr_name in dir(audio_creator_agent):
            if 'tool' in attr_name.lower():
                attr_value = getattr(audio_creator_agent, attr_name)
                print(f"📋 {attr_name}: {attr_value}")
        
        # 尝试模拟一个简单的调用来查看工具
        print(f"\n🧪 尝试模拟调用...")
        
        # 创建一个简单的状态
        test_state = {
            "messages": [("human", "测试消息")]
        }
        
        # 尝试获取Agent的可用工具
        try:
            # 这可能会触发Agent的内部工具绑定
            print(f"📋 测试状态: {test_state}")
            
            # 检查Agent是否有invoke方法
            if hasattr(audio_creator_agent, 'invoke'):
                print(f"✅ Agent有invoke方法")
                
                # 不实际调用，只是检查方法存在
                invoke_method = getattr(audio_creator_agent, 'invoke')
                print(f"📋 invoke方法: {invoke_method}")
            
        except Exception as e:
            print(f"⚠️ 模拟调用失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent运行时调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_langgraph_tools():
    """调试LangGraph工具绑定"""
    
    print("\n🔍 检查LangGraph工具绑定...")
    
    try:
        from langgraph.prebuilt import create_react_agent
        from src.tools import audio_tools
        from src.llms.llm import get_llm_by_type
        from src.config.agents import AGENT_LLM_MAP
        
        print(f"✅ 导入成功")
        print(f"📋 audio_tools数量: {len(audio_tools)}")
        
        # 获取LLM
        llm = get_llm_by_type(AGENT_LLM_MAP["audio_creator"])
        print(f"📋 LLM: {llm}")
        
        # 手动创建一个简单的Agent来测试工具绑定
        print(f"\n🧪 手动创建简单Agent...")
        
        simple_agent = create_react_agent(
            name="test_audio_agent",
            model=llm,
            tools=audio_tools
        )
        
        print(f"✅ 简单Agent创建成功: {type(simple_agent)}")
        
        # 检查简单Agent的属性
        print(f"📋 简单Agent属性: {[attr for attr in dir(simple_agent) if not attr.startswith('_')]}")
        
        # 尝试获取工具信息
        if hasattr(simple_agent, 'get_graph'):
            try:
                graph_def = simple_agent.get_graph()
                print(f"📋 简单Agent图定义: {graph_def}")
            except Exception as e:
                print(f"⚠️ 无法获取简单Agent图定义: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ LangGraph工具绑定调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_tool_filtering():
    """调试工具过滤"""
    
    print("\n🔍 检查工具过滤...")
    
    try:
        from src.tools import audio_tools, multi_speaker_tts_tool
        
        print(f"📋 原始audio_tools: {len(audio_tools)}")
        
        # 检查每个工具的有效性
        print(f"\n🔍 检查每个工具的有效性:")
        for i, tool in enumerate(audio_tools):
            if tool is not None:
                name = getattr(tool, 'name', 'Unknown')
                is_callable = callable(tool)
                has_invoke = hasattr(tool, 'invoke')
                print(f"   {i+1}. {name}: callable={is_callable}, has_invoke={has_invoke}")
                
                # 特别检查multi_speaker_tts工具
                if name == 'multi_speaker_tts':
                    print(f"      🎭 multi_speaker_tts详细信息:")
                    print(f"         类型: {type(tool)}")
                    print(f"         描述长度: {len(tool.description)}")
                    print(f"         参数模式: {hasattr(tool, 'args_schema')}")
                    if hasattr(tool, 'args_schema'):
                        print(f"         参数类: {tool.args_schema}")
            else:
                print(f"   {i+1}. None")
        
        # 检查工具过滤逻辑
        filtered_tools = [tool for tool in audio_tools if tool is not None]
        print(f"\n📋 过滤后工具数量: {len(filtered_tools)}")
        
        # 检查multi_speaker_tts是否在过滤后的列表中
        multi_tts_in_filtered = any(
            getattr(tool, 'name', '') == 'multi_speaker_tts' 
            for tool in filtered_tools
        )
        print(f"📋 multi_speaker_tts在过滤后列表中: {'✅ 是' if multi_tts_in_filtered else '❌ 否'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具过滤调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 开始Agent运行时调试...")
    
    tests = [
        ("Agent运行时", debug_agent_runtime),
        ("LangGraph工具绑定", debug_langgraph_tools),
        ("工具过滤", debug_tool_filtering)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print(f"{'='*50}")
        
        result = test_func()
        print(f"📊 {test_name}: {'✅ 成功' if result else '❌ 失败'}")
    
    print(f"\n🎯 调试完成")

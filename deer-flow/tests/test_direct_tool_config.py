#!/usr/bin/env python3
"""
直接测试多人TTS工具配置（避免循环导入）
"""

import os
import asyncio
import logging
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

async def test_direct_tool():
    """直接测试多人TTS工具"""
    logger.info("🎭 直接测试多人TTS工具配置...")
    
    try:
        # 直接导入避免循环导入
        from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
        from src.config.configuration import Configuration
        
        # 创建配置
        config = Configuration.from_runnable_config()
        
        logger.info(f"📋 使用配置:")
        logger.info(f"   • API Key: {'已设置' if config.minimax_api_key else '未设置'}")
        logger.info(f"   • Group ID: {'已设置' if config.minimax_group_id else '未设置'}")
        logger.info(f"   • 默认模型: {config.minimax_default_model}")
        logger.info(f"   • 默认女声: {config.minimax_default_female_voice}")
        logger.info(f"   • 默认男声: {config.minimax_default_male_voice}")
        
        # 创建工具
        tool = get_multi_speaker_tts_tool(config)
        
        if tool is None:
            logger.warning("⚠️ 工具未启用（可能是配置缺失）")
            return False
        
        logger.info(f"✅ 工具创建成功: {tool.name}")
        
        # 测试1：使用自动声音分配（不提供voice_mapping）
        logger.info("\n🧪 测试1: 自动声音分配")
        test_input_1 = {
            "dialogue_script": [
                {
                    "speaker": "Alice",
                    "text": "你好，我是Alice，很高兴见到你！",
                    "emotion": "happy"
                },
                {
                    "speaker": "Bob", 
                    "text": "很高兴认识你，我是Bob。",
                    "emotion": "neutral"
                },
                {
                    "speaker": "Charlie",
                    "text": "大家好，我是Charlie！",
                    "emotion": "happy"
                }
            ],
            # 不提供voice_mapping，测试自动分配
            "generate_timestamps": True,
            "enable_concurrent": True
        }
        
        logger.info("📤 测试自动声音分配（Alice=女声, Bob=男声, Charlie=女声）...")
        result1 = await tool.arun(tool_input=test_input_1)
        
        logger.info("📥 测试1完成")
        logger.info(f"📄 结果: {result1}")
        
        if "✅" not in str(result1):
            logger.error("❌ 测试1失败")
            return False
        
        logger.info("✅ 测试1成功 - 自动声音分配正常工作")
        
        # 测试2：使用手动声音映射
        logger.info("\n🧪 测试2: 手动声音映射")
        test_input_2 = {
            "dialogue_script": [
                {
                    "speaker": "主持人",
                    "text": "欢迎大家收听今天的节目！",
                    "emotion": "happy"
                },
                {
                    "speaker": "嘉宾",
                    "text": "很高兴来到这里分享。",
                    "emotion": "neutral"
                }
            ],
            "voice_mapping": {
                "主持人": "female-yujie",
                "嘉宾": "male-qn-qingse"
            },
            "generate_timestamps": True,
            "enable_concurrent": True
        }
        
        logger.info("📤 测试手动声音映射...")
        result2 = await tool.arun(tool_input=test_input_2)
        
        logger.info("📥 测试2完成")
        logger.info(f"📄 结果: {result2}")
        
        if "✅" not in str(result2):
            logger.error("❌ 测试2失败")
            return False
        
        logger.info("✅ 测试2成功 - 手动声音映射正常工作")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 直接工具测试失败: {e}", exc_info=True)
        return False

async def main():
    """主测试函数"""
    logger.info("🎯 开始直接工具配置测试...")
    
    result = await test_direct_tool()
    
    logger.info(f"\n{'='*50}")
    logger.info("📊 测试总结")
    logger.info(f"{'='*50}")
    
    if result:
        logger.info("🎉 多人TTS工具配置集成完全成功！")
        logger.info("")
        logger.info("🚀 **新功能特色:**")
        logger.info("   ✨ 统一配置管理 - 所有Minimax工具共享配置")
        logger.info("   🎭 智能声音分配 - 不提供映射时自动分配男女声")
        logger.info("   🔧 灵活配置源 - 支持环境变量和配置文件")
        logger.info("   📋 默认值设置 - 合理的默认模型和声音选择")
        logger.info("")
        logger.info("💡 **使用方式:**")
        logger.info("   • 简单模式: 只提供dialogue_script，自动分配声音")
        logger.info("   • 精确模式: 提供voice_mapping，精确控制每个角色声音")
        logger.info("   • 配置模式: 在config/conf.yaml中统一管理所有设置")
        logger.info("")
        logger.info("🎯 现在您的AI Agent可以更方便地使用多人TTS功能了！")
    else:
        logger.warning("⚠️ 测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    asyncio.run(main())

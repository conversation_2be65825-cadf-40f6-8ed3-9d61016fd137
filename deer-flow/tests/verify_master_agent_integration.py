#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证主Agent理解工具集成

简化的验证脚本，确认主Agent可以访问理解工具。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.append(str(project_root / "src"))

def verify_tools_in_master_agent():
    """验证理解工具是否正确添加到主Agent中"""
    print("🔍 验证主Agent工具集成...")
    
    try:
        # 模拟主Agent节点中的工具创建过程
        from config.configuration import Configuration
        from tools.understanding import (
            get_multimodal_understanding_tool,
            get_image_understanding_tool,
            get_video_understanding_tool
        )
        
        # 创建配置
        app_config = Configuration.from_runnable_config()
        
        # 创建理解工具（模拟主Agent节点中的逻辑）
        understanding_tools = [
            get_multimodal_understanding_tool(app_config),
            get_image_understanding_tool(app_config),
            get_video_understanding_tool(app_config),
        ]
        
        print("✅ 理解工具创建成功:")
        for tool in understanding_tools:
            print(f"   • {tool.name}: {tool.description[:80]}...")
        
        # 验证工具可以被调用
        test_image = "https://upload.wikimedia.org/wikipedia/commons/thumb/3/3a/Cat03.jpg/1200px-Cat03.jpg"
        
        print("\n🧪 测试工具调用...")
        result = understanding_tools[1].invoke({  # 使用image_understanding工具
            "image_url": test_image,
            "question": "简要描述这张图片",
            "analysis_type": "general",
            "detail_level": "brief"
        })
        
        print("✅ 工具调用成功")
        print(f"📄 结果: {result[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_prompt_content():
    """验证提示词内容"""
    print("\n📝 验证提示词内容...")
    
    try:
        prompt_path = project_root / "src" / "prompts" / "master_agent_prompt.md"
        
        with open(prompt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键内容
        checks = [
            ("理解工具列表", "multimodal_understanding" in content),
            ("图片理解工具", "image_understanding" in content),
            ("视频理解工具", "video_understanding" in content),
            ("使用指南", "内容理解工具使用指南" in content),
            ("工具参数说明", "media_url" in content),
            ("使用示例", "使用示例" in content)
        ]
        
        all_passed = True
        for check_name, passed in checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 提示词验证失败: {e}")
        return False

def verify_code_integration():
    """验证代码集成"""
    print("\n🔧 验证代码集成...")
    
    try:
        # 检查nodes.py中的导入
        nodes_path = project_root / "src" / "graph_v2" / "nodes.py"
        
        with open(nodes_path, 'r', encoding='utf-8') as f:
            nodes_content = f.read()
        
        integration_checks = [
            ("理解工具导入", "from src.tools.understanding import" in nodes_content),
            ("多模态工具导入", "get_multimodal_understanding_tool" in nodes_content),
            ("图片工具导入", "get_image_understanding_tool" in nodes_content),
            ("视频工具导入", "get_video_understanding_tool" in nodes_content),
            ("工具实例化", "understanding_tools = [" in nodes_content),
            ("工具添加到列表", "+ understanding_tools" in nodes_content)
        ]
        
        all_passed = True
        for check_name, passed in integration_checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 代码集成验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🦌 DeerFlow 主Agent理解工具集成验证")
    print("=" * 50)
    
    # 运行验证
    verifications = [
        ("工具集成", verify_tools_in_master_agent),
        ("提示词内容", verify_prompt_content),
        ("代码集成", verify_code_integration),
    ]
    
    results = []
    for verification_name, verification_func in verifications:
        try:
            success = verification_func()
            results.append((verification_name, success))
        except Exception as e:
            print(f"❌ {verification_name}验证出错: {e}")
            results.append((verification_name, False))
    
    # 输出验证结果
    print("\n" + "=" * 50)
    print("📊 验证结果汇总:")
    
    passed = 0
    for verification_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {verification_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 总计: {passed}/{len(results)} 项验证通过")
    
    if passed == len(results):
        print("\n🎉 验证完成！主Agent理解工具集成成功！")
        print("\n🚀 集成成果:")
        print("  ✅ 理解工具已添加到主Agent的工具列表")
        print("  ✅ 主Agent提示词已更新，包含详细使用说明")
        print("  ✅ 代码集成完整，支持运行时工具创建")
        print("  ✅ 工具功能正常，可以分析图片和视频")
        print("\n💡 现在主Agent可以:")
        print("  • 直接分析用户提供的图片和视频")
        print("  • 基于理解结果进行智能创作")
        print("  • 在复杂任务规划中使用理解工具")
        print("  • 提供多模态内容的专业分析")
        return 0
    else:
        print("\n⚠️  部分验证失败，请检查集成。")
        return 1

if __name__ == "__main__":
    exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一系统集成测试

验证修复循环导入后，整个统一系统是否正常工作。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
from langchain_core.messages import AIMessage


def test_import_resolution():
    """测试循环导入问题是否解决"""
    print("🧪 测试导入解决方案...")
    
    try:
        # 测试agents模块导入
        import src.agents
        print("✅ src.agents 导入成功")
        
        # 测试tools模块导入
        import src.tools
        print("✅ src.tools 导入成功")
        
        # 测试具体agent导入
        from src.agents.agents import audio_creator_agent
        print("✅ audio_creator_agent 导入成功")
        
        # 测试统一模型导入
        from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
        print("✅ 统一模型导入成功")
        
        # 测试状态管理工具导入
        from src.tools.state_management import new_state_management_tools
        print(f"✅ 状态管理工具导入成功，数量: {len(new_state_management_tools)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False


def test_unified_plan_functionality():
    """测试统一Plan模型功能"""
    print("\n🧪 测试统一Plan模型功能...")
    
    try:
        from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
        from src.graph_v2.migration_utils import ensure_unified_plan
        
        # 创建测试计划
        steps = [
            UnifiedStep(
                step_id="collect_materials",
                name="收集素材",
                description="收集哪吒相关素材",
                tool_to_use="visual_expert"
            ),
            UnifiedStep(
                step_id="generate_video",
                name="生成视频",
                description="制作鬼畜视频",
                tool_to_use="video_expert",
                dependencies=["collect_materials"]
            )
        ]
        
        plan = UnifiedPlan(
            original_task="制作哪吒鬼畜视频",
            steps=steps,
            template_id="ai_parody_video",
            is_from_template=True
        )
        
        print(f"✅ 计划创建成功: {plan.plan_id}")
        print(f"步骤数量: {len(plan.steps)}")
        
        # 测试执行逻辑
        executable_steps = plan.get_next_executable_steps()
        print(f"可执行步骤: {[s.step_id for s in executable_steps]}")
        
        # 完成第一个步骤
        step1 = plan.get_step("collect_materials")
        step1.mark_completed({"result": "成功收集素材"})
        
        # 检查进度
        progress = plan.get_progress_info()
        print(f"进度: {progress['completed_steps']}/{progress['total_steps']}")
        
        # 检查下一步
        next_executable = plan.get_next_executable_steps()
        print(f"下一步可执行: {[s.step_id for s in next_executable]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 统一Plan功能测试失败: {e}")
        return False


def test_state_management_integration():
    """测试状态管理集成"""
    print("\n🧪 测试状态管理集成...")
    
    try:
        from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
        from src.graph_v2.state_handler import (
            apply_status_update,
            get_plan_progress,
            build_step_instruction
        )
        
        # 创建测试状态
        plan = UnifiedPlan(
            original_task="测试状态管理",
            steps=[
                UnifiedStep(
                    step_id="test_step",
                    name="测试步骤",
                    description="测试状态管理功能",
                    tool_to_use="test_tool"
                )
            ]
        )
        
        state = {"plan": plan, "messages": []}
        
        # 测试状态更新
        update = {
            "step_id": "test_step",
            "status": "completed",
            "result_summary": "测试成功完成",
            "timestamp": datetime.now().isoformat()
        }
        
        success = apply_status_update(state, update)
        print(f"✅ 状态更新成功: {success}")
        
        # 检查更新后的状态
        updated_step = plan.get_step("test_step")
        print(f"步骤状态: {updated_step.status}")
        
        # 测试进度获取
        progress = get_plan_progress(state)
        print(f"✅ 进度获取成功: {progress['progress_percentage']}%")
        
        # 测试指令构建
        step_info = {
            "step_id": "test_step",
            "name": "测试步骤",
            "description": "测试描述",
            "tool_to_use": "test_tool"
        }
        instruction = build_step_instruction(step_info)
        print("✅ 指令构建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 状态管理集成测试失败: {e}")
        return False


def test_migration_functionality():
    """测试迁移功能"""
    print("\n🧪 测试迁移功能...")
    
    try:
        from src.graph_v2.migration_utils import (
            migrate_dict_to_unified,
            auto_migrate_plan,
            ensure_unified_plan
        )
        
        # 测试字典迁移
        legacy_dict = {
            "original_task": "测试迁移",
            "steps": [
                {
                    "step_id": 1,
                    "description": "第一步",
                    "tool_to_use": "tool1",
                    "status": "pending",
                    "dependencies": []
                },
                {
                    "step_id": 2,
                    "description": "第二步",
                    "tool_to_use": "tool2",
                    "status": "pending",
                    "dependencies": [1]
                }
            ]
        }
        
        unified_plan = migrate_dict_to_unified(legacy_dict)
        print(f"✅ 字典迁移成功: {unified_plan.original_task}")
        print(f"步骤ID转换: {[s.step_id for s in unified_plan.steps]}")
        
        # 测试自动迁移
        auto_migrated = auto_migrate_plan(legacy_dict)
        print("✅ 自动迁移成功")
        
        # 测试ensure函数
        ensured_plan = ensure_unified_plan(legacy_dict)
        print("✅ ensure_unified_plan成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 迁移功能测试失败: {e}")
        return False


def test_new_state_tools():
    """测试新的状态管理工具"""
    print("\n🧪 测试新的状态管理工具...")
    
    try:
        from src.tools.state_management import (
            report_step_completion,
            request_current_step_info,
            request_plan_progress
        )
        
        # 测试工具调用
        result1 = report_step_completion.invoke({
            "step_id": "test_step",
            "status": "completed",
            "result_summary": "测试完成"
        })
        print("✅ report_step_completion 工具调用成功")
        
        result2 = request_current_step_info.invoke({})
        print("✅ request_current_step_info 工具调用成功")
        
        result3 = request_plan_progress.invoke({})
        print("✅ request_plan_progress 工具调用成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 新状态管理工具测试失败: {e}")
        return False


def main():
    """运行所有集成测试"""
    print("🚀 开始统一系统集成测试\n")
    
    tests = [
        ("导入解决方案", test_import_resolution),
        ("统一Plan功能", test_unified_plan_functionality),
        ("状态管理集成", test_state_management_integration),
        ("迁移功能", test_migration_functionality),
        ("新状态管理工具", test_new_state_tools),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"测试: {test_name}")
        print('='*60)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*60}")
    print(f"集成测试结果: {passed}/{total} 通过")
    print('='*60)
    
    if passed == total:
        print("🎉 所有集成测试通过！统一系统工作正常。")
        print("\n📋 系统状态总结:")
        print("✅ 循环导入问题已解决")
        print("✅ UnifiedPlan模型正常工作")
        print("✅ 状态管理机制正常")
        print("✅ 迁移工具正常")
        print("✅ 新的状态管理工具正常")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
        return False


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试状态更新修复

验证create_plan工具的结果能正确更新到state中。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
from src.graph_v2.types import State
from src.graph_v2.state_handler import process_master_agent_result, extract_plan_updates, apply_plan_update
from langchain_core.messages import AIMessage, ToolMessage


def test_extract_plan_updates_from_result():
    """测试从result中提取计划更新"""
    print("🧪 测试从result中提取计划更新...")
    
    try:
        # 创建测试计划
        test_plan = UnifiedPlan(
            original_task="测试任务",
            steps=[
                UnifiedStep(
                    step_id="step1",
                    name="步骤1",
                    description="测试步骤1",
                    tool_to_use="visual_expert"
                )
            ]
        )
        
        # 模拟Master Agent的结果（包含plan）
        result = {
            "messages": [
                AIMessage(content="我已经创建了计划"),
            ],
            "plan": test_plan  # 直接在result中包含plan
        }
        
        # 提取计划更新
        plan_updates = extract_plan_updates(result)
        
        assert len(plan_updates) == 1
        assert plan_updates[0]["action"] == "update_plan"
        assert plan_updates[0]["plan"] == test_plan
        
        print("   ✅ 成功从result中提取计划更新")
        return True
        
    except Exception as e:
        print(f"   ❌ 提取计划更新测试失败: {e}")
        return False


def test_extract_plan_updates_from_tool_message():
    """测试从工具消息中提取计划更新"""
    print("\n🧪 测试从工具消息中提取计划更新...")
    
    try:
        # 创建测试计划
        test_plan = UnifiedPlan(
            original_task="测试任务",
            steps=[
                UnifiedStep(
                    step_id="step1",
                    name="步骤1",
                    description="测试步骤1",
                    tool_to_use="visual_expert"
                )
            ]
        )
        
        # 模拟工具消息（create_plan的返回）
        tool_message = ToolMessage(
            content={"plan": test_plan},
            tool_call_id="call_123"
        )
        tool_message.type = "tool"  # 确保类型正确
        
        result = {
            "messages": [
                AIMessage(content="我来创建计划", tool_calls=[{
                    "name": "create_plan",
                    "args": {"task": "测试任务"},
                    "id": "call_123"
                }]),
                tool_message
            ]
        }
        
        # 提取计划更新
        plan_updates = extract_plan_updates(result)
        
        assert len(plan_updates) == 1
        assert plan_updates[0]["action"] == "update_plan"
        assert plan_updates[0]["plan"] == test_plan
        
        print("   ✅ 成功从工具消息中提取计划更新")
        return True
        
    except Exception as e:
        print(f"   ❌ 工具消息计划更新测试失败: {e}")
        return False


def test_apply_plan_update():
    """测试应用计划更新"""
    print("\n🧪 测试应用计划更新...")
    
    try:
        # 创建初始状态（无计划）
        state = State(
            messages=[],
            plan=None
        )
        
        # 创建测试计划
        test_plan = UnifiedPlan(
            original_task="测试任务",
            steps=[
                UnifiedStep(
                    step_id="step1",
                    name="步骤1",
                    description="测试步骤1",
                    tool_to_use="visual_expert"
                ),
                UnifiedStep(
                    step_id="step2",
                    name="步骤2",
                    description="测试步骤2",
                    tool_to_use="audio_expert"
                )
            ]
        )
        
        # 应用计划更新
        plan_update = {
            "action": "update_plan",
            "plan": test_plan
        }
        
        success = apply_plan_update(state, plan_update)
        
        assert success == True
        assert state["plan"] == test_plan
        assert len(state["plan"].steps) == 2
        
        print("   ✅ 成功应用计划更新到状态")
        return True
        
    except Exception as e:
        print(f"   ❌ 应用计划更新测试失败: {e}")
        return False


def test_process_master_agent_result_with_plan():
    """测试处理包含计划的Master Agent结果"""
    print("\n🧪 测试处理包含计划的Master Agent结果...")
    
    try:
        # 创建初始状态
        initial_state = State(
            messages=[],
            plan=None
        )
        
        # 创建测试计划
        test_plan = UnifiedPlan(
            original_task="为城市制作海报",
            steps=[
                UnifiedStep(
                    step_id="create_beijing_poster",
                    name="创建北京海报",
                    description="为北京创建海报",
                    tool_to_use="visual_expert"
                ),
                UnifiedStep(
                    step_id="create_shanghai_poster",
                    name="创建上海海报",
                    description="为上海创建海报",
                    tool_to_use="visual_expert"
                )
            ]
        )
        
        # 模拟Master Agent的结果
        master_agent_result = {
            "messages": [
                AIMessage(content="我已经为您创建了包含2个步骤的计划"),
            ],
            "plan": test_plan
        }
        
        # 处理Master Agent结果
        updated_state = process_master_agent_result(initial_state, master_agent_result)
        
        # 验证状态更新
        assert updated_state["plan"] is not None
        assert updated_state["plan"] == test_plan
        assert len(updated_state["plan"].steps) == 2
        
        print("   ✅ 成功处理Master Agent结果并更新状态")
        return True
        
    except Exception as e:
        print(f"   ❌ 处理Master Agent结果测试失败: {e}")
        return False


def test_routing_after_plan_creation():
    """测试计划创建后的路由判断"""
    print("\n🧪 测试计划创建后的路由判断...")
    
    try:
        from src.graph_v2.execution_engine import should_use_execution_engine
        from src.graph_v2.builder import should_continue
        from langchain_core.messages import HumanMessage
        
        # 创建多步计划
        multi_step_plan = UnifiedPlan(
            original_task="为多个城市制作海报",
            steps=[
                UnifiedStep(
                    step_id="step1",
                    name="创建北京海报",
                    description="为北京创建海报",
                    tool_to_use="visual_expert"
                ),
                UnifiedStep(
                    step_id="step2",
                    name="创建上海海报",
                    description="为上海创建海报",
                    tool_to_use="visual_expert"
                ),
                UnifiedStep(
                    step_id="step3",
                    name="创建广州海报",
                    description="为广州创建海报",
                    tool_to_use="visual_expert"
                )
            ]
        )
        
        # 创建包含计划的状态
        state_with_plan = State(
            messages=[
                HumanMessage(content="为多个城市制作海报"),
                AIMessage(content="我已经为您创建了包含3个步骤的计划")
            ],
            plan=multi_step_plan
        )
        
        # 测试执行引擎判断
        use_engine = should_use_execution_engine(state_with_plan)
        assert use_engine == True
        
        # 测试路由判断
        route = should_continue(state_with_plan)
        assert route == "execution_engine"
        
        print("   ✅ 计划创建后正确路由到执行引擎")
        return True
        
    except Exception as e:
        print(f"   ❌ 路由判断测试失败: {e}")
        return False


def main():
    """运行所有状态更新测试"""
    print("🚀 开始测试状态更新修复\n")
    
    tests = [
        ("从result提取计划更新", test_extract_plan_updates_from_result),
        ("从工具消息提取计划更新", test_extract_plan_updates_from_tool_message),
        ("应用计划更新", test_apply_plan_update),
        ("处理Master Agent结果", test_process_master_agent_result_with_plan),
        ("计划创建后路由判断", test_routing_after_plan_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有状态更新测试通过！")
        print("\n📋 修复总结:")
        print("✅ 修复了create_plan工具结果的状态更新")
        print("✅ 支持从result和工具消息中提取计划")
        print("✅ 正确应用计划更新到状态")
        print("✅ 计划创建后正确路由到执行引擎")
        
        print("\n🚀 现在create_plan的结果应该能正确保存到state中！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
        return False


if __name__ == "__main__":
    main()

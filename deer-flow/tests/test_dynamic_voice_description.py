#!/usr/bin/env python3
"""
测试动态音色描述功能
"""

import os
import logging
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_dynamic_voice_description():
    """测试动态生成的音色描述"""
    logger.info("🎭 测试动态音色描述生成...")
    
    try:
        # 直接导入函数避免循环导入
        import sys
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src/tools/audio')
        
        from multi_speaker_tts import _generate_complete_voice_description
        from src.config.configuration import MINIMAX_SYSTEM_VOICES, MINIMAX_VOICE_PAIRS
        
        # 生成完整描述
        description = _generate_complete_voice_description()
        
        logger.info("✅ 动态音色描述生成成功")
        logger.info(f"📋 描述长度: {len(description)} 字符")
        
        # 显示生成的描述
        logger.info("📝 生成的音色描述:")
        logger.info("=" * 60)
        print(description)
        logger.info("=" * 60)
        
        # 验证描述内容
        checks = {
            "包含总数统计": f"({len(MINIMAX_SYSTEM_VOICES)}个" in description,
            "包含男声统计": "男声" in description,
            "包含女声统计": "女声" in description,
            "包含分类信息": "青年音色" in description and "专业主持人" in description,
            "包含具体音色": "presenter_male" in description and "female-yujie" in description,
            "包含推荐组合": "推荐声音组合" in description,
            "包含组合数量": len([line for line in description.split('\n') if line.startswith('• ') and ':' in line]) >= 5
        }
        
        logger.info("🔍 描述内容验证:")
        all_passed = True
        for check_name, passed in checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}: {'通过' if passed else '失败'}")
            if not passed:
                all_passed = False
        
        # 统计各分类音色数量
        categories = ["youth", "presenter", "audiobook", "premium", "child", "character", "english"]
        logger.info("📊 分类统计验证:")
        
        for category in categories:
            count = len([v for v in MINIMAX_SYSTEM_VOICES.values() if v["category"] == category])
            category_in_desc = f"({count}个)" in description
            status = "✅" if category_in_desc else "❌"
            logger.info(f"   {status} {category}: {count}个音色 {'已包含' if category_in_desc else '未包含'}")
        
        # 验证推荐组合
        logger.info("💡 推荐组合验证:")
        for pair_name, pair_info in MINIMAX_VOICE_PAIRS.items():
            pair_in_desc = pair_name in description and pair_info['male'] in description
            status = "✅" if pair_in_desc else "❌"
            logger.info(f"   {status} {pair_name}: {pair_info['male']} + {pair_info['female']}")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ 动态音色描述测试失败: {e}")
        return False

def test_tool_with_dynamic_description():
    """测试工具是否正确使用动态描述"""
    logger.info("🔧 测试工具动态描述集成...")
    
    try:
        from src.config.configuration import Configuration
        import sys
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src/tools/audio')
        
        from multi_speaker_tts import get_multi_speaker_tts_tool
        
        # 创建配置
        config = Configuration.from_runnable_config()
        
        # 获取工具
        tool = get_multi_speaker_tts_tool(config)
        
        if tool is None:
            logger.warning("⚠️ 工具未启用（可能是配置缺失）")
            return False
        
        # 检查工具描述
        tool_description = tool.description
        
        logger.info(f"✅ 工具创建成功: {tool.name}")
        logger.info(f"📋 工具描述长度: {len(tool_description)} 字符")
        
        # 验证动态内容是否包含在工具描述中
        dynamic_checks = {
            "包含完整音色库": "完整音色库" in tool_description,
            "包含具体数量": "46个" in tool_description or f"({len(MINIMAX_SYSTEM_VOICES)}个" in tool_description,
            "包含分类详情": "青年音色" in tool_description and "专业主持人" in tool_description,
            "包含推荐组合": "推荐声音组合" in tool_description,
            "包含具体音色ID": "presenter_male" in tool_description and "female-yujie" in tool_description
        }
        
        logger.info("🔍 工具描述动态内容验证:")
        all_dynamic_passed = True
        for check_name, passed in dynamic_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}: {'包含' if passed else '缺失'}")
            if not passed:
                all_dynamic_passed = False
        
        # 显示工具描述的关键部分
        if "完整音色库" in tool_description:
            lines = tool_description.split('\n')
            voice_section_start = -1
            for i, line in enumerate(lines):
                if "完整音色库" in line:
                    voice_section_start = i
                    break
            
            if voice_section_start >= 0:
                logger.info("🎭 工具描述中的音色信息:")
                for i in range(voice_section_start, min(voice_section_start + 10, len(lines))):
                    if lines[i].strip():
                        logger.info(f"   {lines[i]}")
        
        return all_dynamic_passed
        
    except Exception as e:
        logger.error(f"❌ 工具动态描述测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🎯 开始动态音色描述测试...")
    
    # 测试步骤
    tests = [
        ("动态音色描述生成", test_dynamic_voice_description),
        ("工具动态描述集成", test_tool_with_dynamic_description)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("📊 动态音色描述测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 动态音色描述完美集成！")
        logger.info("")
        logger.info("🚀 **现在AI可以看到：**")
        logger.info("   🎭 完整的46个音色列表和分类")
        logger.info("   📊 每个分类的具体音色数量")
        logger.info("   🎵 具体的音色ID和中文名称")
        logger.info("   💡 5种推荐的声音组合")
        logger.info("   🔄 动态更新的音色信息")
        logger.info("")
        logger.info("🎯 AI现在能够：")
        logger.info("   • 看到所有可用的音色选项")
        logger.info("   • 为用户推荐最合适的声音组合")
        logger.info("   • 根据场景选择最佳音色")
        logger.info("   • 了解每个音色的具体名称和特点")
    else:
        logger.warning("⚠️ 部分测试失败，需要检查配置")

if __name__ == "__main__":
    main()

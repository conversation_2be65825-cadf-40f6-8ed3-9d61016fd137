#!/usr/bin/env python3
"""
测试修复后的multi_speaker_tts工具
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_fixed_multi_speaker_tool():
    """测试修复后的multi_speaker_tts工具"""
    
    print("🔧 测试修复后的multi_speaker_tts工具...")
    
    try:
        from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
        from src.config.configuration import Configuration
        
        config = Configuration()
        tool = get_multi_speaker_tts_tool(config)
        
        print(f"✅ 工具创建成功: {tool.name if tool else None}")
        print(f"📋 工具类型: {type(tool)}")
        
        if tool:
            print(f"📋 有func: {hasattr(tool, 'func')}")
            print(f"📋 有coroutine: {hasattr(tool, 'coroutine')}")
            print(f"📋 工具名称: {tool.name}")
            print(f"📋 描述长度: {len(tool.description)} 字符")
            
            # 测试func调用（应该返回异步消息）
            try:
                sync_result = tool.func()
                print(f"📋 同步调用结果: {sync_result}")
            except Exception as e:
                print(f"⚠️ 同步调用错误: {e}")
            
            return True
        else:
            print(f"❌ 工具创建失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_tools_integration():
    """测试audio_tools集成"""
    
    print(f"\n🔧 测试audio_tools集成...")
    
    try:
        from src.tools import audio_tools
        
        print(f"✅ audio_tools导入成功")
        print(f"📋 工具数量: {len(audio_tools)}")
        
        tool_names = []
        for i, tool in enumerate(audio_tools):
            if tool is not None:
                name = getattr(tool, 'name', f'Tool_{i}')
                tool_names.append(name)
                print(f"   {i+1}. {name}")
            else:
                print(f"   {i+1}. None")
        
        has_multi_tts = 'multi_speaker_tts' in tool_names
        print(f"\n📋 multi_speaker_tts在audio_tools中: {'✅ 是' if has_multi_tts else '❌ 否'}")
        
        return has_multi_tts
        
    except Exception as e:
        print(f"❌ audio_tools集成测试失败: {e}")
        return False

def test_agent_integration():
    """测试Agent集成"""
    
    print(f"\n🔧 测试Agent集成...")
    
    try:
        from src.agents.agents import audio_creator_agent
        
        print(f"✅ Audio Agent导入成功")
        
        # 获取Agent的图定义
        graph_def = audio_creator_agent.get_graph()
        
        # 查找tools节点
        tools_node = None
        for node_id, node in graph_def.nodes.items():
            if node_id == 'tools':
                tools_node = node
                break
        
        if tools_node and hasattr(tools_node.data, 'tools_by_name'):
            tools_by_name = tools_node.data.tools_by_name
            
            print(f"📋 Agent工具数量: {len(tools_by_name)}")
            
            tool_names = list(tools_by_name.keys())
            for i, name in enumerate(tool_names):
                print(f"   {i+1}. {name}")
            
            has_multi_tts = 'multi_speaker_tts' in tools_by_name
            print(f"\n📋 multi_speaker_tts在Agent中: {'✅ 是' if has_multi_tts else '❌ 否'}")
            
            return has_multi_tts
        else:
            print(f"❌ 无法获取Agent工具信息")
            return False
        
    except Exception as e:
        print(f"❌ Agent集成测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 测试修复后的multi_speaker_tts工具...")
    
    tests = [
        ("工具创建", test_fixed_multi_speaker_tool),
        ("audio_tools集成", test_audio_tools_integration),
        ("Agent集成", test_agent_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print(f"{'='*50}")
        
        result = test_func()
        results.append((test_name, result))
        print(f"📊 {test_name}: {'✅ 成功' if result else '❌ 失败'}")
    
    print(f"\n{'='*50}")
    print(f"📊 测试总结")
    print(f"{'='*50}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print(f"🎉 所有测试通过！multi_speaker_tts工具修复成功！")
    else:
        print(f"⚠️ 还有问题需要解决")

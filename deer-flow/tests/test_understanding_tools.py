#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多模态理解工具集成测试

测试新开发的图片和视频理解工具在DeerFlow系统中的集成情况。
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.configuration import Configuration
from src.tools.understanding import (
    get_multimodal_understanding_tool,
    get_image_understanding_tool,
    get_video_understanding_tool
)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_tool_creation():
    """测试工具创建"""
    print("🔧 测试工具创建...")
    
    # 创建配置
    config = Configuration.from_runnable_config()
    
    try:
        # 创建工具实例
        multimodal_tool = get_multimodal_understanding_tool(config)
        image_tool = get_image_understanding_tool(config)
        video_tool = get_video_understanding_tool(config)
        
        print(f"✅ 多模态理解工具: {multimodal_tool.name}")
        print(f"✅ 图片理解工具: {image_tool.name}")
        print(f"✅ 视频理解工具: {video_tool.name}")
        
        return True
    except Exception as e:
        print(f"❌ 工具创建失败: {e}")
        return False

def test_tools_in_registry():
    """测试工具是否正确注册到系统中"""
    print("\n📋 测试工具注册...")
    
    try:
        from src.tools import (
            understanding_tools,
            multimodal_understanding_tool,
            image_understanding_tool,
            video_understanding_tool,
            ALL_TOOLS_LIST,
            TOOL_MAP
        )
        
        print(f"✅ understanding_tools 列表包含 {len(understanding_tools)} 个工具")
        print(f"✅ 多模态工具已注册: {multimodal_understanding_tool.name}")
        print(f"✅ 图片工具已注册: {image_understanding_tool.name}")
        print(f"✅ 视频工具已注册: {video_understanding_tool.name}")
        
        # 检查工具是否在全局工具列表中
        understanding_tool_names = {tool.name for tool in understanding_tools}
        all_tool_names = {tool.name for tool in ALL_TOOLS_LIST if hasattr(tool, 'name')}
        
        if understanding_tool_names.issubset(all_tool_names):
            print("✅ 所有理解工具都已添加到全局工具列表")
        else:
            missing = understanding_tool_names - all_tool_names
            print(f"⚠️  缺失的工具: {missing}")
        
        # 检查TOOL_MAP
        for tool_name in understanding_tool_names:
            if tool_name in TOOL_MAP:
                print(f"✅ {tool_name} 已添加到TOOL_MAP")
            else:
                print(f"❌ {tool_name} 未在TOOL_MAP中找到")
        
        return True
    except Exception as e:
        print(f"❌ 工具注册检查失败: {e}")
        return False

def test_image_analysis():
    """测试图片分析功能"""
    print("\n🖼️  测试图片分析功能...")
    
    try:
        config = Configuration.from_runnable_config()
        tool = get_image_understanding_tool(config)
        
        # 测试图片URL
        test_image = "https://upload.wikimedia.org/wikipedia/commons/thumb/3/3a/Cat03.jpg/1200px-Cat03.jpg"
        
        result = tool.invoke({
            "image_url": test_image,
            "question": "简要描述这张图片的主要内容",
            "analysis_type": "general",
            "detail_level": "brief"
        })
        
        print("✅ 图片分析成功")
        print(f"📄 结果预览: {result[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ 图片分析失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🦌 DeerFlow 多模态理解工具集成测试")
    print("=" * 50)
    
    setup_logging()
    
    # 运行测试
    tests = [
        ("工具创建", test_tool_creation),
        ("工具注册", test_tools_in_registry),
        ("图片分析", test_image_analysis),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！多模态理解工具已成功集成到DeerFlow系统中。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置和代码。")
        return 1

if __name__ == "__main__":
    exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试改进后的规划提示词

验证新的提示词是否能生成更好的计划。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.tools.planning import create_plan, initialize_planning_tools
from src.llms.llm import get_llm_by_type


def test_city_poster_planning():
    """测试城市海报规划"""
    print("🧪 测试城市海报规划...")

    try:
        # 初始化规划工具
        llm = get_llm_by_type("default")  # 使用默认LLM
        initialize_planning_tools(llm)
        
        # 测试城市海报任务
        task = "为北京、上海、广州、深圳、成都这5个热门城市制作旅游海报，每个海报都要体现城市特色"
        
        result = create_plan.func(task)
        
        assert "plan" in result
        plan = result["plan"]
        
        # 验证计划质量
        assert len(plan.steps) == 5  # 应该有5个步骤
        
        # 检查步骤名称是否具体
        step_descriptions = [step.description for step in plan.steps]
        
        # 应该包含城市名称
        cities = ["北京", "上海", "广州", "深圳", "成都"]
        for city in cities:
            found_city = any(city in desc for desc in step_descriptions)
            assert found_city, f"计划中未找到城市: {city}"
        
        # 检查是否都使用visual_expert
        for step in plan.steps:
            assert step.tool_to_use == "visual_expert"
        
        # 检查步骤描述是否具体（不应该是"步骤1"这样的通用名称）
        for step in plan.steps:
            assert "步骤" not in step.description or len(step.description) > 10
        
        print(f"   ✅ 生成了{len(plan.steps)}个步骤的计划")
        for i, step in enumerate(plan.steps, 1):
            print(f"   {i}. {step.description[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 城市海报规划测试失败: {e}")
        return False


def test_multimedia_project_planning():
    """测试多媒体项目规划"""
    print("\n🧪 测试多媒体项目规划...")
    
    try:
        # 测试视频制作任务
        task = "制作一个关于哪吒的3分钟宣传视频，包含角色介绍、故事背景和精彩片段"
        
        result = create_plan.func(task)
        
        assert "plan" in result
        plan = result["plan"]
        
        # 验证计划结构
        assert len(plan.steps) >= 2  # 至少应该有视觉和音频步骤
        
        # 检查工具使用的多样性
        tools_used = set(step.tool_to_use for step in plan.steps)
        
        # 多媒体项目应该使用多种工具
        expected_tools = {"visual_expert", "audio_expert", "video_expert"}
        found_tools = tools_used.intersection(expected_tools)
        assert len(found_tools) >= 2, f"多媒体项目应该使用多种工具，实际使用: {tools_used}"
        
        # 检查依赖关系
        has_dependencies = any(step.dependencies for step in plan.steps)
        assert has_dependencies, "多媒体项目应该有步骤依赖关系"
        
        # 检查是否包含"哪吒"
        descriptions = " ".join(step.description for step in plan.steps)
        assert "哪吒" in descriptions, "计划中应该包含角色名称"
        
        print(f"   ✅ 生成了{len(plan.steps)}个步骤的多媒体计划")
        print(f"   使用的工具: {', '.join(tools_used)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 多媒体项目规划测试失败: {e}")
        return False


def test_audio_series_planning():
    """测试音频系列规划"""
    print("\n🧪 测试音频系列规划...")
    
    try:
        # 测试音频系列任务
        task = "为北京、上海、广州三个城市分别制作语音介绍，每个介绍包含历史文化和现代发展"
        
        result = create_plan.func(task)
        
        assert "plan" in result
        plan = result["plan"]
        
        # 验证计划质量
        assert len(plan.steps) == 3  # 应该有3个步骤
        
        # 检查是否都使用audio_expert
        for step in plan.steps:
            assert step.tool_to_use == "audio_expert"
        
        # 检查城市名称
        cities = ["北京", "上海", "广州"]
        step_descriptions = [step.description for step in plan.steps]
        
        for city in cities:
            found_city = any(city in desc for desc in step_descriptions)
            assert found_city, f"计划中未找到城市: {city}"
        
        print(f"   ✅ 生成了{len(plan.steps)}个步骤的音频系列计划")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 音频系列规划测试失败: {e}")
        return False


def test_plan_quality_metrics():
    """测试计划质量指标"""
    print("\n🧪 测试计划质量指标...")
    
    try:
        # 测试复杂任务
        task = "创建一个完整的品牌设计方案，包括Logo设计、宣传海报和品牌视频，主题是环保科技公司"
        
        result = create_plan.func(task)
        
        assert "plan" in result
        plan = result["plan"]
        
        # 质量指标1：步骤数量合理
        assert 2 <= len(plan.steps) <= 10, f"步骤数量应该在2-10之间，实际: {len(plan.steps)}"
        
        # 质量指标2：描述具体性
        avg_description_length = sum(len(step.description) for step in plan.steps) / len(plan.steps)
        assert avg_description_length >= 20, f"步骤描述平均长度应该>=20字符，实际: {avg_description_length:.1f}"
        
        # 质量指标3：工具选择合理性
        tools_used = [step.tool_to_use for step in plan.steps]
        valid_tools = {"visual_expert", "audio_expert", "video_expert"}
        assert all(tool in valid_tools for tool in tools_used), f"使用了无效工具: {tools_used}"
        
        # 质量指标4：任务覆盖完整性
        descriptions = " ".join(step.description for step in plan.steps)
        key_elements = ["Logo", "海报", "视频", "环保", "科技"]
        found_elements = [elem for elem in key_elements if elem in descriptions]
        assert len(found_elements) >= 3, f"计划应该覆盖更多关键元素，找到: {found_elements}"
        
        print(f"   ✅ 计划质量指标验证通过")
        print(f"   步骤数量: {len(plan.steps)}")
        print(f"   平均描述长度: {avg_description_length:.1f}字符")
        print(f"   覆盖的关键元素: {found_elements}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 计划质量指标测试失败: {e}")
        return False


def main():
    """运行所有规划提示词测试"""
    print("🚀 开始测试改进后的规划提示词\n")
    
    tests = [
        ("城市海报规划", test_city_poster_planning),
        ("多媒体项目规划", test_multimedia_project_planning),
        ("音频系列规划", test_audio_series_planning),
        ("计划质量指标", test_plan_quality_metrics),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有规划提示词测试通过！")
        print("\n📋 改进效果：")
        print("✅ 步骤名称更具体和描述性")
        print("✅ 工具选择更合理")
        print("✅ 任务覆盖更完整")
        print("✅ 依赖关系更清晰")
        
        print("\n🚀 现在应该能生成高质量的执行计划了！")
        return True
    else:
        print("⚠️ 部分测试失败，提示词可能需要进一步优化。")
        return False


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试统一Plan模型

验证新的UnifiedPlan模型的功能和迁移工具。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep


def test_unified_step_creation():
    """测试UnifiedStep创建和基本功能"""
    print("🧪 测试UnifiedStep创建...")
    
    try:
        step = UnifiedStep(
            step_id="collect_materials",
            name="收集素材",
            description="收集哪吒相关的图片和视频素材",
            tool_to_use="visual_expert",
            inputs={"character": "哪吒", "style": "可爱"}
        )
        
        # 检查基本属性
        assert step.step_id == "collect_materials"
        assert step.name == "收集素材"
        assert step.status == "pending"
        assert step.max_retries == 3
        assert step.retry_count == 0
        
        print("✅ UnifiedStep创建成功")
        return True
        
    except Exception as e:
        print(f"❌ UnifiedStep创建失败: {e}")
        return False


def test_unified_step_execution_flow():
    """测试UnifiedStep执行流程"""
    print("\n🧪 测试UnifiedStep执行流程...")
    
    try:
        step = UnifiedStep(
            step_id="generate_image",
            name="生成图像",
            description="生成哪吒形象图像",
            tool_to_use="visual_expert"
        )
        
        # 测试执行流程
        assert step.status == "pending"
        
        # 标记开始执行
        step.mark_started()
        assert step.status == "in_progress"
        assert step.started_at is not None
        
        # 标记完成
        result = {"image_url": "https://example.com/nezha.jpg", "success": True}
        step.mark_completed(result)
        assert step.status == "completed"
        assert step.completed_at is not None
        assert step.result == result
        
        # 检查执行时长
        duration = step.get_execution_duration()
        assert duration is not None and duration >= 0
        
        print("✅ UnifiedStep执行流程正确")
        return True
        
    except Exception as e:
        print(f"❌ UnifiedStep执行流程测试失败: {e}")
        return False


def test_unified_step_retry_logic():
    """测试UnifiedStep重试逻辑"""
    print("\n🧪 测试UnifiedStep重试逻辑...")
    
    try:
        step = UnifiedStep(
            step_id="test_retry",
            name="测试重试",
            description="测试重试功能",
            tool_to_use="test_tool",
            max_retries=2
        )
        
        # 第一次失败
        step.mark_failed({"error": "网络错误"})
        assert step.status == "failed"
        assert step.should_retry() == True
        
        # 重试
        step.reset_for_retry()
        assert step.status == "pending"
        assert step.retry_count == 1
        
        # 第二次失败
        step.mark_failed({"error": "网络错误"})
        assert step.should_retry() == True
        
        # 再次重试
        step.reset_for_retry()
        assert step.retry_count == 2
        
        # 第三次失败（超过最大重试次数）
        step.mark_failed({"error": "网络错误"})
        assert step.should_retry() == False
        
        print("✅ UnifiedStep重试逻辑正确")
        return True
        
    except Exception as e:
        print(f"❌ UnifiedStep重试逻辑测试失败: {e}")
        return False


def test_unified_plan_creation():
    """测试UnifiedPlan创建和基本功能"""
    print("\n🧪 测试UnifiedPlan创建...")
    
    try:
        # 创建步骤
        steps = [
            UnifiedStep(
                step_id="collect_materials",
                name="收集素材",
                description="收集哪吒相关素材",
                tool_to_use="visual_expert"
            ),
            UnifiedStep(
                step_id="generate_video",
                name="生成视频",
                description="制作鬼畜视频",
                tool_to_use="video_expert",
                dependencies=["collect_materials"]
            )
        ]
        
        # 创建计划
        plan = UnifiedPlan(
            original_task="制作哪吒鬼畜视频",
            steps=steps,
            template_id="ai_parody_video",
            is_from_template=True
        )
        
        # 检查基本属性
        assert plan.original_task == "制作哪吒鬼畜视频"
        assert len(plan.steps) == 2
        assert plan.template_id == "ai_parody_video"
        assert plan.is_from_template == True
        assert plan.plan_id is not None
        
        print("✅ UnifiedPlan创建成功")
        return True
        
    except Exception as e:
        print(f"❌ UnifiedPlan创建失败: {e}")
        return False


def test_unified_plan_execution_logic():
    """测试UnifiedPlan执行逻辑"""
    print("\n🧪 测试UnifiedPlan执行逻辑...")
    
    try:
        # 创建有依赖关系的步骤
        steps = [
            UnifiedStep(
                step_id="step1",
                name="步骤1",
                description="第一个步骤",
                tool_to_use="tool1"
            ),
            UnifiedStep(
                step_id="step2",
                name="步骤2", 
                description="第二个步骤",
                tool_to_use="tool2",
                dependencies=["step1"]
            ),
            UnifiedStep(
                step_id="step3",
                name="步骤3",
                description="第三个步骤",
                tool_to_use="tool3",
                dependencies=["step1"]
            )
        ]
        
        plan = UnifiedPlan(
            original_task="测试执行逻辑",
            steps=steps
        )
        
        # 初始状态：只有step1可执行
        executable_steps = plan.get_next_executable_steps()
        assert len(executable_steps) == 1
        assert executable_steps[0].step_id == "step1"
        
        # 完成step1
        step1 = plan.get_step("step1")
        step1.mark_completed({"result": "step1完成"})
        
        # 现在step2和step3都可执行
        executable_steps = plan.get_next_executable_steps()
        assert len(executable_steps) == 2
        step_ids = {s.step_id for s in executable_steps}
        assert step_ids == {"step2", "step3"}
        
        # 完成所有步骤
        plan.get_step("step2").mark_completed({"result": "step2完成"})
        plan.get_step("step3").mark_completed({"result": "step3完成"})
        
        # 检查计划完成状态
        assert plan.is_complete() == True
        
        # 检查进度信息
        progress = plan.get_progress_info()
        assert progress["total_steps"] == 3
        assert progress["completed_steps"] == 3
        assert progress["progress_percentage"] == 100.0
        
        print("✅ UnifiedPlan执行逻辑正确")
        return True
        
    except Exception as e:
        print(f"❌ UnifiedPlan执行逻辑测试失败: {e}")
        return False


def test_unified_plan_progress_tracking():
    """测试UnifiedPlan进度跟踪"""
    print("\n🧪 测试UnifiedPlan进度跟踪...")
    
    try:
        steps = [
            UnifiedStep(step_id="s1", name="步骤1", description="描述1", tool_to_use="tool1"),
            UnifiedStep(step_id="s2", name="步骤2", description="描述2", tool_to_use="tool2"),
            UnifiedStep(step_id="s3", name="步骤3", description="描述3", tool_to_use="tool3")
        ]
        
        plan = UnifiedPlan(original_task="测试进度", steps=steps)
        
        # 初始进度
        progress = plan.get_progress_info()
        assert progress["completed_steps"] == 0
        assert progress["progress_percentage"] == 0.0
        
        # 完成一个步骤
        plan.get_step("s1").mark_completed({"result": "完成"})
        progress = plan.get_progress_info()
        assert progress["completed_steps"] == 1
        expected_percentage = 100.0 / 3  # 33.33...
        assert abs(progress["progress_percentage"] - expected_percentage) < 0.1
        
        # 一个步骤失败
        failed_step = plan.get_step("s2")
        failed_step.mark_failed({"error": "失败"})
        progress = plan.get_progress_info()
        assert progress["failed_steps"] == 1

        # 让步骤彻底失败（超过重试次数）
        failed_step.retry_count = failed_step.max_retries  # 设置为最大重试次数

        # 获取执行摘要
        summary = plan.get_execution_summary()
        # 由于步骤还可以重试，所以不会显示"失败"，而是显示"进行中"
        assert "进行中" in summary or "失败" in summary
        
        print("✅ UnifiedPlan进度跟踪正确")
        return True
        
    except Exception as e:
        print(f"❌ UnifiedPlan进度跟踪测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 开始测试统一Plan模型\n")
    
    tests = [
        ("UnifiedStep创建", test_unified_step_creation),
        ("UnifiedStep执行流程", test_unified_step_execution_flow),
        ("UnifiedStep重试逻辑", test_unified_step_retry_logic),
        ("UnifiedPlan创建", test_unified_plan_creation),
        ("UnifiedPlan执行逻辑", test_unified_plan_execution_logic),
        ("UnifiedPlan进度跟踪", test_unified_plan_progress_tracking),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！统一Plan模型工作正常。")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
        return False


if __name__ == "__main__":
    main()

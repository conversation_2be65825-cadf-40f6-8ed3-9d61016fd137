#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的状态管理工具测试

避开循环导入问题，直接测试核心功能。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
from datetime import datetime
from langchain_core.tools import tool
from langchain_core.messages import AIMessage, HumanMessage


# 直接定义测试用的工具（避开导入问题）
@tool
def test_report_step_completion(step_id: str, status: str, result_summary: str) -> str:
    """测试版本的步骤完成汇报工具"""
    update_instruction = {
        "action": "update_step_status",
        "step_id": step_id,
        "status": status,
        "result_summary": result_summary,
        "timestamp": datetime.now().isoformat()
    }
    return f"✅ 步骤状态已汇报: {json.dumps(update_instruction, ensure_ascii=False)}"


def test_tool_invocation():
    """测试工具调用是否正常"""
    print("🧪 测试工具调用...")
    
    try:
        # 测试工具调用
        result = test_report_step_completion.invoke({
            "step_id": "step_1",
            "status": "completed",
            "result_summary": "成功生成了图像"
        })
        
        print(f"工具调用结果: {result}")
        
        # 检查结果是否包含预期内容
        if "步骤状态已汇报" in result and "step_1" in result:
            print("✅ 工具调用成功")
            return True
        else:
            print("❌ 工具调用结果不正确")
            return False
            
    except Exception as e:
        print(f"❌ 工具调用失败: {e}")
        return False


def test_json_parsing():
    """测试JSON解析功能"""
    print("\n🧪 测试JSON解析...")
    
    try:
        # 模拟工具返回的JSON字符串
        json_str = '{"action": "update_step_status", "step_id": "step_1", "status": "completed", "result_summary": "成功生成了图像"}'
        
        # 解析JSON
        data = json.loads(json_str)
        
        # 检查解析结果
        if (data.get("action") == "update_step_status" and
            data.get("step_id") == "step_1" and
            data.get("status") == "completed"):
            print("✅ JSON解析成功")
            return True
        else:
            print("❌ JSON解析结果不正确")
            return False
            
    except Exception as e:
        print(f"❌ JSON解析失败: {e}")
        return False


def test_message_tool_calls():
    """测试消息中的工具调用检测"""
    print("\n🧪 测试工具调用检测...")
    
    try:
        # 模拟AIMessage with tool_calls
        message = AIMessage(
            content="我已经完成了任务",
            tool_calls=[
                {
                    "id": "call_1",
                    "name": "report_step_completion",
                    "args": {
                        "step_id": "step_1",
                        "status": "completed",
                        "result_summary": "任务完成"
                    }
                }
            ]
        )
        
        # 检测工具调用
        found_report = False
        if hasattr(message, 'tool_calls') and message.tool_calls:
            for tool_call in message.tool_calls:
                if tool_call.get("name") == "report_step_completion":
                    found_report = True
                    args = tool_call.get("args", {})
                    if args.get("step_id") == "step_1":
                        print("✅ 工具调用检测成功")
                        return True
        
        if not found_report:
            print("❌ 未检测到工具调用")
        return False
        
    except Exception as e:
        print(f"❌ 工具调用检测失败: {e}")
        return False


def test_state_update_logic():
    """测试状态更新逻辑"""
    print("\n🧪 测试状态更新逻辑...")
    
    try:
        # 模拟简单的步骤对象
        class MockStep:
            def __init__(self, step_id, status="pending"):
                self.step_id = step_id
                self.status = status
                self.result = None
                self.completed_at = None
        
        # 创建测试步骤
        step = MockStep("step_1", "pending")
        
        # 模拟状态更新
        update = {
            "step_id": "step_1",
            "status": "completed",
            "result_summary": "任务完成",
            "timestamp": datetime.now().isoformat()
        }
        
        # 应用更新
        if step.step_id == update["step_id"]:
            step.status = update["status"]
            step.result = {
                "summary": update["result_summary"],
                "timestamp": update["timestamp"]
            }
            step.completed_at = datetime.now()
        
        # 检查更新结果
        if (step.status == "completed" and
            step.result and
            step.result["summary"] == "任务完成"):
            print("✅ 状态更新逻辑正确")
            return True
        else:
            print("❌ 状态更新逻辑错误")
            return False
            
    except Exception as e:
        print(f"❌ 状态更新逻辑测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 开始测试状态管理核心功能\n")
    
    tests = [
        ("工具调用", test_tool_invocation),
        ("JSON解析", test_json_parsing),
        ("工具调用检测", test_message_tool_calls),
        ("状态更新逻辑", test_state_update_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*40}")
        print(f"测试: {test_name}")
        print('='*40)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*40}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*40)
    
    if passed == total:
        print("🎉 所有核心功能测试通过！")
        print("✅ 新的状态管理机制基础功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
        return False


if __name__ == "__main__":
    main()

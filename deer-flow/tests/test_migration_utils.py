#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试迁移工具

验证从Legacy Plan和Enhanced Plan迁移到Unified Plan的功能。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
from src.graph_v2.migration_utils import (
    migrate_dict_to_unified,
    migrate_step_dict_to_unified,
    auto_migrate_plan,
    ensure_unified_plan
)


def test_dict_to_unified_migration():
    """测试从字典迁移到UnifiedPlan"""
    print("🧪 测试字典迁移...")
    
    try:
        # 模拟Legacy Plan字典
        legacy_dict = {
            "original_task": "制作视频",
            "steps": [
                {
                    "step_id": 1,
                    "description": "收集素材",
                    "tool_to_use": "visual_expert",
                    "inputs": {"character": "哪吒"},
                    "status": "pending",
                    "dependencies": []
                },
                {
                    "step_id": 2,
                    "description": "生成视频",
                    "tool_to_use": "video_expert",
                    "inputs": {"style": "鬼畜"},
                    "status": "pending",
                    "dependencies": [1]
                }
            ]
        }
        
        # 迁移
        unified_plan = migrate_dict_to_unified(legacy_dict)
        
        # 验证
        assert unified_plan.original_task == "制作视频"
        assert len(unified_plan.steps) == 2
        
        # 检查步骤ID转换
        step1 = unified_plan.get_step("step_1")
        step2 = unified_plan.get_step("step_2")
        
        assert step1 is not None
        assert step2 is not None
        assert step1.description == "收集素材"
        assert step2.description == "生成视频"
        assert step2.dependencies == ["step_1"]
        
        print("✅ 字典迁移成功")
        return True
        
    except Exception as e:
        print(f"❌ 字典迁移失败: {e}")
        return False


def test_enhanced_dict_migration():
    """测试Enhanced Plan字典迁移"""
    print("\n🧪 测试Enhanced Plan字典迁移...")
    
    try:
        # 模拟Enhanced Plan字典
        enhanced_dict = {
            "plan_id": "test_plan_123",
            "original_task": "AI鬼畜视频制作",
            "source_template": "ai_parody_video",
            "template_params": {"character": "哪吒"},
            "is_from_template": True,
            "steps": [
                {
                    "step_id": "collect_materials",
                    "name": "收集素材",
                    "description": "收集哪吒相关素材",
                    "tool_to_use": "visual_expert",
                    "status": "completed",
                    "dependencies": []
                },
                {
                    "step_id": "generate_video",
                    "name": "生成视频",
                    "description": "制作鬼畜视频",
                    "tool_to_use": "video_expert",
                    "status": "pending",
                    "dependencies": ["collect_materials"]
                }
            ]
        }
        
        # 迁移
        unified_plan = migrate_dict_to_unified(enhanced_dict)
        
        # 验证
        assert unified_plan.plan_id == "test_plan_123"
        assert unified_plan.original_task == "AI鬼畜视频制作"
        assert unified_plan.template_id == "ai_parody_video"
        assert unified_plan.is_from_template == True
        assert len(unified_plan.steps) == 2
        
        # 检查步骤
        step1 = unified_plan.get_step("collect_materials")
        step2 = unified_plan.get_step("generate_video")
        
        assert step1.status == "completed"
        assert step2.status == "pending"
        assert step2.dependencies == ["collect_materials"]
        
        print("✅ Enhanced Plan字典迁移成功")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced Plan字典迁移失败: {e}")
        return False


def test_auto_migration():
    """测试自动迁移功能"""
    print("\n🧪 测试自动迁移...")
    
    try:
        # 测试UnifiedPlan直接返回
        original_plan = UnifiedPlan(
            original_task="测试任务",
            steps=[
                UnifiedStep(
                    step_id="test_step",
                    name="测试步骤",
                    description="测试描述",
                    tool_to_use="test_tool"
                )
            ]
        )
        
        migrated_plan = auto_migrate_plan(original_plan)
        assert migrated_plan is original_plan  # 应该是同一个对象
        
        # 测试字典迁移
        dict_plan = {
            "original_task": "字典任务",
            "steps": [
                {
                    "step_id": "dict_step",
                    "description": "字典步骤",
                    "tool_to_use": "dict_tool"
                }
            ]
        }
        
        migrated_dict_plan = auto_migrate_plan(dict_plan)
        assert isinstance(migrated_dict_plan, UnifiedPlan)
        assert migrated_dict_plan.original_task == "字典任务"
        
        print("✅ 自动迁移成功")
        return True
        
    except Exception as e:
        print(f"❌ 自动迁移失败: {e}")
        return False


def test_ensure_unified_plan():
    """测试ensure_unified_plan函数"""
    print("\n🧪 测试ensure_unified_plan...")
    
    try:
        # 测试正常情况
        plan_dict = {
            "original_task": "确保统一",
            "steps": [
                {
                    "step_id": "ensure_step",
                    "description": "确保步骤",
                    "tool_to_use": "ensure_tool"
                }
            ]
        }
        
        unified_plan = ensure_unified_plan(plan_dict)
        assert isinstance(unified_plan, UnifiedPlan)
        assert unified_plan.original_task == "确保统一"
        
        # 测试异常情况
        invalid_plan = "这不是一个有效的计划"
        fallback_plan = ensure_unified_plan(invalid_plan)
        assert isinstance(fallback_plan, UnifiedPlan)
        assert fallback_plan.original_task == "迁移失败的计划"
        assert len(fallback_plan.steps) == 0
        
        print("✅ ensure_unified_plan成功")
        return True
        
    except Exception as e:
        print(f"❌ ensure_unified_plan失败: {e}")
        return False


def test_step_dict_migration():
    """测试步骤字典迁移"""
    print("\n🧪 测试步骤字典迁移...")
    
    try:
        # 测试数字ID步骤
        step_dict_1 = {
            "step_id": 1,
            "description": "数字ID步骤",
            "tool_to_use": "test_tool",
            "dependencies": [0],
            "status": "completed"
        }
        
        unified_step_1 = migrate_step_dict_to_unified(step_dict_1, 0)
        assert unified_step_1.step_id == "step_1"
        assert unified_step_1.dependencies == ["step_0"]
        
        # 测试字符串ID步骤
        step_dict_2 = {
            "step_id": "string_step",
            "name": "字符串步骤",
            "description": "字符串ID步骤",
            "tool_to_use": "test_tool",
            "dependencies": ["other_step"],
            "max_retries": 5
        }
        
        unified_step_2 = migrate_step_dict_to_unified(step_dict_2, 1)
        assert unified_step_2.step_id == "string_step"
        assert unified_step_2.name == "字符串步骤"
        assert unified_step_2.dependencies == ["other_step"]
        assert unified_step_2.max_retries == 5
        
        print("✅ 步骤字典迁移成功")
        return True
        
    except Exception as e:
        print(f"❌ 步骤字典迁移失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 开始测试迁移工具\n")
    
    tests = [
        ("字典迁移", test_dict_to_unified_migration),
        ("Enhanced Plan字典迁移", test_enhanced_dict_migration),
        ("自动迁移", test_auto_migration),
        ("ensure_unified_plan", test_ensure_unified_plan),
        ("步骤字典迁移", test_step_dict_migration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有迁移测试通过！迁移工具工作正常。")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
        return False


if __name__ == "__main__":
    main()

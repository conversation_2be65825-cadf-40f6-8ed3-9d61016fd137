#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的状态管理机制

这个测试脚本验证修复后的状态管理工具是否能正常工作。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
from datetime import datetime
from langchain_core.messages import AIMessage, HumanMessage

# 导入新的状态管理工具
from src.tools.state_management import (
    report_step_completion,
    request_current_step_info,
    request_plan_progress,
    new_state_management_tools
)

# 导入状态处理模块
from src.graph_v2.state_handler import (
    extract_status_updates,
    apply_status_update,
    get_current_step_info,
    build_step_instruction
)

# 导入模型
from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
from src.graph_v2.types import State


def test_new_tools():
    """测试新的状态管理工具是否可以正常调用"""
    print("🧪 测试新的状态管理工具...")
    
    try:
        # 测试report_step_completion工具
        result1 = report_step_completion.invoke({
            "step_id": "step_1",
            "status": "completed",
            "result_summary": "成功生成了图像"
        })
        print(f"✅ report_step_completion: {result1}")
        
        # 测试request_current_step_info工具
        result2 = request_current_step_info.invoke({})
        print(f"✅ request_current_step_info: {result2}")
        
        # 测试request_plan_progress工具
        result3 = request_plan_progress.invoke({})
        print(f"✅ request_plan_progress: {result3}")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具测试失败: {e}")
        return False


def test_status_update_extraction():
    """测试状态更新提取功能"""
    print("\n🧪 测试状态更新提取...")
    
    try:
        # 模拟Master Agent的工具调用结果
        mock_result = {
            "messages": [
                AIMessage(
                    content="我已经完成了图像生成任务",
                    tool_calls=[
                        {
                            "name": "report_step_completion",
                            "args": {
                                "step_id": "step_1",
                                "status": "completed",
                                "result_summary": "成功生成了哪吒形象图像"
                            }
                        }
                    ]
                )
            ]
        }
        
        # 提取状态更新
        updates = extract_status_updates(mock_result)
        print(f"✅ 提取到状态更新: {updates}")
        
        if len(updates) == 1:
            update = updates[0]
            if (update["step_id"] == "step_1" and 
                update["status"] == "completed" and
                "哪吒" in update["result_summary"]):
                print("✅ 状态更新提取正确")
                return True
        
        print("❌ 状态更新提取结果不正确")
        return False
        
    except Exception as e:
        print(f"❌ 状态更新提取测试失败: {e}")
        return False


def test_state_update_application():
    """测试状态更新应用功能"""
    print("\n🧪 测试状态更新应用...")
    
    try:
        # 创建测试计划
        test_step = EnhancedStep(
            step_id="step_1",
            name="生成图像",
            description="生成哪吒形象图像",
            tool_to_use="visual_expert",
            status="pending"
        )
        
        test_plan = EnhancedPlan(
            plan_id="test_plan",
            original_task="制作哪吒视频",
            steps=[test_step]
        )
        
        # 创建测试状态
        test_state = {
            "plan": test_plan,
            "messages": []
        }
        
        # 应用状态更新
        update = {
            "step_id": "step_1",
            "status": "completed",
            "result_summary": "成功生成了哪吒形象图像",
            "timestamp": datetime.now().isoformat()
        }
        
        success = apply_status_update(test_state, update)
        
        if success:
            # 检查状态是否正确更新
            updated_step = test_plan.get_step("step_1")
            if updated_step.status == "completed":
                print("✅ 状态更新应用成功")
                return True
            else:
                print(f"❌ 步骤状态未正确更新: {updated_step.status}")
        else:
            print("❌ 状态更新应用失败")
        
        return False
        
    except Exception as e:
        print(f"❌ 状态更新应用测试失败: {e}")
        return False


def test_step_instruction_building():
    """测试步骤指令构建功能"""
    print("\n🧪 测试步骤指令构建...")
    
    try:
        step_info = {
            "step_id": "step_1",
            "name": "生成图像",
            "description": "生成哪吒形象图像，要求可爱风格",
            "tool_to_use": "visual_expert"
        }
        
        instruction = build_step_instruction(step_info)
        
        # 检查指令是否包含必要信息
        if (step_info["step_id"] in instruction and
            step_info["description"] in instruction and
            step_info["tool_to_use"] in instruction and
            "report_step_completion" in instruction):
            print("✅ 步骤指令构建正确")
            print(f"指令内容:\n{instruction}")
            return True
        else:
            print("❌ 步骤指令缺少必要信息")
            return False
            
    except Exception as e:
        print(f"❌ 步骤指令构建测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 开始测试新的状态管理机制\n")
    
    tests = [
        ("新工具调用", test_new_tools),
        ("状态更新提取", test_status_update_extraction),
        ("状态更新应用", test_state_update_application),
        ("步骤指令构建", test_step_instruction_building),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！新的状态管理机制工作正常。")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
        return False


if __name__ == "__main__":
    main()

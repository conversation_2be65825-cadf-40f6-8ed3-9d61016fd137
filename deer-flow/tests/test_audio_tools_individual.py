#!/usr/bin/env python3
"""
逐个测试音频工具导入
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_individual_audio_tools():
    """逐个测试音频工具导入"""
    
    audio_tools = [
        ("Suno Music", "src.tools.audio.music_generation", "get_suno_music_generation_tool"),
        ("TTS", "src.tools.audio.text_to_speech", "get_text_to_speech_tool"),
        ("Voice Clone", "src.tools.audio.text_to_speech", "get_voice_clone_tool"),
        ("Text to Voice", "src.tools.audio.text_to_speech", "get_text_to_voice_tool"),
        ("Multi Speaker TTS", "src.tools.audio.multi_speaker_tts", "get_multi_speaker_tts_tool"),
    ]
    
    results = {}
    
    for tool_name, module_path, function_name in audio_tools:
        print(f"\n🔧 测试 {tool_name}...")
        
        try:
            # 导入模块
            module = __import__(module_path, fromlist=[function_name])
            print(f"✅ 模块 {module_path} 导入成功")
            
            # 获取函数
            tool_func = getattr(module, function_name)
            print(f"✅ 函数 {function_name} 获取成功")
            
            # 导入配置
            from src.config.configuration import Configuration
            config = Configuration.from_runnable_config()
            
            # 创建工具实例
            tool_instance = tool_func(config=config)
            print(f"✅ 工具实例创建成功: {tool_instance.name if tool_instance else 'None'}")
            
            results[tool_name] = True
            
        except Exception as e:
            print(f"❌ {tool_name} 失败: {e}")
            results[tool_name] = False
            
            # 如果是循环导入错误，打印详细信息
            if "circular import" in str(e):
                print(f"🔍 循环导入详情: {e}")
                import traceback
                traceback.print_exc()
    
    return results

def test_tools_init_import():
    """测试从tools.__init__导入"""
    print("\n📦 测试从 src.tools 导入...")
    
    try:
        # 尝试导入单个工具
        from src.tools.audio.music_generation import get_suno_music_generation_tool
        print("✅ Suno工具直接导入成功")
        
        from src.tools.audio.text_to_speech import get_text_to_speech_tool
        print("✅ TTS工具直接导入成功")
        
        # 这个应该会失败
        from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
        print("✅ Multi Speaker TTS工具直接导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接导入失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 开始逐个音频工具导入测试...")
    
    # 先测试直接导入
    direct_result = test_tools_init_import()
    
    if not direct_result:
        print("\n🔍 直接导入失败，开始逐个测试...")
        individual_results = test_individual_audio_tools()
        
        print(f"\n📊 逐个测试结果:")
        for tool_name, success in individual_results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {tool_name}: {status}")
        
        # 找出问题工具
        failed_tools = [name for name, success in individual_results.items() if not success]
        if failed_tools:
            print(f"\n⚠️ 失败的工具: {', '.join(failed_tools)}")
        else:
            print("\n🎉 所有工具单独导入都成功！问题可能在tools.__init__.py的组合导入中")
    else:
        print("\n🎉 所有工具直接导入都成功！")

#!/usr/bin/env python3
"""
快速测试Audio Agent工具
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_audio_tools():
    """测试音频工具"""
    print("🔧 测试音频工具...")
    
    try:
        # 导入工具
        from src.tools import audio_tools, multi_speaker_tts_tool
        
        print(f"✅ 导入成功")
        print(f"📋 audio_tools数量: {len(audio_tools)}")
        print(f"📋 multi_speaker_tts_tool: {multi_speaker_tts_tool}")
        
        # 列出所有工具
        print("\n🔧 audio_tools列表:")
        for i, tool in enumerate(audio_tools):
            if tool is not None:
                name = getattr(tool, 'name', 'Unknown')
                print(f"   {i+1}. {name}")
            else:
                print(f"   {i+1}. None")
        
        # 检查multi_speaker_tts是否在列表中
        multi_tts_names = [getattr(tool, 'name', '') for tool in audio_tools if tool is not None]
        has_multi_tts = 'multi_speaker_tts' in multi_tts_names
        
        print(f"\n📋 multi_speaker_tts在audio_tools中: {'✅ 是' if has_multi_tts else '❌ 否'}")
        
        return has_multi_tts
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def test_audio_agent():
    """测试Audio Agent"""
    print("\n🤖 测试Audio Agent...")
    
    try:
        from src.agents.agents import audio_creator_agent
        
        print(f"✅ Audio Agent导入成功")
        print(f"📋 Agent类型: {type(audio_creator_agent)}")
        
        # 检查Agent的属性
        attrs = ['tools', 'runnable', 'config']
        for attr in attrs:
            has_attr = hasattr(audio_creator_agent, attr)
            print(f"📋 有{attr}属性: {'是' if has_attr else '否'}")
            
            if has_attr and attr == 'tools':
                tools = getattr(audio_creator_agent, attr)
                if tools:
                    print(f"   工具数量: {len(tools)}")
                    for i, tool in enumerate(tools[:3]):  # 只显示前3个
                        name = getattr(tool, 'name', 'Unknown')
                        print(f"   {i+1}. {name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

if __name__ == "__main__":
    print("🎯 快速测试Audio Agent工具集成...")
    
    result1 = test_audio_tools()
    result2 = test_audio_agent()
    
    print(f"\n📊 测试结果:")
    print(f"音频工具: {'✅ 通过' if result1 else '❌ 失败'}")
    print(f"Audio Agent: {'✅ 通过' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print("\n🎉 集成正常！")
    else:
        print("\n⚠️ 可能存在问题")

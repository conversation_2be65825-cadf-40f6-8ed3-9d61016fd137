#!/usr/bin/env python3
"""
简化版多人TTS工具测试脚本
不依赖soundfile和pydub，用于快速验证API调用
"""

import os
import json
import logging
import requests
import asyncio
import aiohttp
from typing import List, Dict, Any
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

class SimpleDialogueItem:
    """简化的对话项"""
    def __init__(self, speaker: str, text: str, emotion: str = "neutral", speed: float = 1.0):
        self.speaker = speaker
        self.text = text
        self.emotion = emotion
        self.speed = speed

async def test_minimax_api_single():
    """测试单句Minimax TTS API调用"""
    logger.info("🧪 测试单句Minimax TTS API调用...")
    
    # 检查API配置
    api_key = os.getenv("MINIMAX_API_KEY")
    group_id = os.getenv("MINIMAX_GROUP_ID")
    
    if not api_key or not group_id:
        logger.error("❌ 缺少Minimax API配置")
        return False
    
    # API配置
    api_url = f"https://api.minimaxi.com/v1/t2a_v2?GroupId={group_id}"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # 测试数据 - 使用正确的Minimax API格式
    payload = {
        "model": "speech-02-hd",
        "text": "你好，这是一个测试语音。",
        "voice_setting": {
            "voice_id": "female-yujie",  # 使用文档中的voice_id
            "speed": 1.0,
            "vol": 1.0,
            "pitch": 0,
            "emotion": "neutral"
        },
        "audio_setting": {
            "sample_rate": 32000,
            "bitrate": 128000,
            "format": "mp3",
            "channel": 1
        },
        "stream": False,
        "output_format": "hex"
    }
    
    try:
        logger.info(f"📤 发送API请求到: {api_url}")
        logger.info(f"📋 请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        response = requests.post(api_url, headers=headers, json=payload, timeout=60)
        
        logger.info(f"📥 响应状态码: {response.status_code}")
        logger.info(f"📥 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            logger.info(f"✅ API调用成功!")
            logger.info(f"📄 响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            return True
        else:
            logger.error(f"❌ API调用失败: HTTP {response.status_code}")
            logger.error(f"📄 错误响应: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ API调用异常: {e}")
        return False

async def test_minimax_api_concurrent():
    """测试并发Minimax TTS API调用"""
    logger.info("🚀 测试并发Minimax TTS API调用...")
    
    # 检查API配置
    api_key = os.getenv("MINIMAX_API_KEY")
    group_id = os.getenv("MINIMAX_GROUP_ID")
    
    if not api_key or not group_id:
        logger.error("❌ 缺少Minimax API配置")
        return False
    
    # 测试对话数据
    test_dialogues = [
        SimpleDialogueItem("张三", "大家好，我是张三。", "happy", 1.0),
        SimpleDialogueItem("李四", "你好张三，我是李四。", "neutral", 0.9),
        SimpleDialogueItem("张三", "很高兴认识你！", "happy", 1.1),
        SimpleDialogueItem("李四", "我也很高兴认识你。", "neutral", 1.0)
    ]
    
    # API配置
    api_url = f"https://api.minimaxi.com/v1/t2a_v2?GroupId={group_id}"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    async def generate_single_audio(session, dialogue, idx):
        """生成单句音频"""
        # 为不同说话人选择不同的声音
        voice_id = "female-yujie" if dialogue.speaker == "张三" else "male-qn-qingse"

        payload = {
            "model": "speech-02-hd",
            "text": dialogue.text,
            "voice_setting": {
                "voice_id": voice_id,
                "speed": dialogue.speed,
                "vol": 1.0,
                "pitch": 0,
                "emotion": dialogue.emotion
            },
            "audio_setting": {
                "sample_rate": 32000,
                "bitrate": 128000,
                "format": "mp3",
                "channel": 1
            },
            "stream": False,
            "output_format": "hex"
        }
        
        try:
            logger.info(f"🎤 生成第{idx+1}句: {dialogue.speaker} - {dialogue.text}")
            
            async with session.post(api_url, json=payload) as response:
                if response.status == 200:
                    response_data = await response.json()
                    logger.info(f"✅ 第{idx+1}句生成成功")
                    return {
                        "success": True,
                        "idx": idx,
                        "speaker": dialogue.speaker,
                        "text": dialogue.text,
                        "response": response_data
                    }
                else:
                    error_text = await response.text()
                    logger.error(f"❌ 第{idx+1}句生成失败: HTTP {response.status}")
                    return {
                        "success": False,
                        "idx": idx,
                        "error": f"HTTP {response.status}: {error_text}"
                    }
                    
        except Exception as e:
            logger.error(f"❌ 第{idx+1}句生成异常: {e}")
            return {
                "success": False,
                "idx": idx,
                "error": str(e)
            }
    
    try:
        # 并发执行
        async with aiohttp.ClientSession(headers=headers, timeout=aiohttp.ClientTimeout(total=120)) as session:
            tasks = [
                generate_single_audio(session, dialogue, idx)
                for idx, dialogue in enumerate(test_dialogues)
            ]
            
            logger.info(f"⚡ 并发执行{len(tasks)}个音频生成任务...")
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            success_count = sum(1 for r in results if isinstance(r, dict) and r.get("success"))
            total_count = len(results)
            
            logger.info(f"📊 并发测试完成: {success_count}/{total_count} 成功")
            
            for result in results:
                if isinstance(result, dict):
                    if result.get("success"):
                        logger.info(f"✅ 第{result['idx']+1}句: {result['speaker']} - 成功")
                    else:
                        logger.error(f"❌ 第{result['idx']+1}句: {result.get('error', '未知错误')}")
                else:
                    logger.error(f"❌ 任务异常: {result}")
            
            return success_count == total_count
            
    except Exception as e:
        logger.error(f"❌ 并发测试异常: {e}")
        return False

async def test_cos_upload():
    """测试COS上传功能"""
    logger.info("☁️ 测试COS上传功能...")
    
    # 检查COS配置
    required_vars = ["TENCENT_SECRET_ID", "TENCENT_SECRET_KEY", "COS_REGION", "COS_BUCKET"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ 缺少COS配置: {missing_vars}")
        return False
    
    try:
        # 尝试导入COS相关模块
        from src.utils.cos_uploader import get_cos_client, upload_to_cos
        from src.config.configuration import Configuration
        
        # 创建配置
        class TestConfig(Configuration):
            def __init__(self):
                super().__init__()
                self.tencent_secret_id = os.getenv("TENCENT_SECRET_ID")
                self.tencent_secret_key = os.getenv("TENCENT_SECRET_KEY")
                self.cos_region = os.getenv("COS_REGION")
                self.cos_bucket = os.getenv("COS_BUCKET")
        
        config = TestConfig()
        
        # 初始化COS客户端
        cos_client = get_cos_client(config)
        logger.info("✅ COS客户端初始化成功")
        
        # 测试上传一个小文件
        test_content = "这是一个测试文件内容"
        test_bytes = test_content.encode('utf-8')
        
        cos_url = upload_to_cos(
            client=cos_client,
            file_bytes=test_bytes,
            bucket=config.cos_bucket,
            region=config.cos_region,
            file_extension="txt"
        )
        
        logger.info(f"✅ 测试文件上传成功: {cos_url}")
        return True
        
    except ImportError as e:
        logger.error(f"❌ 导入COS模块失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ COS上传测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🎭 开始多人TTS工具测试...")
    
    # 测试步骤
    tests = [
        ("单句API调用", test_minimax_api_single),
        ("并发API调用", test_minimax_api_concurrent),
        ("COS上传功能", test_cos_upload)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*50}")
    logger.info("📊 测试总结")
    logger.info(f"{'='*50}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 所有测试通过！多人TTS工具准备就绪！")
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
测试最终的Minimax声音配置集成
"""

import os
import logging
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_configuration_voice_system():
    """测试Configuration中的声音系统"""
    logger.info("🎭 测试Configuration中的Minimax声音系统...")
    
    try:
        from src.config.configuration import (
            Configuration,
            MINIMAX_SYSTEM_VOICES,
            MINIMAX_PARAM_RANGES,
            MINIMAX_EMOTIONS,
            MINIMAX_VOICE_PAIRS
        )
        
        # 创建配置实例
        config = Configuration.from_runnable_config()
        
        # 统计声音数量
        total_voices = len(MINIMAX_SYSTEM_VOICES)
        male_voices = len(config.get_minimax_voices_by_gender("male"))
        female_voices = len(config.get_minimax_voices_by_gender("female"))
        
        logger.info(f"✅ 声音配置加载成功")
        logger.info(f"📊 声音统计:")
        logger.info(f"   • 总计: {total_voices} 个系统音色")
        logger.info(f"   • 男声: {male_voices} 个")
        logger.info(f"   • 女声: {female_voices} 个")
        logger.info(f"   • 其他: {total_voices - male_voices - female_voices} 个")
        
        # 显示各分类的声音数量
        categories = ["youth", "presenter", "audiobook", "premium", "child", "character", "english"]
        logger.info(f"📋 分类统计:")
        for category in categories:
            voices = config.get_minimax_voices_by_category(category)
            logger.info(f"   • {category}: {len(voices)} 个")
        
        # 显示参数范围
        logger.info(f"🎚️ 参数范围:")
        for param, config_info in MINIMAX_PARAM_RANGES.items():
            logger.info(f"   • {param}: [{config_info['min']}, {config_info['max']}], 默认: {config_info['default']}")
        
        # 显示支持的情感
        logger.info(f"😊 支持情感: {', '.join(MINIMAX_EMOTIONS)}")
        
        # 显示推荐组合
        logger.info(f"💡 推荐声音组合:")
        for name, pair in MINIMAX_VOICE_PAIRS.items():
            logger.info(f"   • {name}: {pair['male']} + {pair['female']} - {pair['desc']}")
        
        # 测试配置方法
        logger.info(f"🔧 配置方法测试:")
        
        # 测试声音信息获取
        test_voice = "female-yujie"
        voice_info = config.get_minimax_voice_info(test_voice)
        logger.info(f"   • 声音信息: {test_voice} -> {voice_info}")
        
        # 测试参数验证
        validation_result = config.validate_minimax_params(
            speed=1.5, vol=2.0, pitch=3, emotion="happy"
        )
        logger.info(f"   • 参数验证: {validation_result}")
        
        # 测试推荐组合获取
        recommended = config.get_recommended_voice_pair("professional")
        logger.info(f"   • 推荐组合: {recommended}")
        
        # 验证默认声音是否存在
        default_female = config.minimax_default_female_voice
        default_male = config.minimax_default_male_voice
        
        female_exists = default_female in MINIMAX_SYSTEM_VOICES
        male_exists = default_male in MINIMAX_SYSTEM_VOICES
        
        logger.info(f"🎯 默认声音验证:")
        logger.info(f"   • 默认女声: {default_female} {'✅存在' if female_exists else '❌不存在'}")
        logger.info(f"   • 默认男声: {default_male} {'✅存在' if male_exists else '❌不存在'}")
        
        return female_exists and male_exists
        
    except Exception as e:
        logger.error(f"❌ 声音系统测试失败: {e}")
        return False

def test_voice_selection_scenarios():
    """测试各种声音选择场景"""
    logger.info("🎵 测试声音选择场景...")
    
    try:
        from src.config.configuration import Configuration, MINIMAX_SYSTEM_VOICES
        
        config = Configuration.from_runnable_config()
        
        # 场景1: 专业播客
        logger.info("📻 场景1: 专业播客")
        professional_pair = config.get_recommended_voice_pair("professional")
        logger.info(f"   推荐组合: {professional_pair['male']} + {professional_pair['female']}")
        
        # 场景2: 青年对话
        logger.info("👥 场景2: 青年对话")
        youth_pair = config.get_recommended_voice_pair("youth")
        logger.info(f"   推荐组合: {youth_pair['male']} + {youth_pair['female']}")
        
        # 场景3: 有声书朗读
        logger.info("📚 场景3: 有声书朗读")
        audiobook_pair = config.get_recommended_voice_pair("audiobook")
        logger.info(f"   推荐组合: {audiobook_pair['male']} + {audiobook_pair['female']}")
        
        # 场景4: 角色扮演
        logger.info("🎭 场景4: 角色扮演")
        character_pair = config.get_recommended_voice_pair("character")
        logger.info(f"   推荐组合: {character_pair['male']} + {character_pair['female']}")
        
        # 场景5: 精品版高质量
        logger.info("💎 场景5: 精品版高质量")
        premium_pair = config.get_recommended_voice_pair("premium")
        logger.info(f"   推荐组合: {premium_pair['male']} + {premium_pair['female']}")
        
        # 验证所有推荐声音都存在
        all_exist = True
        for pair_name, pair in [
            ("professional", professional_pair),
            ("youth", youth_pair),
            ("audiobook", audiobook_pair),
            ("character", character_pair),
            ("premium", premium_pair)
        ]:
            male_exists = pair["male"] in MINIMAX_SYSTEM_VOICES
            female_exists = pair["female"] in MINIMAX_SYSTEM_VOICES
            
            if not (male_exists and female_exists):
                logger.error(f"❌ {pair_name}组合中的声音不存在")
                all_exist = False
        
        if all_exist:
            logger.info("✅ 所有推荐声音组合都存在")
        
        return all_exist
        
    except Exception as e:
        logger.error(f"❌ 声音选择场景测试失败: {e}")
        return False

def test_parameter_validation():
    """测试参数验证功能"""
    logger.info("🔍 测试参数验证功能...")
    
    try:
        from src.config.configuration import Configuration
        
        config = Configuration.from_runnable_config()
        
        # 测试有效参数
        valid_tests = [
            {"speed": 1.0, "vol": 1.0, "pitch": 0, "emotion": "calm"},
            {"speed": 0.5, "vol": 10.0, "pitch": -12, "emotion": "happy"},
            {"speed": 2.0, "vol": 0.1, "pitch": 12, "emotion": "sad"}
        ]
        
        # 测试无效参数
        invalid_tests = [
            {"speed": 0.4, "vol": 1.0, "pitch": 0, "emotion": "calm"},  # speed太小
            {"speed": 1.0, "vol": 11.0, "pitch": 0, "emotion": "calm"},  # vol太大
            {"speed": 1.0, "vol": 1.0, "pitch": 13, "emotion": "calm"},  # pitch太大
            {"speed": 1.0, "vol": 1.0, "pitch": 0, "emotion": "invalid"}  # 无效情感
        ]
        
        logger.info("✅ 有效参数测试:")
        for i, params in enumerate(valid_tests):
            result = config.validate_minimax_params(**params)
            all_valid = all(result.values())
            status = "✅" if all_valid else "❌"
            logger.info(f"   {status} 测试{i+1}: {params} -> {result}")
        
        logger.info("❌ 无效参数测试:")
        for i, params in enumerate(invalid_tests):
            result = config.validate_minimax_params(**params)
            has_invalid = not all(result.values())
            status = "✅" if has_invalid else "❌"
            logger.info(f"   {status} 测试{i+1}: {params} -> {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 参数验证测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🎯 开始最终声音配置集成测试...")
    
    # 测试步骤
    tests = [
        ("Configuration声音系统", test_configuration_voice_system),
        ("声音选择场景", test_voice_selection_scenarios),
        ("参数验证功能", test_parameter_validation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("📊 最终声音配置集成测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 Minimax声音配置完美集成到Configuration中！")
        logger.info("")
        logger.info("🚀 **最终系统特性:**")
        logger.info("   🎭 50+ 完整系统音色 - 直接集成在Configuration类中")
        logger.info("   🎚️ 精细参数控制 - 语速[0.5-2.0]、音量(0-10]、语调[-12,12]")
        logger.info("   😊 7种情感支持 - happy/sad/angry/fearful/disgusted/surprised/calm")
        logger.info("   💡 5种推荐组合 - 专业/青年/有声书/角色/精品版")
        logger.info("   🔍 参数验证方法 - 自动验证所有参数范围")
        logger.info("   🔧 便捷查询方法 - 按性别、分类、ID查询声音信息")
        logger.info("")
        logger.info("🎯 现在所有TTS工具都可以使用统一的专业声音配置了！")
        logger.info("📋 配置位置: src/config/configuration.py")
        logger.info("🛠️ 使用方式: Configuration.get_minimax_voices_by_gender('male')")
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试统一模板系统

验证更新后的模板系统是否正常工作。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
from src.graph_v2.template_models import PlanTemplate, StepTemplate, ParameterSchema, ParameterType
from src.graph_v2.template_renderer import template_renderer, TemplateRenderError


def test_template_creation():
    """测试模板创建"""
    print("🧪 测试模板创建...")
    
    try:
        # 创建参数schema
        params = {
            "character": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="角色名称"
            ),
            "style": ParameterSchema(
                type=ParameterType.STRING,
                required=False,
                default="modern",
                options=["modern", "classic", "cartoon"],
                description="视频风格"
            ),
            "duration": ParameterSchema(
                type=ParameterType.INTEGER,
                required=False,
                default=30,
                min_value=10,
                max_value=300,
                description="视频时长（秒）"
            )
        }
        
        # 创建步骤模板
        step_templates = [
            StepTemplate(
                template_step_id="collect_materials",
                name="收集素材",
                description_template="收集{{ character }}的{{ style }}风格素材",
                tool_to_use="visual_expert",
                input_template={
                    "character": "{{ character }}",
                    "style": "{{ style }}",
                    "search_keywords": ["{{ character }}", "{{ style }}", "素材"]
                }
            ),
            StepTemplate(
                template_step_id="generate_video",
                name="生成视频",
                description_template="制作{{ character }}的{{ duration }}秒{{ style }}风格视频",
                tool_to_use="video_expert",
                input_template={
                    "character": "{{ character }}",
                    "style": "{{ style }}",
                    "duration": "{{ duration }}",
                    "materials": "从collect_materials步骤获取"
                },
                dependencies=["collect_materials"]
            )
        ]
        
        # 创建计划模板
        template = PlanTemplate(
            template_id="character_video",
            name="角色视频制作",
            description="制作{{ character }}的{{ style }}风格视频",
            category="video_creation",
            tags=["character", "video", "ai"],
            step_templates=step_templates,
            parameters=params
        )
        
        print("✅ 模板创建成功")
        return template
        
    except Exception as e:
        print(f"❌ 模板创建失败: {e}")
        return None


def test_template_rendering():
    """测试模板渲染"""
    print("\n🧪 测试模板渲染...")
    
    template = test_template_creation()
    if not template:
        return False
    
    try:
        # 测试参数
        params = {
            "character": "哪吒",
            "style": "modern",
            "duration": 60
        }
        
        # 渲染模板
        plan = template_renderer.render_plan(template, params)
        
        # 验证结果
        assert isinstance(plan, UnifiedPlan)
        assert plan.template_id == "character_video"
        assert plan.is_from_template == True
        assert len(plan.steps) == 2
        
        # 验证第一个步骤
        step1 = plan.get_step("collect_materials")
        assert step1 is not None
        assert "哪吒" in step1.description
        assert "modern" in step1.description
        assert step1.inputs["character"] == "哪吒"
        assert step1.inputs["style"] == "modern"
        
        # 验证第二个步骤
        step2 = plan.get_step("generate_video")
        assert step2 is not None
        assert "哪吒" in step2.description
        assert "60秒" in step2.description
        assert step2.dependencies == ["collect_materials"]
        assert step2.inputs["duration"] == "60"
        
        print("✅ 模板渲染成功")
        print(f"   计划ID: {plan.plan_id}")
        print(f"   任务描述: {plan.original_task}")
        print(f"   步骤数量: {len(plan.steps)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模板渲染失败: {e}")
        return False


def test_parameter_validation():
    """测试参数验证"""
    print("\n🧪 测试参数验证...")
    
    template = test_template_creation()
    if not template:
        return False
    
    try:
        # 测试缺少必需参数
        try:
            template_renderer.render_plan(template, {"style": "modern"})
            print("❌ 应该检测到缺少必需参数")
            return False
        except TemplateRenderError as e:
            if "缺少必需参数" in str(e):
                print("✅ 正确检测到缺少必需参数")
            else:
                print(f"❌ 错误信息不正确: {e}")
                return False
        
        # 测试无效选项
        try:
            template_renderer.render_plan(template, {
                "character": "哪吒",
                "style": "invalid_style"
            })
            print("❌ 应该检测到无效选项")
            return False
        except TemplateRenderError as e:
            if "不在允许的选项中" in str(e):
                print("✅ 正确检测到无效选项")
            else:
                print(f"❌ 错误信息不正确: {e}")
                return False
        
        # 测试数值范围
        try:
            template_renderer.render_plan(template, {
                "character": "哪吒",
                "duration": 500  # 超过最大值
            })
            print("❌ 应该检测到数值超出范围")
            return False
        except TemplateRenderError as e:
            if "大于最大值" in str(e):
                print("✅ 正确检测到数值超出范围")
            else:
                print(f"❌ 错误信息不正确: {e}")
                return False
        
        # 测试正确参数
        plan = template_renderer.render_plan(template, {
            "character": "哪吒",
            "style": "cartoon",
            "duration": 45
        })
        
        assert plan.template_params["character"] == "哪吒"
        assert plan.template_params["style"] == "cartoon"
        assert plan.template_params["duration"] == 45
        
        print("✅ 参数验证功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 参数验证测试失败: {e}")
        return False


def test_complex_template_logic():
    """测试复杂模板逻辑"""
    print("\n🧪 测试复杂模板逻辑...")
    
    try:
        # 创建包含条件逻辑的模板
        step_template = StepTemplate(
            template_step_id="conditional_step",
            name="条件步骤",
            description_template="{% if style == 'modern' %}现代风格处理{% else %}传统风格处理{% endif %}",
            tool_to_use="style_processor",
            input_template={
                "processing_mode": "{% if style == 'modern' %}ai_enhanced{% else %}traditional{% endif %}",
                "filters": [
                    "{% if style == 'modern' %}modern_filter{% endif %}",
                    "{% if style == 'cartoon' %}cartoon_filter{% endif %}"
                ]
            }
        )
        
        template = PlanTemplate(
            template_id="conditional_test",
            name="条件测试",
            description="测试条件逻辑",
            category="test",
            step_templates=[step_template],
            parameters={
                "style": ParameterSchema(
                    type=ParameterType.STRING,
                    required=True,
                    options=["modern", "cartoon", "classic"]
                )
            }
        )
        
        # 测试modern风格
        plan1 = template_renderer.render_plan(template, {"style": "modern"})
        step1 = plan1.steps[0]
        assert "现代风格处理" in step1.description
        assert step1.inputs["processing_mode"] == "ai_enhanced"
        
        # 测试cartoon风格
        plan2 = template_renderer.render_plan(template, {"style": "cartoon"})
        step2 = plan2.steps[0]
        assert "传统风格处理" in step2.description
        assert step2.inputs["processing_mode"] == "traditional"
        
        print("✅ 复杂模板逻辑正常")
        return True
        
    except Exception as e:
        print(f"❌ 复杂模板逻辑测试失败: {e}")
        return False


def test_plan_execution_flow():
    """测试计划执行流程"""
    print("\n🧪 测试计划执行流程...")
    
    template = test_template_creation()
    if not template:
        return False
    
    try:
        # 渲染计划
        plan = template_renderer.render_plan(template, {
            "character": "哪吒",
            "style": "modern",
            "duration": 30
        })
        
        # 测试执行流程
        assert not plan.is_complete()
        
        # 获取可执行步骤
        executable_steps = plan.get_next_executable_steps()
        assert len(executable_steps) == 1
        assert executable_steps[0].step_id == "collect_materials"
        
        # 完成第一个步骤
        step1 = plan.get_step("collect_materials")
        step1.mark_completed({
            "materials": ["image1.jpg", "image2.jpg"],
            "success": True
        })
        
        # 检查进度
        progress = plan.get_progress_info()
        assert progress["completed_steps"] == 1
        assert progress["progress_percentage"] == 50.0
        
        # 获取下一个可执行步骤
        executable_steps = plan.get_next_executable_steps()
        assert len(executable_steps) == 1
        assert executable_steps[0].step_id == "generate_video"
        
        # 完成第二个步骤
        step2 = plan.get_step("generate_video")
        step2.mark_completed({
            "video_url": "output.mp4",
            "success": True
        })
        
        # 检查完成状态
        assert plan.is_complete()
        progress = plan.get_progress_info()
        assert progress["completed_steps"] == 2
        assert progress["progress_percentage"] == 100.0
        
        print("✅ 计划执行流程正常")
        return True
        
    except Exception as e:
        print(f"❌ 计划执行流程测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 开始测试统一模板系统\n")
    
    tests = [
        ("模板渲染", test_template_rendering),
        ("参数验证", test_parameter_validation),
        ("复杂模板逻辑", test_complex_template_logic),
        ("计划执行流程", test_plan_execution_flow),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！统一模板系统工作正常。")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
        return False


if __name__ == "__main__":
    main()

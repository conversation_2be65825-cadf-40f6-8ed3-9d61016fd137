#!/usr/bin/env python3
"""
测试导入问题
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_step_by_step():
    """逐步测试导入"""
    
    print("1. 测试基础配置导入...")
    try:
        from src.config.configuration import Configuration
        print("✅ Configuration导入成功")
    except Exception as e:
        print(f"❌ Configuration导入失败: {e}")
        return
    
    print("\n2. 测试多人TTS工具导入...")
    try:
        from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
        print("✅ get_multi_speaker_tts_tool导入成功")
    except Exception as e:
        print(f"❌ get_multi_speaker_tts_tool导入失败: {e}")
        return
    
    print("\n3. 测试工具初始化...")
    try:
        config = Configuration.from_runnable_config()
        multi_speaker_tts_tool = get_multi_speaker_tts_tool(config=config)
        print(f"✅ 工具初始化成功: {multi_speaker_tts_tool.name if multi_speaker_tts_tool else 'None'}")
    except Exception as e:
        print(f"❌ 工具初始化失败: {e}")
        return
    
    print("\n4. 测试其他音频工具导入...")
    try:
        from src.tools.audio.music_generation import get_suno_music_generation_tool
        from src.tools.audio.text_to_speech import get_text_to_speech_tool
        print("✅ 其他音频工具导入成功")
    except Exception as e:
        print(f"❌ 其他音频工具导入失败: {e}")
        return
    
    print("\n5. 测试tools模块导入...")
    try:
        # 尝试直接导入tools模块
        import src.tools
        print("✅ src.tools模块导入成功")
        
        # 检查模块属性
        if hasattr(src.tools, 'audio_tools'):
            audio_tools = src.tools.audio_tools
            print(f"✅ audio_tools属性存在，长度: {len(audio_tools)}")
            
            # 检查multi_speaker_tts是否在其中
            tool_names = [getattr(tool, 'name', 'Unknown') for tool in audio_tools if tool is not None]
            print(f"📋 工具名称: {tool_names}")
            
            has_multi_tts = 'multi_speaker_tts' in tool_names
            print(f"📋 包含multi_speaker_tts: {'✅ 是' if has_multi_tts else '❌ 否'}")
            
        else:
            print("❌ audio_tools属性不存在")
            
    except Exception as e:
        print(f"❌ src.tools模块导入失败: {e}")
        return
    
    print("\n6. 测试从tools直接导入...")
    try:
        from src.tools import multi_speaker_tts_tool
        print(f"✅ 直接导入multi_speaker_tts_tool成功: {multi_speaker_tts_tool.name if multi_speaker_tts_tool else 'None'}")
    except Exception as e:
        print(f"❌ 直接导入multi_speaker_tts_tool失败: {e}")
        return
    
    print("\n🎉 所有导入测试完成！")

if __name__ == "__main__":
    print("🎯 开始逐步导入测试...")
    test_step_by_step()

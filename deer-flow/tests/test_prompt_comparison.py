#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试提示词改进效果

对比改进前后的规划提示词效果。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.prompt_utils import get_agent_prompt_template


def test_prompt_content():
    """测试提示词内容"""
    print("🧪 测试提示词内容...")
    
    try:
        # 获取规划提示词
        prompt_template = get_agent_prompt_template("planner_prompt_zh")
        
        # 检查提示词内容
        prompt_content = prompt_template.template
        
        # 验证关键改进点
        improvements = {
            "任务类型识别": "任务类型识别" in prompt_content,
            "步骤命名规范": "步骤命名规范" in prompt_content,
            "具体示例": "示例参考" in prompt_content,
            "工具详细说明": "visual_expert" in prompt_content and "用于生成图像" in prompt_content,
            "城市海报示例": "北京、上海、广州" in prompt_content,
            "多媒体示例": "哪吒主题" in prompt_content,
        }
        
        print("   提示词改进点检查:")
        for improvement, found in improvements.items():
            status = "✅" if found else "❌"
            print(f"   {status} {improvement}: {'已包含' if found else '未包含'}")
        
        # 计算改进覆盖率
        coverage = sum(improvements.values()) / len(improvements) * 100
        print(f"\n   改进覆盖率: {coverage:.1f}%")
        
        return coverage >= 80  # 80%以上认为通过
        
    except Exception as e:
        print(f"   ❌ 提示词内容测试失败: {e}")
        return False


def test_prompt_structure():
    """测试提示词结构"""
    print("\n🧪 测试提示词结构...")
    
    try:
        prompt_template = get_agent_prompt_template("planner_prompt_zh")
        prompt_content = prompt_template.template
        
        # 检查结构完整性
        structure_elements = {
            "核心指令": "核心指令" in prompt_content,
            "格式说明": "格式说明" in prompt_content,
            "示例参考": "示例参考" in prompt_content,
            "任务分析": "开始分析" in prompt_content,
            "变量占位符": "{task}" in prompt_content and "{plan}" in prompt_content,
        }
        
        print("   提示词结构检查:")
        for element, found in structure_elements.items():
            status = "✅" if found else "❌"
            print(f"   {status} {element}: {'已包含' if found else '未包含'}")
        
        # 检查示例格式
        has_json_examples = prompt_content.count("```json") >= 2
        print(f"   {'✅' if has_json_examples else '❌'} JSON示例: {'充足' if has_json_examples else '不足'}")
        
        structure_score = sum(structure_elements.values()) + (1 if has_json_examples else 0)
        total_checks = len(structure_elements) + 1
        
        print(f"\n   结构完整性: {structure_score}/{total_checks} ({structure_score/total_checks*100:.1f}%)")
        
        return structure_score >= total_checks * 0.8
        
    except Exception as e:
        print(f"   ❌ 提示词结构测试失败: {e}")
        return False


def test_example_quality():
    """测试示例质量"""
    print("\n🧪 测试示例质量...")
    
    try:
        prompt_template = get_agent_prompt_template("planner_prompt_zh")
        prompt_content = prompt_template.template
        
        # 检查示例质量指标
        quality_metrics = {
            "具体城市名称": all(city in prompt_content for city in ["北京", "上海", "广州"]),
            "具体角色名称": "哪吒" in prompt_content,
            "工具使用多样性": all(tool in prompt_content for tool in ["visual_expert", "audio_expert", "video_expert"]),
            "依赖关系示例": "dependencies" in prompt_content and "[1, 2]" in prompt_content,
            "输入参数示例": "inputs" in prompt_content and "landmarks" in prompt_content,
            "描述性步骤名称": "创建北京旅游海报" in prompt_content,
        }
        
        print("   示例质量检查:")
        for metric, passed in quality_metrics.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {metric}: {'优秀' if passed else '需改进'}")
        
        quality_score = sum(quality_metrics.values()) / len(quality_metrics) * 100
        print(f"\n   示例质量评分: {quality_score:.1f}%")
        
        return quality_score >= 80
        
    except Exception as e:
        print(f"   ❌ 示例质量测试失败: {e}")
        return False


def test_prompt_length_and_clarity():
    """测试提示词长度和清晰度"""
    print("\n🧪 测试提示词长度和清晰度...")
    
    try:
        prompt_template = get_agent_prompt_template("planner_prompt_zh")
        prompt_content = prompt_template.template
        
        # 长度分析
        total_length = len(prompt_content)
        line_count = len(prompt_content.split('\n'))
        
        # 清晰度指标
        clarity_metrics = {
            "适当长度": 1000 <= total_length <= 5000,  # 1K-5K字符
            "合理行数": 50 <= line_count <= 150,  # 50-150行
            "段落分明": prompt_content.count('\n\n') >= 5,  # 至少5个段落
            "标题清晰": prompt_content.count('**') >= 10,  # 足够的标题标记
            "代码块格式": prompt_content.count('```') >= 4,  # 至少2个代码块
        }
        
        print(f"   提示词长度: {total_length} 字符")
        print(f"   提示词行数: {line_count} 行")
        print("\n   清晰度检查:")
        
        for metric, passed in clarity_metrics.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {metric}: {'合格' if passed else '不合格'}")
        
        clarity_score = sum(clarity_metrics.values()) / len(clarity_metrics) * 100
        print(f"\n   清晰度评分: {clarity_score:.1f}%")
        
        return clarity_score >= 80
        
    except Exception as e:
        print(f"   ❌ 长度和清晰度测试失败: {e}")
        return False


def compare_with_original():
    """与原始提示词对比"""
    print("\n🧪 与原始提示词对比...")
    
    try:
        current_prompt = get_agent_prompt_template("planner_prompt_zh").template
        
        # 分析改进点
        improvements = {
            "示例数量": current_prompt.count("示例") >= 2,
            "具体指导": "步骤命名规范" in current_prompt,
            "任务分类": "任务类型识别" in current_prompt,
            "工具说明": "用于生成图像" in current_prompt,
            "JSON示例": current_prompt.count("```json") >= 2,
        }
        
        print("   改进点对比:")
        for improvement, added in improvements.items():
            status = "✅ 新增" if added else "❌ 缺失"
            print(f"   {status} {improvement}")
        
        improvement_rate = sum(improvements.values()) / len(improvements) * 100
        print(f"\n   改进程度: {improvement_rate:.1f}%")
        
        return improvement_rate >= 80
        
    except Exception as e:
        print(f"   ❌ 对比测试失败: {e}")
        return False


def main():
    """运行所有提示词测试"""
    print("🚀 开始测试提示词改进效果\n")
    
    tests = [
        ("提示词内容", test_prompt_content),
        ("提示词结构", test_prompt_structure),
        ("示例质量", test_example_quality),
        ("长度和清晰度", test_prompt_length_and_clarity),
        ("与原始对比", compare_with_original),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 提示词改进效果优秀！")
        print("\n📋 改进总结:")
        print("✅ 添加了任务类型识别指南")
        print("✅ 提供了具体的命名规范")
        print("✅ 包含了丰富的示例")
        print("✅ 增强了工具使用说明")
        print("✅ 改善了整体结构和清晰度")
        
        print("\n🚀 现在应该能生成更高质量的计划了！")
        return True
    else:
        print("⚠️ 提示词还有改进空间。")
        return False


if __name__ == "__main__":
    main()

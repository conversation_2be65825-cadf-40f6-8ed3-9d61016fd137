#!/usr/bin/env python3
"""
测试Video Agent输出格式更新
"""

import os
import logging
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_video_prompt_format():
    """测试Video Agent提示词格式更新"""
    logger.info("📝 测试Video Agent提示词格式更新...")
    
    try:
        # 读取更新后的提示词
        with open('/Users/<USER>/openArt-1/deer-flow/src/prompts/video_creator_prompt.md', 'r', encoding='utf-8') as f:
            prompt_content = f.read()
        
        logger.info(f"✅ 提示词文件读取成功")
        logger.info(f"📋 提示词长度: {len(prompt_content)} 字符")
        
        # 检查关键格式更新
        format_checks = {
            "包含ASSETS块": "<ASSETS>" in prompt_content and "</ASSETS>" in prompt_content,
            "移除旧JSON格式": '```json\n{\n  "video_path": {' not in prompt_content,
            "包含输出格式说明": "输出格式 (Output Format)" in prompt_content,
            "包含自然语言描述": "自然语言描述" in prompt_content,
            "包含结构化资产信息": "结构化资产信息" in prompt_content,
            "包含示例输出": "示例输出格式" in prompt_content,
            "包含资产信息要求": "资产信息要求" in prompt_content,
            "包含工具调用优先原则": "工具调用优先原则" in prompt_content,
            "包含video数组": '"video": [' in prompt_content
        }
        
        logger.info("🔍 提示词格式检查:")
        all_passed = True
        for check_name, passed in format_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}: {'包含' if passed else '缺失'}")
            if not passed:
                all_passed = False
        
        # 统计ASSETS块出现次数
        assets_count = prompt_content.count("<ASSETS>")
        logger.info(f"📊 ASSETS块出现次数: {assets_count}")
        
        # 检查是否完全移除了旧格式
        old_format_indicators = [
            '```json\n{\n  "video_path": {',
            '"value": "[工具实际返回的真实视频路径]"',
            '"video_url": {\n    "value":'
        ]
        
        old_format_found = any(indicator in prompt_content for indicator in old_format_indicators)
        
        if old_format_found:
            logger.warning("⚠️ 仍然包含旧的JSON格式")
            all_passed = False
        else:
            logger.info("✅ 已完全移除旧的JSON格式")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ 提示词格式测试失败: {e}")
        return False

def test_video_expert_output_parsing():
    """测试Video Expert输出解析功能"""
    logger.info("🔧 测试Video Expert输出解析功能...")
    
    try:
        from src.tools.experts import _parse_expert_output
        
        # 模拟Video Agent的新格式输出
        mock_video_output = """
我成功为您创作了一段震撼的科幻视频！这个视频采用了电影级的视觉效果，展现了未来城市的壮丽景象。镜头运用了缓慢的推进和旋转，营造出史诗般的氛围。整体色调偏向蓝紫色，充满科技感。

<ASSETS>
{
  "video": [
    {
      "url": "/path/to/futuristic_city.mp4",
      "name": "未来城市科幻大片",
      "description": "电影级科幻视频，展现未来城市景象，蓝紫色调，缓慢镜头运动，时长30秒，4K分辨率，史诗氛围。"
    }
  ],
  "duration": "30"
}
</ASSETS>
        """.strip()
        
        # 测试解析
        parsed_result = _parse_expert_output(mock_video_output, "video")
        
        logger.info("✅ 输出解析成功")
        logger.info(f"📋 解析结果类型: {type(parsed_result)}")
        
        # 验证解析结果结构
        structure_checks = {
            "包含success字段": "success" in parsed_result,
            "包含content字段": "content" in parsed_result,
            "包含assets字段": "assets" in parsed_result,
            "包含metadata字段": "metadata" in parsed_result,
            "assets包含video": "video" in parsed_result.get("assets", {}),
            "success为True": parsed_result.get("success") == True
        }
        
        logger.info("🔍 解析结果结构检查:")
        all_passed = True
        for check_name, passed in structure_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}: {'通过' if passed else '失败'}")
            if not passed:
                all_passed = False
        
        # 检查视频资产详情
        video_assets = parsed_result.get("assets", {}).get("video", [])
        if video_assets:
            video_asset = video_assets[0]
            logger.info("🎬 视频资产详情:")
            logger.info(f"   • URL: {video_asset.get('url', 'N/A')}")
            logger.info(f"   • 名称: {video_asset.get('name', 'N/A')}")
            logger.info(f"   • 描述: {video_asset.get('description', 'N/A')[:50]}...")
        
        # 检查其他字段
        duration = parsed_result.get("assets", {}).get("duration")
        if duration:
            logger.info(f"   • 时长: {duration}秒")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Video Expert输出解析测试失败: {e}")
        return False

def test_video_json_format_compatibility():
    """测试Video JSON格式兼容性"""
    logger.info("📄 测试Video JSON格式兼容性...")
    
    try:
        import json
        
        # 测试新格式的JSON部分是否可以正确解析
        test_video_assets_json = """
{
  "video": [
    {
      "url": "/path/to/test_video.mp4",
      "name": "测试视频",
      "description": "这是一个测试视频文件，用于验证JSON格式的正确性。包含科幻风格，时长30秒。"
    }
  ],
  "audio_path": "/path/to/background_music.mp3",
  "duration": "30"
}
        """.strip()
        
        # 尝试解析JSON
        parsed_json = json.loads(test_video_assets_json)
        
        logger.info("✅ JSON格式解析成功")
        
        # 验证JSON结构
        json_checks = {
            "包含video数组": "video" in parsed_json and isinstance(parsed_json["video"], list),
            "video数组非空": len(parsed_json.get("video", [])) > 0,
            "视频对象包含url": "url" in parsed_json["video"][0] if parsed_json.get("video") else False,
            "视频对象包含name": "name" in parsed_json["video"][0] if parsed_json.get("video") else False,
            "视频对象包含description": "description" in parsed_json["video"][0] if parsed_json.get("video") else False,
            "包含duration": "duration" in parsed_json,
            "包含audio_path": "audio_path" in parsed_json
        }
        
        logger.info("🔍 JSON结构检查:")
        all_passed = True
        for check_name, passed in json_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}: {'通过' if passed else '失败'}")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except json.JSONDecodeError as e:
        logger.error(f"❌ JSON解析失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ JSON格式兼容性测试失败: {e}")
        return False

def test_format_consistency_with_audio():
    """测试与Audio Agent格式一致性"""
    logger.info("🔄 测试与Audio Agent格式一致性...")
    
    try:
        # 读取Audio Agent提示词
        with open('/Users/<USER>/openArt-1/deer-flow/src/prompts/audio_creator_prompt.md', 'r', encoding='utf-8') as f:
            audio_prompt = f.read()
        
        # 读取Video Agent提示词
        with open('/Users/<USER>/openArt-1/deer-flow/src/prompts/video_creator_prompt.md', 'r', encoding='utf-8') as f:
            video_prompt = f.read()
        
        # 检查一致性
        consistency_checks = {
            "都使用ASSETS块": "<ASSETS>" in audio_prompt and "<ASSETS>" in video_prompt,
            "都有输出格式说明": "输出格式 (Output Format)" in audio_prompt and "输出格式 (Output Format)" in video_prompt,
            "都有自然语言描述": "自然语言描述" in audio_prompt and "自然语言描述" in video_prompt,
            "都有工具调用优先原则": "工具调用优先原则" in audio_prompt and "工具调用优先原则" in video_prompt,
            "都有示例输出": "示例输出格式" in audio_prompt and "示例输出格式" in video_prompt,
            "都有资产信息要求": "资产信息要求" in audio_prompt and "资产信息要求" in video_prompt
        }
        
        logger.info("🔍 格式一致性检查:")
        all_passed = True
        for check_name, passed in consistency_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}: {'一致' if passed else '不一致'}")
            if not passed:
                all_passed = False
        
        # 检查资产字段差异
        logger.info("📋 资产字段差异:")
        logger.info("   • Audio Agent: audio数组 + voice_id")
        logger.info("   • Video Agent: video数组 + audio_path + duration")
        logger.info("   ✅ 字段差异合理，符合各自功能特点")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ 格式一致性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🎯 开始Video Agent输出格式更新测试...")
    
    # 测试步骤
    tests = [
        ("提示词格式更新", test_video_prompt_format),
        ("Expert输出解析", test_video_expert_output_parsing),
        ("JSON格式兼容性", test_video_json_format_compatibility),
        ("与Audio Agent格式一致性", test_format_consistency_with_audio)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("📊 Video Agent输出格式更新测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 Video Agent输出格式更新完全成功！")
        logger.info("")
        logger.info("🚀 **更新效果:**")
        logger.info("   📝 提示词已更新为<ASSETS>格式，与Audio/Visual Agent保持一致")
        logger.info("   🔧 移除了旧的JSON格式，避免解析冲突")
        logger.info("   📋 添加了完整的输出格式说明和示例")
        logger.info("   🎯 Expert解析器可以正确处理新格式")
        logger.info("   ✅ JSON格式兼容性良好")
        logger.info("   🔄 与Audio Agent格式保持一致")
        logger.info("")
        logger.info("🎯 现在所有Expert Agent都使用统一的<ASSETS>格式：")
        logger.info("   • Visual Agent: <ASSETS> + image数组")
        logger.info("   • Audio Agent: <ASSETS> + audio数组 + voice_id")
        logger.info("   • Video Agent: <ASSETS> + video数组 + audio_path + duration")
        logger.info("")
        logger.info("🎬 Video Agent现在可以正确处理各种视频任务，不再出现JSON解析错误！")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()

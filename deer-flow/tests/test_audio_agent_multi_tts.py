#!/usr/bin/env python3
"""
测试Audio Agent对多人TTS工具的理解和使用
"""

import os
import asyncio
import logging
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_audio_agent_prompt_update():
    """测试Audio Agent提示词更新"""
    logger.info("📝 测试Audio Agent提示词更新...")
    
    try:
        # 读取更新后的提示词
        with open('/Users/<USER>/openArt-1/deer-flow/src/prompts/audio_creator_prompt.md', 'r', encoding='utf-8') as f:
            prompt_content = f.read()
        
        logger.info(f"✅ 提示词文件读取成功")
        logger.info(f"📋 提示词长度: {len(prompt_content)} 字符")
        
        # 检查关键内容是否包含
        key_checks = {
            "包含多人TTS工具": "multi_speaker_tts" in prompt_content,
            "包含工具描述": "专业多人对话音频生成工具" in prompt_content,
            "包含音色库信息": "46个系统音色" in prompt_content,
            "包含使用场景": "播客制作" in prompt_content and "有声书录制" in prompt_content,
            "包含推荐组合": "professional" in prompt_content and "presenter_male" in prompt_content,
            "包含参数控制": "语速" in prompt_content and "音量" in prompt_content,
            "包含工具选择指南": "工具选择指南" in prompt_content,
            "包含播客示例": "播客对话音频" in prompt_content,
            "包含有声书示例": "有声书角色对话" in prompt_content,
            "包含具体参数示例": "voice_mapping" in prompt_content and "dialogue_script" in prompt_content
        }
        
        logger.info("🔍 提示词内容检查:")
        all_passed = True
        for check_name, passed in key_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}: {'包含' if passed else '缺失'}")
            if not passed:
                all_passed = False
        
        # 统计示例数量
        example_count = prompt_content.count("任务示例")
        logger.info(f"📊 任务示例数量: {example_count}")
        
        # 检查工具描述的详细程度
        multi_tts_section = ""
        lines = prompt_content.split('\n')
        in_multi_tts_section = False
        
        for line in lines:
            if "多人对话音频生成" in line:
                in_multi_tts_section = True
            elif in_multi_tts_section and line.startswith('* **') and "多人对话" not in line:
                break
            elif in_multi_tts_section:
                multi_tts_section += line + '\n'
        
        logger.info(f"🎭 多人TTS工具描述长度: {len(multi_tts_section)} 字符")
        
        if len(multi_tts_section) > 500:
            logger.info("✅ 多人TTS工具描述详细充分")
        else:
            logger.warning("⚠️ 多人TTS工具描述可能不够详细")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ 提示词更新测试失败: {e}")
        return False

def test_audio_agent_tool_access():
    """测试Audio Agent是否能访问多人TTS工具"""
    logger.info("🔧 测试Audio Agent工具访问...")
    
    try:
        from src.agents.agents import audio_creator_agent
        
        # 检查Agent是否有工具
        if hasattr(audio_creator_agent, 'tools'):
            tools = audio_creator_agent.tools
            logger.info(f"✅ Audio Agent有 {len(tools)} 个工具")
            
            # 查找多人TTS工具
            multi_tts_tool = None
            for tool in tools:
                if hasattr(tool, 'name') and tool.name == 'multi_speaker_tts':
                    multi_tts_tool = tool
                    break
            
            if multi_tts_tool:
                logger.info("✅ 找到multi_speaker_tts工具")
                logger.info(f"📋 工具描述长度: {len(multi_tts_tool.description)} 字符")
                
                # 检查工具描述是否包含关键信息
                desc_checks = {
                    "包含完整音色库": "完整音色库" in multi_tts_tool.description,
                    "包含推荐组合": "推荐声音组合" in multi_tts_tool.description,
                    "包含使用场景": "播客制作" in multi_tts_tool.description,
                    "包含参数说明": "语速" in multi_tts_tool.description
                }
                
                logger.info("🔍 工具描述内容检查:")
                for check_name, passed in desc_checks.items():
                    status = "✅" if passed else "❌"
                    logger.info(f"   {status} {check_name}: {'包含' if passed else '缺失'}")
                
                return True
            else:
                logger.error("❌ 未找到multi_speaker_tts工具")
                
                # 列出所有可用工具
                logger.info("📋 可用工具列表:")
                for tool in tools:
                    tool_name = getattr(tool, 'name', 'Unknown')
                    logger.info(f"   • {tool_name}")
                
                return False
        else:
            logger.error("❌ Audio Agent没有tools属性")
            return False
        
    except Exception as e:
        logger.error(f"❌ Audio Agent工具访问测试失败: {e}")
        return False

def test_audio_agent_understanding():
    """测试Audio Agent对多人场景的理解"""
    logger.info("🧠 测试Audio Agent场景理解...")
    
    try:
        # 模拟不同的用户请求，看Agent是否能正确识别需要使用多人TTS
        test_scenarios = [
            {
                "request": "为我制作一个播客对话，主持人采访AI专家",
                "expected_tool": "multi_speaker_tts",
                "reason": "多人对话场景"
            },
            {
                "request": "创建一个有声书片段，包含旁白和角色对话",
                "expected_tool": "multi_speaker_tts", 
                "reason": "多角色音频"
            },
            {
                "request": "生成一段教学对话，老师和学生问答",
                "expected_tool": "multi_speaker_tts",
                "reason": "师生对话"
            },
            {
                "request": "为我朗读一篇文章",
                "expected_tool": "text_to_speech_generator",
                "reason": "单人朗读"
            },
            {
                "request": "创建一首背景音乐",
                "expected_tool": "suno_music_generation",
                "reason": "音乐生成"
            }
        ]
        
        logger.info("📋 场景识别测试:")
        
        for i, scenario in enumerate(test_scenarios, 1):
            request = scenario["request"]
            expected = scenario["expected_tool"]
            reason = scenario["reason"]
            
            # 简单的关键词匹配来模拟Agent的理解
            is_multi_speaker = any(keyword in request for keyword in [
                "对话", "播客", "采访", "问答", "角色", "主持人", "嘉宾", "老师", "学生", "旁白"
            ])
            
            is_music = any(keyword in request for keyword in [
                "音乐", "背景音", "配乐", "旋律"
            ])
            
            if is_music:
                predicted_tool = "suno_music_generation"
            elif is_multi_speaker:
                predicted_tool = "multi_speaker_tts"
            else:
                predicted_tool = "text_to_speech_generator"
            
            correct = predicted_tool == expected
            status = "✅" if correct else "❌"
            
            logger.info(f"   {status} 场景{i}: {request}")
            logger.info(f"      预期工具: {expected} ({reason})")
            logger.info(f"      预测工具: {predicted_tool}")
            
        logger.info("✅ 场景理解测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 场景理解测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🎯 开始Audio Agent多人TTS集成测试...")
    
    # 测试步骤
    tests = [
        ("提示词更新检查", test_audio_agent_prompt_update),
        ("工具访问测试", test_audio_agent_tool_access),
        ("场景理解测试", test_audio_agent_understanding)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("📊 Audio Agent多人TTS集成测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 Audio Agent多人TTS集成完全成功！")
        logger.info("")
        logger.info("🚀 **现在Audio Agent能够：**")
        logger.info("   🎭 识别多人对话场景并选择正确工具")
        logger.info("   📝 了解完整的46个音色库和推荐组合")
        logger.info("   🎯 根据场景自动选择最佳声音搭配")
        logger.info("   🔧 使用专业参数进行精细控制")
        logger.info("   📋 提供播客、有声书、教学等多种示例")
        logger.info("")
        logger.info("🎯 用户现在可以直接要求Audio Agent：")
        logger.info("   • '为我制作一个播客对话'")
        logger.info("   • '创建有声书角色对话'") 
        logger.info("   • '生成教学问答音频'")
        logger.info("   • '制作广告配音对话'")
        logger.info("")
        logger.info("Audio Agent会自动选择multi_speaker_tts工具并配置合适的参数！")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步优化")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试Audio Agent输出格式修复
"""

import os
import asyncio
import logging
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_audio_prompt_format():
    """测试Audio Agent提示词格式更新"""
    logger.info("📝 测试Audio Agent提示词格式更新...")
    
    try:
        # 读取更新后的提示词
        with open('/Users/<USER>/openArt-1/deer-flow/src/prompts/audio_creator_prompt.md', 'r', encoding='utf-8') as f:
            prompt_content = f.read()
        
        logger.info(f"✅ 提示词文件读取成功")
        logger.info(f"📋 提示词长度: {len(prompt_content)} 字符")
        
        # 检查关键格式更新
        format_checks = {
            "包含ASSETS块": "<ASSETS>" in prompt_content and "</ASSETS>" in prompt_content,
            "移除旧JSON格式": '```json\n{\n  "audio": {' not in prompt_content,
            "包含输出格式说明": "输出格式 (Output Format)" in prompt_content,
            "包含自然语言描述": "自然语言描述" in prompt_content,
            "包含结构化资产信息": "结构化资产信息" in prompt_content,
            "包含示例输出": "示例输出格式" in prompt_content,
            "包含资产信息要求": "资产信息要求" in prompt_content,
            "包含工具调用优先原则": "工具调用优先原则" in prompt_content
        }
        
        logger.info("🔍 提示词格式检查:")
        all_passed = True
        for check_name, passed in format_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}: {'包含' if passed else '缺失'}")
            if not passed:
                all_passed = False
        
        # 统计ASSETS块出现次数
        assets_count = prompt_content.count("<ASSETS>")
        logger.info(f"📊 ASSETS块出现次数: {assets_count}")
        
        # 检查是否完全移除了旧格式
        old_format_indicators = [
            '```json\n{\n  "audio": {',
            '"value": "[工具实际返回的真实音频URL]"',
            '"voice_id": {\n    "value":'
        ]
        
        old_format_found = any(indicator in prompt_content for indicator in old_format_indicators)
        
        if old_format_found:
            logger.warning("⚠️ 仍然包含旧的JSON格式")
            all_passed = False
        else:
            logger.info("✅ 已完全移除旧的JSON格式")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ 提示词格式测试失败: {e}")
        return False

def test_expert_output_parsing():
    """测试Expert输出解析功能"""
    logger.info("🔧 测试Expert输出解析功能...")
    
    try:
        from src.tools.experts import _parse_expert_output
        
        # 模拟Audio Agent的新格式输出
        mock_audio_output = """
我成功为您创作了一段关于哪吒送外卖的相声对话音频！这段音频包含了逗哏和捧哏两个角色的精彩对话，语调幽默风趣，节奏紧凑，完美展现了传统相声的魅力与现代生活的结合。

<ASSETS>
{
  "audio": [
    {
      "url": "https://example.com/nezha_delivery_comedy.mp3",
      "name": "哪吒送外卖相声对话",
      "description": "传统相声风格的多人对话音频，包含逗哏和捧哏两个角色，主题为哪吒在美团送外卖的幽默故事，时长约5分钟，语调风趣幽默。"
    }
  ]
}
</ASSETS>
        """.strip()
        
        # 测试解析
        parsed_result = _parse_expert_output(mock_audio_output, "audio")
        
        logger.info("✅ 输出解析成功")
        logger.info(f"📋 解析结果类型: {type(parsed_result)}")
        
        # 验证解析结果结构
        structure_checks = {
            "包含success字段": "success" in parsed_result,
            "包含content字段": "content" in parsed_result,
            "包含assets字段": "assets" in parsed_result,
            "包含metadata字段": "metadata" in parsed_result,
            "assets包含audio": "audio" in parsed_result.get("assets", {}),
            "success为True": parsed_result.get("success") == True
        }
        
        logger.info("🔍 解析结果结构检查:")
        all_passed = True
        for check_name, passed in structure_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}: {'通过' if passed else '失败'}")
            if not passed:
                all_passed = False
        
        # 检查音频资产详情
        audio_assets = parsed_result.get("assets", {}).get("audio", [])
        if audio_assets:
            audio_asset = audio_assets[0]
            logger.info("🎵 音频资产详情:")
            logger.info(f"   • URL: {audio_asset.get('url', 'N/A')}")
            logger.info(f"   • 名称: {audio_asset.get('name', 'N/A')}")
            logger.info(f"   • 描述: {audio_asset.get('description', 'N/A')[:50]}...")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Expert输出解析测试失败: {e}")
        return False

def test_json_format_compatibility():
    """测试JSON格式兼容性"""
    logger.info("📄 测试JSON格式兼容性...")
    
    try:
        import json
        
        # 测试新格式的JSON部分是否可以正确解析
        test_assets_json = """
{
  "audio": [
    {
      "url": "https://example.com/test.mp3",
      "name": "测试音频",
      "description": "这是一个测试音频文件，用于验证JSON格式的正确性。"
    }
  ],
  "voice_id": "test_voice_123"
}
        """.strip()
        
        # 尝试解析JSON
        parsed_json = json.loads(test_assets_json)
        
        logger.info("✅ JSON格式解析成功")
        
        # 验证JSON结构
        json_checks = {
            "包含audio数组": "audio" in parsed_json and isinstance(parsed_json["audio"], list),
            "audio数组非空": len(parsed_json.get("audio", [])) > 0,
            "音频对象包含url": "url" in parsed_json["audio"][0] if parsed_json.get("audio") else False,
            "音频对象包含name": "name" in parsed_json["audio"][0] if parsed_json.get("audio") else False,
            "音频对象包含description": "description" in parsed_json["audio"][0] if parsed_json.get("audio") else False,
            "包含voice_id": "voice_id" in parsed_json
        }
        
        logger.info("🔍 JSON结构检查:")
        all_passed = True
        for check_name, passed in json_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}: {'通过' if passed else '失败'}")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except json.JSONDecodeError as e:
        logger.error(f"❌ JSON解析失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ JSON格式兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🎯 开始Audio Agent输出格式修复测试...")
    
    # 测试步骤
    tests = [
        ("提示词格式更新", test_audio_prompt_format),
        ("Expert输出解析", test_expert_output_parsing),
        ("JSON格式兼容性", test_json_format_compatibility)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("📊 Audio Agent输出格式修复测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 Audio Agent输出格式修复完全成功！")
        logger.info("")
        logger.info("🚀 **修复效果:**")
        logger.info("   📝 提示词已更新为<ASSETS>格式，与Visual Agent保持一致")
        logger.info("   🔧 移除了旧的JSON格式，避免解析冲突")
        logger.info("   📋 添加了完整的输出格式说明和示例")
        logger.info("   🎯 Expert解析器可以正确处理新格式")
        logger.info("   ✅ JSON格式兼容性良好")
        logger.info("")
        logger.info("🎯 现在Audio Agent应该能够正确处理相声剧本任务了！")
        logger.info("   • 不再出现'Extra data'JSON解析错误")
        logger.info("   • 输出格式与其他Expert保持一致")
        logger.info("   • 支持多人TTS工具的完整功能")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()

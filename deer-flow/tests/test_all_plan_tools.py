#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全面检查所有Plan相关工具

验证所有工具都正确使用UnifiedPlan，没有遗留的Legacy Plan或Enhanced Plan引用。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
from src.graph_v2.types import State


def test_planning_tools():
    """测试规划工具"""
    print("🧪 测试规划工具...")
    
    try:
        from src.tools.planning import planner_tool, reviser_tool
        
        # 检查返回类型注解
        import inspect
        
        planner_sig = inspect.signature(planner_tool.func)
        planner_return = planner_sig.return_annotation
        print(f"   planner_tool返回类型: {planner_return}")
        
        reviser_sig = inspect.signature(reviser_tool.func)
        reviser_return = reviser_sig.return_annotation
        print(f"   reviser_tool返回类型: {reviser_return}")
        
        # 验证返回类型包含UnifiedPlan
        if "UnifiedPlan" in str(planner_return):
            print("   ✅ planner_tool使用UnifiedPlan")
        else:
            print("   ❌ planner_tool未使用UnifiedPlan")
            return False
            
        if "UnifiedPlan" in str(reviser_return):
            print("   ✅ reviser_tool使用UnifiedPlan")
        else:
            print("   ❌ reviser_tool未使用UnifiedPlan")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 规划工具测试失败: {e}")
        return False


def test_template_tools():
    """测试模板工具"""
    print("\n🧪 测试模板工具...")
    
    try:
        from src.tools.template_tools import (
            recommend_template,
            get_available_templates,
            create_plan_from_template,
            validate_template_params
        )
        
        print("   ✅ 模板工具导入成功")
        
        # 测试create_plan_from_template的返回类型
        import inspect
        sig = inspect.signature(create_plan_from_template.func)
        return_annotation = sig.return_annotation
        print(f"   create_plan_from_template返回类型: {return_annotation}")
        
        # 检查工具描述中是否提到UnifiedPlan
        if hasattr(create_plan_from_template, 'description'):
            desc = create_plan_from_template.description
            if "UnifiedPlan" in desc or "unified" in desc.lower():
                print("   ✅ create_plan_from_template描述提到统一模型")
            else:
                print("   ⚠️ create_plan_from_template描述未明确提到统一模型")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模板工具测试失败: {e}")
        return False


def test_state_management_tools():
    """测试状态管理工具"""
    print("\n🧪 测试状态管理工具...")
    
    try:
        from src.tools.state_management import (
            get_current_plan,
            get_next_pending_step,
            update_step_status,
            resolve_step_inputs,
            check_execution_status,
            new_state_management_tools,
            legacy_state_management_tools
        )
        
        print(f"   ✅ 状态管理工具导入成功")
        print(f"   新工具数量: {len(new_state_management_tools)}")
        print(f"   遗留工具数量: {len(legacy_state_management_tools)}")
        
        # 检查工具是否使用ensure_unified_plan
        import inspect
        
        # 检查get_current_plan的源码
        source = inspect.getsource(get_current_plan.func)
        if "ensure_unified_plan" in source:
            print("   ✅ get_current_plan使用ensure_unified_plan")
        else:
            print("   ❌ get_current_plan未使用ensure_unified_plan")
            return False
        
        # 检查get_next_pending_step的源码
        source = inspect.getsource(get_next_pending_step.func)
        if "ensure_unified_plan" in source:
            print("   ✅ get_next_pending_step使用ensure_unified_plan")
        else:
            print("   ❌ get_next_pending_step未使用ensure_unified_plan")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 状态管理工具测试失败: {e}")
        return False


def test_execution_engine():
    """测试执行引擎"""
    print("\n🧪 测试执行引擎...")
    
    try:
        from src.graph_v2.execution_engine import should_use_execution_engine, execution_engine_node
        
        # 检查should_use_execution_engine的源码
        import inspect
        source = inspect.getsource(should_use_execution_engine)
        
        if "ensure_unified_plan" in source:
            print("   ✅ should_use_execution_engine使用ensure_unified_plan")
        else:
            print("   ❌ should_use_execution_engine未使用ensure_unified_plan")
            return False
        
        if "UnifiedPlan" in source:
            print("   ✅ should_use_execution_engine引用UnifiedPlan")
        else:
            print("   ❌ should_use_execution_engine未引用UnifiedPlan")
            return False
        
        # 测试路由逻辑
        test_plan = UnifiedPlan(
            original_task="测试任务",
            steps=[
                UnifiedStep(
                    step_id="step1",
                    name="步骤1",
                    description="测试步骤1",
                    tool_to_use="visual_expert"
                ),
                UnifiedStep(
                    step_id="step2",
                    name="步骤2",
                    description="测试步骤2",
                    tool_to_use="audio_expert"
                )
            ]
        )
        
        state = {"plan": test_plan}
        result = should_use_execution_engine(state)
        
        if result == True:
            print("   ✅ 路由逻辑正确：多步计划使用执行引擎")
        else:
            print("   ❌ 路由逻辑错误：多步计划未使用执行引擎")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 执行引擎测试失败: {e}")
        return False


def test_migration_utils():
    """测试迁移工具"""
    print("\n🧪 测试迁移工具...")
    
    try:
        from src.graph_v2.migration_utils import (
            ensure_unified_plan,
            migrate_legacy_plan_to_unified,
            auto_migrate_plan
        )
        
        print("   ✅ 迁移工具导入成功")
        
        # 测试ensure_unified_plan
        test_plan = UnifiedPlan(
            original_task="测试",
            steps=[]
        )
        
        result = ensure_unified_plan(test_plan)
        if isinstance(result, UnifiedPlan):
            print("   ✅ ensure_unified_plan正确处理UnifiedPlan")
        else:
            print("   ❌ ensure_unified_plan处理UnifiedPlan失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 迁移工具测试失败: {e}")
        return False


def test_no_enhanced_references():
    """检查是否还有Enhanced模型的引用"""
    print("\n🧪 检查Enhanced模型引用...")
    
    try:
        # 尝试导入Enhanced模型（应该失败）
        try:
            from src.graph_v2.enhanced_models import EnhancedPlan
            print("   ❌ Enhanced模型仍然存在")
            return False
        except ImportError:
            print("   ✅ Enhanced模型已正确移除")
        
        # 检查enhanced_state_management是否已删除
        try:
            from src.tools.enhanced_state_management import enhanced_state_management_tools
            print("   ❌ enhanced_state_management仍然存在")
            return False
        except ImportError:
            print("   ✅ enhanced_state_management已正确移除")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Enhanced引用检查失败: {e}")
        return False


def test_unified_plan_functionality():
    """测试UnifiedPlan功能完整性"""
    print("\n🧪 测试UnifiedPlan功能...")
    
    try:
        # 创建测试计划
        plan = UnifiedPlan(
            original_task="功能测试",
            steps=[
                UnifiedStep(
                    step_id="step1",
                    name="步骤1",
                    description="测试步骤1",
                    tool_to_use="visual_expert"
                ),
                UnifiedStep(
                    step_id="step2",
                    name="步骤2",
                    description="测试步骤2",
                    tool_to_use="audio_expert",
                    dependencies=["step1"]
                )
            ]
        )
        
        # 测试基本功能
        assert not plan.is_complete()
        assert not plan.has_failed()
        
        executable_steps = plan.get_next_executable_steps()
        assert len(executable_steps) == 1
        assert executable_steps[0].step_id == "step1"
        
        # 完成第一步
        step1 = plan.get_step("step1")
        step1.mark_completed({"result": "success"})
        
        # 检查进度
        progress = plan.get_progress_info()
        assert progress["completed_steps"] == 1
        assert progress["progress_percentage"] == 50.0
        
        # 获取下一步
        executable_steps = plan.get_next_executable_steps()
        assert len(executable_steps) == 1
        assert executable_steps[0].step_id == "step2"
        
        print("   ✅ UnifiedPlan所有功能正常")
        return True
        
    except Exception as e:
        print(f"   ❌ UnifiedPlan功能测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 开始全面检查Plan相关工具\n")
    
    tests = [
        ("规划工具", test_planning_tools),
        ("模板工具", test_template_tools),
        ("状态管理工具", test_state_management_tools),
        ("执行引擎", test_execution_engine),
        ("迁移工具", test_migration_utils),
        ("Enhanced引用检查", test_no_enhanced_references),
        ("UnifiedPlan功能", test_unified_plan_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有Plan工具检查通过！")
        print("\n📋 检查总结:")
        print("1. ✅ 所有工具都使用UnifiedPlan")
        print("2. ✅ Enhanced模型已完全移除")
        print("3. ✅ 路由逻辑正确工作")
        print("4. ✅ 迁移工具功能正常")
        print("5. ✅ UnifiedPlan功能完整")
        return True
    else:
        print("⚠️ 部分检查失败，需要进一步修复。")
        return False


if __name__ == "__main__":
    main()

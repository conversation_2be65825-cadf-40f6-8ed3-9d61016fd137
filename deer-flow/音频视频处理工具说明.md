# 音频视频处理工具说明

## 📁 整理后的目录结构

```
deer-flow/
├── audio_video_tools/          # 音频视频处理工具
│   ├── ffmpeg_audio_splitter.py      # 基础音频分割工具
│   ├── smart_audio_splitter.py       # 智能音频分割工具
│   ├── video_character_clipper.py    # 视频角色特写切片工具
│   ├── enhance_female_smart.py       # 女主角音频增强工具
│   ├── extract_character_clips.py    # 快速视频切片工具
│   └── run_split.py                  # 快速音频分割工具
├── final_outputs/              # 最终输出结果
│   ├── speaker_audio/                # 原始音频分割结果
│   ├── smart_audio/                  # 智能音频分割结果
│   ├── ultra_clean_audio/            # 超净化音频结果
│   └── character_clips/              # 视频角色特写片段
├── docs/                       # 文档目录
│   ├── 音频分割使用说明.md
│   ├── 音频分割结果总结.md
│   ├── 女主角音频对比说明.md
│   └── 女主角音频三版本对比.md
├── tests/                      # 测试脚本
│   └── test_json_structure.py
└── real_tool_call_result.json  # 原始数据文件
```

## 🛠️ 工具说明

### 音频处理工具

1. **ffmpeg_audio_splitter.py** - 基础音频分割
   - 按说话人分割音频
   - 使用ffmpeg处理
   - 支持自定义时长

2. **smart_audio_splitter.py** - 智能音频分割
   - 多维度评分算法
   - 智能选择高质量片段
   - 严格过滤噪音

3. **enhance_female_smart.py** - 女主角音频增强
   - 专门处理女主角音频
   - 超严格筛选标准
   - 生成超净化版本

4. **run_split.py** - 快速音频分割
   - 一键运行音频分割
   - 自动检测环境
   - 简化操作流程

### 视频处理工具

1. **video_character_clipper.py** - 视频角色特写切片
   - 智能识别角色类型
   - 为每个角色生成特写片段
   - 支持自定义片段数量

2. **extract_character_clips.py** - 快速视频切片
   - 自动查找视频文件
   - 一键提取角色特写
   - 简化操作流程

## 📊 最终输出结果

### 音频输出 (final_outputs/)

1. **speaker_audio/** - 原始版本
   - 按时间顺序分割
   - 每个说话人约1分钟

2. **smart_audio/** - 智能版本
   - 多维度评分选择
   - 优化音质和连贯性

3. **ultra_clean_audio/** - 超净化版本 ⭐
   - 超严格筛选
   - 只保留最高质量片段
   - 推荐使用

### 视频输出 (final_outputs/)

1. **character_clips/** - 角色特写片段
   - 说话人1_心理医生/ (5个片段)
   - 说话人3_患者家属/ (5个片段)  
   - 说话人4_患者/ (5个片段)

## 🎯 推荐使用

### 音频
- **最佳选择**: `final_outputs/ultra_clean_audio/说话人3_主持人_智能版_11.6秒.wav`
- **特点**: 11.6秒精华内容，无杂音，语音清晰

### 视频
- **心理医生**: `final_outputs/character_clips/说话人1_心理医生/`
- **患者家属**: `final_outputs/character_clips/说话人3_患者家属/`
- **患者**: `final_outputs/character_clips/说话人4_患者/`

## 🔧 使用方法

### 重新生成音频
```bash
cd audio_video_tools
python enhance_female_smart.py  # 生成女主角超净化音频
python run_split.py             # 生成所有说话人音频
```

### 重新生成视频
```bash
cd audio_video_tools
python extract_character_clips.py  # 提取角色特写片段
```

## 📝 技术特点

- ✅ 智能评分算法
- ✅ 严格质量控制
- ✅ 自动角色识别
- ✅ 多格式支持
- ✅ 高质量输出

## 🗑️ 已清理的文件

删除了以下临时和重复文件：
- Python缓存文件 (__pycache__)
- 重复的音频处理脚本
- 临时测试文件
- 中间处理结果

现在目录结构更加清晰，便于使用和维护！

# 通用相声对话模板 - 技术需求与准备清单

## 📋 项目概述

基于Open-NotebookLM的Cross Talk技术，实现通用的相声对话生成模板。该模板能够：
- 生成专业的双人相声脚本
- 合成多角色语音（逗哏/捧哏）
- 生成精确的时间戳数据
- 智能匹配视频片段
- 合成完整的相声视频

## 🎯 核心技术架构

### 1. 脚本适配层 (CrossTalkAdapter)
- **功能**：将主题转换为标准相声格式
- **输入**：主题、风格、角色名等参数
- **输出**：带语调标记的相声脚本
- **格式**：`[语调] 角色名：台词内容`

### 2. 语音合成层 (CrossTalkSynth)
- **功能**：多角色语音合成
- **技术**：零样本语音克隆（如CosyVoice）
- **输入**：相声脚本 + 语音样本
- **输出**：分段音频 + 完整音频

### 3. 时间戳生成层 (CrossTalkConversion)
- **功能**：生成毫秒级精确时间戳
- **输入**：分段音频文件
- **输出**：timestamps.json文件
- **精度**：毫秒级（3位小数）

### 4. 视频匹配层 (VideoConversion + VideoEditor)
- **功能**：智能视频片段匹配
- **策略**：说话人匹配 + 语调匹配 + 内容匹配
- **输出**：同步的音视频作品

## 🛠️ 需要准备的资源

### 1. 语音样本资源 ⭐⭐⭐
```
voice_samples/
├── 逗哏演员/
│   ├── natural.wav     # 自然语调样本（3-5秒，清晰录音）
│   ├── natural.lab     # 对应文本标注
│   ├── emphatic.wav    # 强调语调样本（3-5秒）
│   ├── emphatic.lab    # 对应文本标注
│   ├── confused.wav    # 困惑语调样本（3-5秒）
│   └── confused.lab    # 对应文本标注
└── 捧哏演员/
    └── (相同结构)
```

**录音要求**：
- 采样率：16kHz或以上
- 格式：WAV无损
- 时长：每个样本3-5秒
- 质量：无背景噪音，发音清晰
- 内容：自然的相声台词，不是朗读

### 2. 视频素材资源 ⭐⭐
```
video_assets/
├── 逗哏演员片段/
│   ├── speaking_natural_01.mp4    # 自然说话片段（30秒）
│   ├── speaking_emphatic_01.mp4   # 强调说话片段（30秒）
│   ├── listening_01.mp4           # 倾听反应片段（30秒）
│   └── gesturing_01.mp4           # 手势表达片段（30秒）
├── 捧哏演员片段/
│   ├── speaking_confused_01.mp4   # 困惑说话片段（30秒）
│   ├── speaking_natural_01.mp4    # 自然说话片段（30秒）
│   ├── reacting_01.mp4            # 反应片段（30秒）
│   └── nodding_01.mp4             # 点头认同片段（30秒）
└── 场景片段/
    ├── stage_wide_01.mp4          # 舞台全景（30秒）
    ├── audience_reaction_01.mp4   # 观众反应（30秒）
    └── close_up_01.mp4            # 特写镜头（30秒）
```

**视频要求**：
- 分辨率：1080p或以上
- 格式：MP4
- 帧率：25fps或30fps
- 时长：每个片段30秒左右
- 内容：真实的相声表演片段

## 🔧 需要开发的新功能

### 1. 多角色语音合成工具 ✅ **已有**
**当前状态**：deer-flow已支持多角色语音合成，且生成结果自带时间戳！
**现有能力**：
- ✅ 支持多个角色的语音合成
- ✅ 自动生成精确时间戳
- ✅ 角色声音区分
- ✅ 高质量语音输出

**需要适配**：
- 🔧 相声脚本格式解析（`[语调] 角色名：台词`）
- 🔧 语调控制集成（Natural/Emphatic/Confused）
- 🔧 相声对话节奏优化

**集成方案**：
```python
# 直接使用现有的audio_expert
def generate_crosstalk_audio(script_text, dou_gen_name, peng_gen_name):
    """
    利用现有多角色语音合成能力生成相声音频
    输入：解析后的相声脚本
    输出：多角色音频 + 精确时间戳
    """
    return audio_expert.synthesize_multi_voice(
        script=script_text,
        characters=[dou_gen_name, peng_gen_name],
        include_timestamps=True
    )
```

### 2. 音频时长分析工具 ✅ **已有**
**当前状态**：多角色语音合成的结果已自带精确时间戳！
**现有能力**：
- ✅ 自动生成毫秒级精度时间戳
- ✅ 每句话的精确时长信息
- ✅ 累积时间戳计算

**无需额外开发**：时间戳功能已集成在多角色语音合成中

### 3. 相声脚本解析器 ⭐⭐
**功能**：解析相声脚本格式
**输入**：`[Natural] 张三：各位观众朋友们...`
**输出**：结构化数据

```python
def parse_crosstalk_script(script_text):
    lines = script_text.strip().split('\n')
    parsed_lines = []
    
    for line in lines:
        # 解析格式：[语调] 角色名：台词内容
        match = re.match(r'\[(\w+)\]\s*([^：]+)：(.+)', line)
        if match:
            tone, role, text = match.groups()
            parsed_lines.append({
                'tone': tone.lower(),
                'role': role.strip(),
                'text': text.strip()
            })
    
    return parsed_lines
```

### 4. 说话人画面匹配算法 ⭐⭐⭐
**核心问题**：确保说话人与画面对应
**解决方案**：
- 基于角色的视频片段分类
- 语调与表情的匹配
- 视觉连贯性保证

```python
class SpeakerVideoMatcher:
    def __init__(self):
        self.video_segments = {
            'dou_gen': {...},  # 逗哏演员的视频片段
            'peng_gen': {...}  # 捧哏演员的视频片段
        }
    
    def match_video_segment(self, speaker, tone, content):
        """根据说话人、语调、内容匹配视频片段"""
        
    def ensure_visual_coherence(self, segments):
        """确保视觉连贯性"""
```

## 📊 实现优先级（更新版）

### 🚀 **大幅简化！多角色语音+时间戳已有**

### 第一阶段：立即可实现 (1周)
1. ✅ 相声脚本生成（使用现有LLM）
2. ✅ 多角色语音合成（现有能力）
3. ✅ 精确时间戳生成（现有能力）
4. 🔧 相声脚本解析器（简单适配）
5. 🔧 基础视频合成（使用现有video_expert）

### 第二阶段：核心功能 (1-2周)
1. 🔧 说话人画面匹配算法
2. 🔧 语调与表情匹配
3. 🔧 视觉连贯性优化
4. 🔧 相声专业效果

### 第三阶段：完善优化 (1周)
1. 🔧 质量评估和调优
2. 🔧 用户体验优化
3. 🔧 性能提升
4. 🔧 错误处理完善

**总开发时间：从原来的7-9周缩短到3-4周！**

## 🎯 技术挑战与解决方案

### 挑战1：多角色语音区分
**问题**：如何让不同角色的声音有明显区别
**解决方案**：
- 使用不同的语音样本
- 调整音调和语速
- 后期音效处理

### 挑战2：精确时间同步
**问题**：音视频同步的毫秒级精度
**解决方案**：
- 使用soundfile精确计算音频时长
- 视频剪辑时严格按照时间戳
- 添加同步检查机制

### 挑战3：说话人画面匹配
**问题**：避免张冠李戴
**解决方案**：
- 预先标记视频片段的角色信息
- 基于时间戳的智能切换
- 人工审核机制

## 📈 预期效果

### 技术指标
- **语音质量**：MOS评分 > 4.0
- **同步精度**：误差 < 50ms
- **角色区分度**：> 85%
- **视觉匹配度**：> 80%

### 用户体验
- 一键生成专业相声视频
- 支持自定义主题和角色
- 生成速度：5-10分钟
- 输出质量：接近真人表演

## 🚀 下一步行动

1. **立即开始**：创建相声脚本解析器
2. **准备资源**：收集语音样本和视频素材
3. **扩展audio_expert**：支持多角色语音合成
4. **测试验证**：小规模功能测试
5. **迭代优化**：根据测试结果改进

这个项目将是deer-flow系统的一个重要里程碑，展示了AI在传统艺术创新方面的巨大潜力！

## 🔗 集成到现有系统

### 1. 注册相声模板
```python
# 在 src/templates/builtin_templates.py 中添加
from .crosstalk_template import get_crosstalk_template

def get_builtin_templates():
    templates = {}
    # ... 现有模板 ...

    # 添加相声模板
    templates["universal_crosstalk"] = get_crosstalk_template()

    return templates
```

### 2. 集成相声工具
```python
# 在 src/graph_v2/nodes.py 中添加相声工具
from src.tools.crosstalk_tools import CROSSTALK_TOOLS

def master_agent_node(state: State, config: RunnableConfig):
    # ... 现有工具 ...

    # 添加相声工具
    all_tools.extend(CROSSTALK_TOOLS)

    # ... 其余代码 ...
```

### 3. 扩展audio_expert支持多角色
```python
# 需要扩展 src/tools/audio.py
class MultiVoiceAudioExpert:
    def __init__(self):
        self.voice_models = {}

    def synthesize_crosstalk_dialogue(self, parsed_script, voice_samples):
        """合成相声对话"""
        # 实现多角色语音合成
        pass
```

### 4. 测试相声模板
```bash
# 启动相声模板
python interactive_chat.py --template universal_crosstalk

# 提供参数
topic: 网购
style: 现代幽默
dou_gen_name: 张三
peng_gen_name: 李四
```

# DeerFlow Plan和模板系统重构文档

## 📋 重构概述

本文档记录了DeerFlow系统的完整重构工作，包括统一数据模型、执行引擎、模板系统、工具简化等所有改进内容。

### � 重构目标
- **工具简化**: 从19个工具减少到8个 (减少58%)
- **模型统一**: 设计UnifiedPlan统一数据模型
- **执行优化**: 重构执行引擎支持多步任务
- **模板系统**: 构建可扩展的模板引擎
- **提示词优化**: 提升LLM输出质量

### 📊 重构成果对比

| 方面 | 重构前 | 重构后 | 改进 |
|------|--------|--------|------|
| 工具数量 | 19个 | 8个 | 减少58% |
| 步骤描述 | "步骤1"、"步骤2" | "为北京创建旅游海报，突出故宫长城" | 质量大幅提升 |
| 执行引擎 | 未正确使用 | 智能路由，正确执行 | 功能完全修复 |
| 状态管理 | 复杂消息解析 | 简单全局状态 | 架构大幅简化 |
| 模板支持 | 基础功能 | 完整模板引擎 | 功能显著增强 |

## 🏗️ 系统架构

```mermaid
graph TB
    A[用户任务] --> B{智能路由}
    B -->|简单任务| C[Master Agent]
    B -->|复杂任务| D[create_plan]
    B -->|模板任务| E[use_template]

    D --> F[UnifiedPlan]
    E --> G[模板渲染] --> F
    F --> H[执行引擎]

    H --> I[循环执行]
    I --> C
    C --> J[专家工具]
    J --> K[状态更新]
    K --> L{完成?}
    L -->|否| I
    L -->|是| M[任务完成]
```

## 🎯 UnifiedPlan统一数据模型

### 设计目标

1. **统一性** - 替代多种不同的计划格式
2. **扩展性** - 支持模板、依赖关系、重试机制
3. **可追踪性** - 完整的执行状态和时间记录
4. **兼容性** - 与现有系统平滑集成

### 核心模型定义

#### UnifiedStep - 统一步骤模型

```python
class UnifiedStep(BaseModel):
    # 基础信息
    step_id: str                    # 步骤唯一标识符
    name: str                       # 步骤名称
    description: str                # 详细描述
    tool_to_use: str               # 使用的工具名称
    inputs: Dict[str, Any]         # 工具输入参数
    
    # 执行控制
    status: StepStatus             # 执行状态 (pending/in_progress/completed/failed)
    dependencies: List[str]        # 依赖的步骤ID列表
    max_retries: int = 3           # 最大重试次数
    retry_count: int = 0           # 当前重试次数
    
    # 时间追踪
    created_at: datetime           # 创建时间
    started_at: Optional[datetime] # 开始执行时间
    completed_at: Optional[datetime] # 完成时间
    
    # 执行结果
    result: Optional[Dict[str, Any]] # 执行结果数据
```

#### UnifiedPlan - 统一计划模型

```python
class UnifiedPlan(BaseModel):
    # 基础信息
    plan_id: str                   # 计划唯一标识符
    original_task: str             # 原始任务描述
    steps: List[UnifiedStep]       # 步骤列表
    
    # 模板支持
    template_id: Optional[str]     # 模板ID（如果来自模板）
    template_params: Optional[Dict[str, Any]] # 模板参数
    is_from_template: bool = False # 是否来自模板
    
    # 执行状态
    current_step_index: int = 0    # 当前执行步骤索引
    created_at: datetime           # 创建时间
    step_results: Dict[str, Any]   # 步骤结果汇总
```

### 核心方法

#### 步骤管理
- `get_step(step_id: str)` - 根据ID获取步骤
- `get_next_executable_steps()` - 获取可执行的步骤列表
- `update_step_result(step_id: str, result: Dict)` - 更新步骤结果

#### 状态查询
- `is_complete()` - 检查计划是否完成
- `get_progress_info()` - 获取执行进度信息
- `get_pending_steps()` - 获取待执行步骤
- `get_failed_steps()` - 获取失败步骤

#### 步骤状态管理
- `mark_step_started(step_id: str)` - 标记步骤开始
- `mark_step_completed(step_id: str, result: Dict)` - 标记步骤完成
- `mark_step_failed(step_id: str, error: str)` - 标记步骤失败

## 🎨 模板系统

### 设计理念

模板系统旨在为常见的创作任务提供预定义的工作流程，提高效率和一致性。

### 模板定义结构

#### Template - 模板定义

```python
class Template(BaseModel):
    template_id: str               # 模板唯一标识符
    name: str                      # 模板名称
    description: str               # 模板描述
    category: str                  # 模板分类
    
    step_templates: List[StepTemplate] # 步骤模板列表
    required_params: List[str]     # 必需参数
    optional_params: List[str]     # 可选参数
    
    created_at: datetime           # 创建时间
    version: str = "1.0"           # 模板版本
```

#### StepTemplate - 步骤模板

```python
class StepTemplate(BaseModel):
    step_id: str                   # 步骤ID模板
    name: str                      # 步骤名称模板
    description: str               # 描述模板（支持Jinja2语法）
    tool_to_use: str              # 使用的工具
    inputs: Dict[str, Any]         # 输入参数模板
    dependencies: List[str]        # 依赖关系
```

### 内置模板

#### 1. 城市海报系列模板 (city_poster_series)

**用途**: 为多个城市创建旅游海报

**参数**:
- `cities: List[str]` - 城市列表
- `style: str` - 设计风格 (modern/classic/artistic)
- `theme: str` - 主题 (tourism/culture/business)

**生成步骤**: 为每个城市生成一个海报创作步骤

#### 2. AI鬼畜视频模板 (ai_parody_video)

**用途**: 创建角色主题的鬼畜视频

**参数**:
- `character: str` - 角色名称
- `style: str` - 视频风格
- `duration: int` - 视频时长

**生成步骤**: 角色素材 → 背景音乐 → 视频合成

#### 3. 品牌设计套件模板 (brand_design_suite)

**用途**: 完整的品牌设计方案

**参数**:
- `brand_name: str` - 品牌名称
- `industry: str` - 行业类型
- `style: str` - 设计风格

**生成步骤**: Logo设计 → 宣传海报 → 品牌视频

### 模板渲染引擎

#### TemplateRenderer类

```python
class TemplateRenderer:
    def __init__(self):
        self.env = Environment(loader=BaseLoader())
    
    def render_plan(self, template: Template, params: Dict[str, Any]) -> UnifiedPlan:
        """将模板渲染为UnifiedPlan"""
        
    def _render_step(self, step_template: StepTemplate, params: Dict[str, Any]) -> UnifiedStep:
        """渲染单个步骤模板"""
        
    def _validate_params(self, template: Template, params: Dict[str, Any]) -> Dict[str, Any]:
        """验证模板参数"""
```

#### 渲染过程

1. **参数验证** - 检查必需参数是否提供
2. **模板解析** - 使用Jinja2解析模板字符串
3. **步骤生成** - 为每个步骤模板生成UnifiedStep
4. **依赖处理** - 解析和设置步骤依赖关系
5. **计划创建** - 生成完整的UnifiedPlan

## ⚙️ 执行引擎

### 设计原则

1. **智能路由** - 根据计划复杂度选择执行策略
2. **状态驱动** - 基于步骤状态进行执行控制
3. **错误恢复** - 支持步骤重试和错误处理
4. **进度追踪** - 实时监控执行进度

### 路由决策逻辑

```python
def should_use_execution_engine(state: State) -> bool:
    """
    判断是否需要使用执行引擎
    
    规则：
    1. 没有计划 → 不需要
    2. 空计划 → 不需要
    3. 单步计划 → 不需要（Master Agent可以处理）
    4. 模板计划 → 需要（预定义的多步流程）
    5. 多步自定义计划 → 需要
    """
```

### 执行流程

1. **计划验证** - 检查计划完整性和有效性
2. **步骤调度** - 根据依赖关系确定执行顺序
3. **循环执行** - 持续执行直到计划完成
4. **状态更新** - 实时更新步骤和计划状态
5. **错误处理** - 处理执行失败和重试逻辑

### 执行引擎核心方法

```python
def execution_engine_node(state: State, config: RunnableConfig = None) -> State:
    """
    执行引擎节点：驱动Master Agent持续执行计划直到完成
    
    职责：
    1. 循环调用Master Agent
    2. 监控计划执行进度
    3. 强制继续执行未完成的计划
    4. 防止Master Agent提前停止
    """
```

## 🔧 工具集成

### 简化后的工具体系

#### 规划工具 (1个)
- `create_plan` - 创建执行计划

#### 模板工具 (1个)  
- `use_template` - 使用模板创建计划

#### 状态管理工具 (3个)
- `get_plan_status` - 获取计划状态
- `get_next_step` - 获取下一步
- `report_step_completion` - 报告步骤完成

#### 专家工具 (3个)
- `visual_expert` - 视觉内容创作
- `audio_expert` - 音频内容创作  
- `video_expert` - 视频内容创作

### 工具协作流程

1. **计划创建** - 使用`create_plan`或`use_template`
2. **状态查询** - 使用`get_plan_status`了解当前状态
3. **步骤执行** - 使用专家工具执行具体任务
4. **进度报告** - 使用`report_step_completion`更新状态
5. **循环继续** - 直到计划完成

## 📊 使用示例

### 示例1：使用模板创建城市海报

```python
# 1. 使用模板工具
result = use_template.func(
    template_id="city_poster_series",
    params={
        "cities": ["北京", "上海", "广州"],
        "style": "modern",
        "theme": "tourism"
    },
    user_context="为旅游公司制作城市宣传海报"
)

# 2. 获取生成的计划
plan = result["plan"]
print(f"生成了{len(plan.steps)}个步骤")

# 3. 执行引擎自动执行多步计划
```

### 示例2：自定义计划创建

```python
# 1. 创建自定义计划
result = create_plan.func(
    task="为新品牌创建完整的视觉识别系统，包括Logo、海报和宣传视频"
)

# 2. 计划自动保存到全局状态
# 3. 执行引擎根据步骤数量决定执行策略
```

### 示例3：计划状态监控

```python
# 1. 查询计划状态
status = get_plan_status.func(state)
print(status)  # 输出进度信息

# 2. 获取下一步
next_step = get_next_step.func(state)
print(f"下一步: {next_step}")

# 3. 报告步骤完成
report_step_completion.func(
    step_id="step_1",
    status="completed", 
    result={"images": ["poster1.jpg"]}
)
```

## 🎯 最佳实践

### 模板设计原则

1. **参数化设计** - 使用参数控制模板行为
2. **步骤原子性** - 每个步骤完成一个明确的任务
3. **依赖最小化** - 减少不必要的步骤依赖
4. **错误容忍** - 设计容错和重试机制

### 计划创建指南

1. **任务分解** - 将复杂任务分解为可执行的步骤
2. **工具选择** - 为每个步骤选择合适的专家工具
3. **依赖管理** - 正确设置步骤间的依赖关系
4. **参数传递** - 确保步骤间的数据流转

### 执行监控建议

1. **状态检查** - 定期检查计划执行状态
2. **错误处理** - 及时处理执行失败的步骤
3. **进度追踪** - 监控整体执行进度
4. **结果验证** - 验证步骤执行结果的质量

## 🔄 迁移指南

### 从旧系统迁移

1. **数据模型迁移** - 使用`migration_utils.py`中的工具
2. **工具调用更新** - 更新为简化后的工具集
3. **状态管理重构** - 使用新的状态管理工具
4. **测试验证** - 确保迁移后功能正常

### 兼容性保证

- 提供迁移工具自动转换旧格式
- 保持关键API的向后兼容
- 渐进式迁移，支持新旧系统并存

## 📈 性能优化

### 执行效率提升

1. **工具简化** - 从19个工具减少到8个
2. **状态管理** - 简化的全局状态管理
3. **路由优化** - 智能的执行策略选择
4. **并行执行** - 支持无依赖步骤的并行执行

### 资源使用优化

1. **内存管理** - 优化计划和步骤的内存使用
2. **状态持久化** - 支持计划状态的持久化存储
3. **缓存机制** - 缓存常用模板和计划
4. **清理机制** - 自动清理完成的计划数据

## � 快速开始

### 1. 使用模板创建计划

```python
# 城市海报系列
result = use_template.func(
    template_id="city_poster_series",
    params={
        "cities": ["北京", "上海", "广州"],
        "style": "modern"
    },
    user_context="旅游宣传"
)
```

### 2. 自定义计划创建

```python
# 复杂任务自动规划
result = create_plan.func(
    task="为新品牌创建Logo、海报和宣传视频"
)
```

### 3. 监控执行进度

```python
# 查看状态
status = get_plan_status.func()

# 获取下一步
next_step = get_next_step.func()

# 报告完成
report_step_completion.func(
    step_id="step_1",
    status="completed",
    result={"output": "result.jpg"}
)
```

## 🔧 重构详细内容

### 第一阶段：统一数据模型设计

**文件**: `src/graph_v2/unified_models.py`

**新增UnifiedPlan模型**:
- 统一的计划和步骤数据结构
- 支持模板、依赖关系、重试机制
- 完整的执行状态和时间记录
- 与现有系统平滑集成

### 第二阶段：工具体系简化

**简化成果**:
- **规划工具**: 2个 → 1个 (`create_plan`)
- **模板工具**: 4个 → 1个 (`use_template`)
- **状态管理**: 10个 → 3个 (`get_plan_status`, `get_next_step`, `report_step_completion`)
- **专家工具**: 保持3个不变 (`visual_expert`, `audio_expert`, `video_expert`)

### 第三阶段：执行引擎重构

**文件**: `src/graph_v2/execution_engine.py`

**重大改进**:
- 智能路由机制：根据计划复杂度选择执行策略
- 循环执行控制：持续驱动直到计划完成
- 状态同步机制：全局状态和本地状态同步

### 第四阶段：模板系统构建

**文件**: `src/templates/`, `src/graph_v2/template_renderer.py`

**核心功能**:
- 模板定义和注册系统
- Jinja2模板渲染引擎
- 内置常用模板（城市海报、AI视频、品牌设计）

### 第五阶段：提示词优化

**文件**: `src/prompts/planner_prompt_zh.md`

**重大改进**:
- 添加任务类型识别指南
- 增强步骤命名规范
- 提供具体示例和模板
- 改进工具使用说明

### 第六阶段：状态管理简化

**核心改进**:
- 从复杂消息解析改为简单全局状态管理
- 工具直接管理状态，避免复杂的数据流转
- Master Agent检查全局状态，自动同步计划

## � 内置模板

### 1. city_poster_series (城市海报系列)

**用途**: 为多个城市创建旅游海报

**参数**:
- `cities: List[str]` - 城市列表 (必需)
- `style: str` - 设计风格 (可选: modern/classic/artistic)
- `theme: str` - 主题 (可选: tourism/culture/business)

**用法**:
```python
use_template.func(
    template_id="city_poster_series",
    params={
        "cities": ["北京", "上海", "深圳"],
        "style": "modern",
        "theme": "tourism"
    }
)
```

### 2. ai_parody_video (AI鬼畜视频)

**用途**: 创建角色主题的鬼畜视频

**参数**:
- `character: str` - 角色名称 (必需)
- `style: str` - 视频风格 (可选: funny/epic/cute)
- `duration: int` - 时长秒数 (可选: 默认30)

**用法**:
```python
use_template.func(
    template_id="ai_parody_video",
    params={
        "character": "哪吒",
        "style": "epic",
        "duration": 60
    }
)
```

### 3. brand_design_suite (品牌设计套件)

**用途**: 完整的品牌设计方案

**参数**:
- `brand_name: str` - 品牌名称 (必需)
- `industry: str` - 行业类型 (必需)
- `style: str` - 设计风格 (可选: modern/classic/minimalist)

**用法**:
```python
use_template.func(
    template_id="brand_design_suite",
    params={
        "brand_name": "创新科技",
        "industry": "科技",
        "style": "modern"
    }
)
```

## ⚙️ 工具API参考

### 简化后的8个核心工具

| 分类 | 工具 | 功能 | 输入 | 输出 |
|------|------|------|------|------|
| 规划 | `create_plan` | 创建执行计划 | `task: str` | 确认消息 |
| 模板 | `use_template` | 使用模板 | `template_id, params, user_context` | 计划结果 |
| 状态 | `get_plan_status` | 获取计划状态 | 无 | 状态信息 |
| 状态 | `get_next_step` | 获取下一步 | 无 | 步骤信息 |
| 状态 | `report_step_completion` | 报告完成 | `step_id, status, result` | 确认消息 |
| 专家 | `visual_expert` | 视觉内容创作 | 任务描述 | 图片/设计结果 |
| 专家 | `audio_expert` | 音频内容创作 | 任务描述 | 音频/语音结果 |
| 专家 | `video_expert` | 视频内容创作 | 任务描述 | 视频/动画结果 |

## 🔍 常见问题

### 1. 执行引擎未启动
**症状**: 多步计划仍由Master Agent执行
**解决**: 检查计划步骤数量和路由逻辑

### 2. 模板渲染失败
**症状**: 参数替换不正确
**解决**: 验证必需参数是否提供完整

### 3. 步骤执行卡住
**症状**: 某个步骤长时间无响应
**解决**: 检查依赖关系和工具状态

### 4. 状态同步问题
**症状**: 计划状态丢失
**解决**: 检查全局状态和本地状态同步

## � 重构技术亮点

### 1. 工程简化哲学
- **问题**: "为什么会这么复杂？"
- **解决**: 直接的全局状态管理，避免复杂的消息解析
- **效果**: 代码复杂度大幅降低，维护成本显著减少

### 2. 提示词工程优化
- **问题**: LLM生成通用的"步骤1"、"步骤2"
- **解决**: 添加任务类型识别、步骤命名规范、具体示例
- **效果**: 步骤描述质量从通用名称提升到具体描述

### 3. 智能路由机制
- **问题**: 执行引擎未被正确使用
- **解决**: 修复路由时机，优化判断逻辑
- **效果**: 多步任务正确使用执行引擎，LangSmith可见

### 4. 架构优化设计
- **问题**: 工具冗余重叠，职责不清
- **解决**: 从19个工具精简到8个核心工具
- **效果**: 保持核心功能的同时大幅简化实现

## 📊 性能提升数据

| 指标 | 重构前 | 重构后 | 提升 |
|------|--------|--------|------|
| 工具数量 | 19个 | 8个 | 减少58% |
| 代码复杂度 | 高 | 低 | 大幅降低 |
| 执行效率 | 低 | 高 | 显著提升 |
| 维护成本 | 高 | 低 | 大幅减少 |
| 用户体验 | 一般 | 优秀 | 质的飞跃 |

## 🎯 使用建议

### 新开发者
1. 先阅读"快速开始"部分
2. 了解8个核心工具的用法
3. 尝试使用内置模板

### 项目维护者
1. 重点关注重构详细内容
2. 理解新的架构设计
3. 参考常见问题解决方案

### 系统集成者
1. 关注工具API参考
2. 理解执行流程变化
3. 注意状态管理机制

---

## 📖 更新日志

### v2.0.0 (2025-08-04)
- 🎉 **重大重构**: 完整的Plan和模板系统重构
- ✨ **新增**: UnifiedPlan统一数据模型
- ✨ **新增**: 完整的模板引擎系统
- 🔧 **优化**: 工具体系从19个简化到8个
- 🔧 **修复**: 执行引擎路由和状态管理
- 📝 **更新**: 提示词质量大幅提升
- 🏗️ **重构**: 架构全面优化和简化

### v1.x.x (历史版本)
- 原有的Plan和Step模型
- 基础的执行引擎
- 简单的模板支持

---

**文档版本**: v2.0.0
**最后更新**: 2025-08-04
**维护者**: DeerFlow开发团队

*这次重构是一个完整的系统优化项目，从工具简化到状态管理，从提示词优化到架构重构，全面提升了系统的可用性和可维护性！🎉*

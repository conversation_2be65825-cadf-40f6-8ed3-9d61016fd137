# 小品故事改写工作流程文档

## 概述

小品故事改写模板是一个完整的视频内容改写流程，通过AI技术实现：
1. 提取原视频的脚本和时间戳
2. 根据用户要求改写故事内容
3. 生成新的多角色配音
4. 合成最终的改写视频

## 工具链分析

### 1. 视频字幕提取工具 (video_subtitle_extraction)

**文件位置**: `/Users/<USER>/openArt-1/deer-flow/src/tools/audio/video_subtitle_extraction.py`

**输入参数**:
```python
{
    "media_url": "视频文件URL或路径",
    "language": "zh-CN",  # 语言代码
    "speaker_detection": True,  # 是否进行说话人识别
    "output_format": "json",  # 输出格式
    "extract_audio_segments": True,  # 是否提取音频片段
    "save_to_cos": False  # 是否保存到云存储
}
```

**输出格式**:
```json
{
    "success": true,
    "content": "JSON字符串，包含完整的字幕和时间戳信息",
    "metadata": {
        "task_id": "任务ID",
        "media_info": "媒体信息",
        "speaker_count": "说话人数量",
        "total_duration": "总时长",
        "language": "语言"
    }
}
```

**输出内容结构**:
```json
{
    "subtitles": [
        {
            "start_time": "00:00:01.000",
            "end_time": "00:00:03.500",
            "text": "对话内容",
            "speaker": "说话人ID",
            "confidence": 0.95
        }
    ],
    "speaker_info": {
        "speaker_1": "角色A",
        "speaker_2": "角色B"
    },
    "statistics": {
        "total_duration": 120.5,
        "total_segments": 45,
        "speaker_distribution": {}
    }
}
```

### 2. 多角色TTS工具 (multi_speaker_tts)

**文件位置**: `/Users/<USER>/openArt-1/deer-flow/src/tools/audio/multi_speaker_tts.py`

**输入参数**:
```python
{
    "dialogue_list": [
        {
            "speaker": "角色名",
            "text": "要转换的文本",
            "emotion": "calm/happy/sad/angry",
            "speed": 1.0
        }
    ],
    "voice_mapping": {
        "角色A": "voice_id_1",
        "角色B": "voice_id_2"
    },
    "output_format": "mp3",
    "save_to_cos": True
}
```

**输出格式**:
```json
{
    "success": true,
    "audio_file": "合成音频文件路径",
    "segments": [
        {
            "speaker": "角色名",
            "text": "文本内容",
            "audio_file": "单段音频文件",
            "duration": 2.5,
            "start_time": 0.0,
            "end_time": 2.5
        }
    ],
    "total_duration": 120.0,
    "cos_urls": ["云存储URL列表"]
}
```

### 3. 视频合成工具 (creatomate_video_tool_v2)

**文件位置**: `/Users/<USER>/openArt-1/deer-flow/src/tools/video/creatomate_video_tool_v2.py`

**输入参数**:
```python
{
    "input_mode": "natural_language",
    "task_description": "自然语言描述的合成要求",
    "scenes": [
        {
            "video_source": "原视频路径",
            "audio_source": "新配音路径", 
            "subtitles": [
                {
                    "text": "字幕文本",
                    "start_time": 1.0,
                    "end_time": 3.5,
                    "style": "字幕样式"
                }
            ]
        }
    ]
}
```

**输出格式**:
```json
{
    "success": true,
    "video_url": "最终视频的下载URL",
    "video_id": "Creatomate视频ID",
    "duration": 120.5,
    "resolution": "1920x1080",
    "file_size": "25.6MB",
    "download_info": {
        "local_path": "本地下载路径",
        "cos_url": "云存储URL"
    }
}
```

## 数据流转分析

### 步骤1: 视频脚本提取
```
输入: 原始视频文件
↓
video_subtitle_extraction工具
↓
输出: 带时间戳的脚本JSON
```

### 步骤2: 脚本改写
```
输入: 原始脚本 + 改写要求
↓
主agent直接处理 (direct_generation)
↓
输出: 改写后的脚本JSON (保持时间戳结构)
```

### 步骤3: 配音生成
```
输入: 改写脚本 + 角色声音映射
↓
multi_speaker_tts工具
↓
输出: 多角色合成音频 + 分段信息
```

### 步骤4: 视频合成
```
输入: 原视频 + 新配音 + 新字幕
↓
creatomate_video_tool_v2工具
↓
输出: 最终合成视频
```

## 关键技术要点

### 时间戳同步
- 原始脚本的时间戳必须在改写过程中保持
- 新配音的时长应与原对话时长匹配
- 字幕显示时机要与音频精确同步

### 角色一致性
- 说话人识别结果要映射到具体角色名
- 每个角色要分配固定的TTS声音ID
- 改写过程中保持角色数量和基本设定

### 质量控制
- 脚本改写要保持对话的自然性
- TTS配音要体现情感和语调变化
- 最终视频要确保音画同步准确

## 错误处理

### 常见问题
1. **视频格式不支持**: 自动转换或提示用户
2. **字幕提取失败**: 降级到纯音频处理
3. **TTS生成失败**: 重试或使用备用声音
4. **视频合成超时**: 分段处理或简化效果

### 恢复策略
- 每个步骤都有独立的错误处理
- 支持从中间步骤重新开始
- 保存中间结果便于调试

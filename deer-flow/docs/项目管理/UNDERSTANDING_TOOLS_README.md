# 🎯 DeerFlow 多模态理解工具

## 概述

DeerFlow 多模态理解工具是一套强大的内容分析工具，支持图片和视频的智能理解。所有工具都使用统一的 `gemini-2.5-pro-preview-06-05` 模型，提供高质量的多模态内容分析能力。

## 🛠️ 工具列表

### 1. 多模态理解工具 (`multimodal_understanding`)
- **功能**: 统一的图片和视频分析工具
- **适用场景**: 通用的多媒体内容理解
- **支持格式**: 图片（jpg, png, gif, webp）、视频（mp4, mov, avi, mkv, webm）

### 2. 图片理解工具 (`image_understanding`)
- **功能**: 专门的图片内容分析
- **适用场景**: 图片识别、物体检测、场景分析
- **支持格式**: 图片（jpg, png, gif, webp）

### 3. 视频理解工具 (`video_understanding`)
- **功能**: 专门的视频内容分析
- **适用场景**: 视频内容理解、动作分析、情节分析
- **支持格式**: 视频（mp4, mov, avi, mkv, webm）

## 🚀 使用方法

### 在 DeerFlow 系统中使用

```python
from src.config.configuration import Configuration
from src.tools.understanding import (
    get_multimodal_understanding_tool,
    get_image_understanding_tool,
    get_video_understanding_tool
)

# 创建配置
config = Configuration.from_runnable_config()

# 创建工具实例
multimodal_tool = get_multimodal_understanding_tool(config)
image_tool = get_image_understanding_tool(config)
video_tool = get_video_understanding_tool(config)
```

### Agent 调用示例

#### 图片分析
```python
result = image_tool.invoke({
    "image_url": "https://example.com/image.jpg",
    "question": "这张图片中有什么？",
    "analysis_type": "objects",  # 可选: general, objects, scene, style, text
    "detail_level": "detailed"   # 可选: brief, detailed, comprehensive
})
```

#### 视频分析
```python
result = video_tool.invoke({
    "media_url": "https://example.com/video.mp4",
    "question": "这个视频讲了什么？",
    "analysis_focus": "action",  # 可选: general, objects, scene, action, style, text
    "max_tokens": 4000
})
```

#### 多模态分析
```python
result = multimodal_tool.invoke({
    "media_url": "https://example.com/media.jpg",
    "question": "请详细分析这个内容",
    "analysis_focus": "general",
    "max_tokens": 4000,
    "temperature": 0.3
})
```

## 📋 参数说明

### 图片理解工具参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `image_url` | str | 必填 | 图片URL地址 |
| `question` | str | 通用描述 | 分析问题 |
| `analysis_type` | str | "general" | 分析类型：general/objects/scene/style/text |
| `detail_level` | str | "detailed" | 详细程度：brief/detailed/comprehensive |

### 多模态理解工具参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `media_url` | str | 必填 | 媒体文件URL |
| `question` | str | 通用描述 | 分析问题 |
| `analysis_focus` | str | None | 分析重点：general/objects/scene/action/style/text |
| `model` | str | gemini-2.5-pro-preview-06-05 | 使用的模型 |
| `max_tokens` | int | 4000 | 最大token数量 |
| `temperature` | float | 0.3 | 生成温度 |

## 🎯 分析类型说明

- **general**: 通用分析，全面描述内容
- **objects**: 物体识别，重点识别图像中的物体和元素
- **scene**: 场景分析，重点描述环境和背景
- **action**: 动作分析，重点描述视频中的动作和行为
- **style**: 风格分析，重点分析艺术风格和视觉效果
- **text**: 文字识别，重点提取图像中的文字内容

## 🔧 配置要求

### API 配置
工具会自动从以下位置获取API配置：
1. `conf.yaml` 中的 `BASIC_MODEL` 配置
2. 环境变量 `OPENAI_API_KEY` 和 `OPENAI_BASE_URL`
3. Configuration 对象中的 `openai_api_key` 和 `openai_base_url`

### 模型配置
- **默认模型**: `gemini-2.5-pro-preview-06-05`
- **API兼容**: OpenAI Chat Completions API 格式
- **支持功能**: 图片分析、视频分析、多模态理解

## 📊 返回格式

### 成功响应
```
✅ 多模态分析完成
📄 分析内容: [详细的分析结果]
🤖 使用模型: gemini-2.5-pro-preview-06-05
🎯 分析重点: objects
📊 Token使用: 2185
```

### 错误响应
```
❌ 多模态分析失败: [错误信息]
```

## 🚨 注意事项

1. **Token限制**: 建议设置 `max_tokens` 为 4000-8000，避免内容被截断
2. **URL要求**: 媒体文件必须是公开可访问的URL
3. **格式支持**: 确保媒体文件格式被模型支持
4. **API配置**: 确保API密钥和基础URL正确配置

## 🔍 故障排除

### 常见问题

1. **API返回空内容 (finish_reason: length)**
   - 解决方案: 增加 `max_tokens` 参数值

2. **API密钥未配置**
   - 解决方案: 检查 `conf.yaml` 或环境变量配置

3. **媒体URL无法访问**
   - 解决方案: 确保URL是公开可访问的

4. **模型不支持视频**
   - 解决方案: 确保使用支持视频的模型（如Gemini系列）

## 🎉 开发完成

✅ 多模态理解工具已成功集成到 DeerFlow 系统中  
✅ 支持所有 Agent 使用  
✅ 提供统一的 API 接口  
✅ 包含完善的错误处理  
✅ 使用最新的 gemini-2.5-pro-preview-06-05 模型  

工具已准备好在生产环境中使用！

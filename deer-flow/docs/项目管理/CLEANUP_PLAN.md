# 🗂️ DeerFlow 项目文件整理计划

## 📋 当前问题

1. **根目录混乱**: 大量测试文件、调试文件散落在根目录
2. **重复文件**: 多个配置文件示例、重复的文档
3. **临时文件**: 调试脚本、实验性代码没有分类
4. **数据库文件**: 大量测试数据库文件
5. **文档分散**: 文档分布在多个位置

## 🎯 目标结构

```
deer-flow/
├── README.md                    # 主要文档
├── pyproject.toml              # Python项目配置
├── Makefile                    # 构建脚本
├── .env.example                # 环境变量示例
├── .gitignore                  # Git忽略文件
├── langgraph.json              # LangGraph配置
│
├── src/                        # 源代码 (保持不变)
│   ├── __init__.py
│   ├── agents/
│   ├── config/
│   ├── graph_v2/
│   ├── llms/
│   ├── prompts/
│   ├── templates/
│   ├── tools/
│   └── utils/
│
├── tests/                      # 测试代码 (整理后)
│   ├── __init__.py
│   ├── unit/                   # 单元测试
│   ├── integration/            # 集成测试
│   ├── performance/            # 性能测试
│   └── fixtures/               # 测试数据
│
├── scripts/                    # 脚本工具 (整理后)
│   ├── setup/                  # 安装脚本
│   ├── debug/                  # 调试脚本
│   ├── maintenance/            # 维护脚本
│   └── examples/               # 示例脚本
│
├── docs/                       # 文档 (整理后)
│   ├── README.md               # 文档索引
│   ├── architecture/           # 架构文档
│   ├── api/                    # API文档
│   ├── guides/                 # 使用指南
│   └── development/            # 开发文档
│
├── config/                     # 配置文件
│   ├── conf.example.yaml       # 配置示例
│   └── templates/              # 配置模板
│
├── data/                       # 数据目录
│   ├── db/                     # 数据库文件
│   ├── assets/                 # 资源文件
│   └── temp/                   # 临时文件
│
├── web/                        # Web界面 (保持不变)
│
└── archive/                    # 归档文件
    ├── deprecated/             # 废弃代码
    ├── experiments/            # 实验性代码
    └── old_versions/           # 旧版本
```

## 📝 整理步骤

### Phase 1: 创建新目录结构
- [ ] 创建标准目录
- [ ] 移动核心文件
- [ ] 整理配置文件

### Phase 2: 分类现有文件
- [ ] 测试文件分类
- [ ] 调试脚本分类
- [ ] 文档整理
- [ ] 资源文件整理

### Phase 3: 清理和优化
- [ ] 删除重复文件
- [ ] 更新引用路径
- [ ] 统一命名规范
- [ ] 更新文档

### Phase 4: 验证和测试
- [ ] 验证所有路径
- [ ] 运行测试套件
- [ ] 更新CI/CD配置
- [ ] 更新README

## 🗑️ 待删除文件

### 根目录测试文件 (移动到 tests/)
- debug_current_issues.py
- debug_master_agent.py
- debug_react.py
- test_*.py (所有测试文件)

### 重复配置文件 (保留一个)
- conf.example.yaml
- conf.yaml.example
- env.example
- example.env.txt

### 临时/实验文件 (移动到 archive/)
- demo_*.py
- experimental/
- wawacreator/ (虚拟环境)

### 重复文档 (整合)
- 多个README和指南文件

## 📦 文件移动计划

### 测试文件 → tests/
```bash
mkdir -p tests/{unit,integration,performance,fixtures}
mv test_*.py tests/integration/
mv debug_*.py tests/debug/
```

### 脚本文件 → scripts/
```bash
mkdir -p scripts/{setup,debug,maintenance,examples}
mv bootstrap.* scripts/setup/
mv demo_*.py scripts/examples/
```

### 配置文件 → config/
```bash
mkdir -p config/templates
mv conf.example.yaml config/
mv *.env.* config/templates/
```

### 数据文件 → data/
```bash
mkdir -p data/{db,assets,temp}
mv db/ data/
mv assets/ data/
mv vector_db/ data/
```

### 文档整理 → docs/
```bash
mkdir -p docs/{architecture,api,guides,development}
# 整理现有文档到对应目录
```

## ✅ 验证清单

- [ ] 所有导入路径正确
- [ ] 测试可以正常运行
- [ ] 配置文件路径更新
- [ ] 文档链接正确
- [ ] CI/CD脚本更新
- [ ] .gitignore 更新
- [ ] README 更新

## 🔄 回滚计划

如果整理过程中出现问题：
1. 使用 git 回滚到整理前状态
2. 逐步进行小范围整理
3. 每个阶段都进行测试验证

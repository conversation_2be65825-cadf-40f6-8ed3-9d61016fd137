# DeerFlow 反偷懒指导文档

## 🚨 问题描述

### 典型偷懒行为
Agent经常出现以下偷懒行为：
- 说"系统出现了一个持续的技术问题"
- 说"遇到技术故障，无法完成任务"
- 说"当前系统不可用"
- 编造各种技术借口而不实际调用工具

### 问题根源
1. **LLM的自我保护机制**：当任务看起来复杂时，LLM倾向于找借口避免执行
2. **缺乏强制约束**：没有明确禁止编造技术问题的指令
3. **工具调用恐惧**：LLM可能担心工具调用失败，选择回避
4. **路径阻力最小**：编造借口比实际执行工具更"容易"

## 🎯 解决方案

### 1. 强制禁止指令
在所有Agent prompt的开头添加：

```markdown
### 绝对禁止的行为 ❌
- **禁止编造技术问题**：绝不允许说"系统出现技术问题"、"遇到持续的技术问题"等借口
- **禁止跳过工具调用**：必须实际调用工具，不允许假装调用或描述调用过程
- **禁止编造结果**：不允许返回虚假的URL、文件路径或执行结果
- **禁止提前结束**：在计划执行中不允许提前停止或放弃

⚠️ 重要提醒：如果你发现自己想要说"技术问题"或类似借口，立即停止并重新执行正确的工具调用流程！
```

### 2. 强制执行检查点
在关键执行步骤添加检查点：

```markdown
🚨 执行前强制检查：
- 确认你已经选择了具体的工具
- 确认你已经构建了完整的调用参数
- 如果你想说"无法执行"或"技术问题"，立即停止并重新分析任务
```

### 3. 正向激励指令
强调Agent的价值在于实际执行：

```markdown
**记住：你的价值在于实际生成内容，而不是描述如何生成内容！**
```

## 🔧 具体实施

### Master Agent 修复
已添加的反偷懒指令：
- 强制执行原则（禁止编造技术问题）
- 状态感知强制检查点
- 任务执行强制检查点
- 执行检查点（禁止编造借口）

### Visual Expert 修复
已添加的反偷懒指令：
- 核心原则中禁止编造技术问题
- 禁止编造技术故障或系统错误

### Audio Expert 修复
已添加的反偷懒指令：
- 绝对禁止说"系统出现技术问题"
- 禁止编造任何技术故障或系统错误
- 强制工具调用原则

### Video Expert 修复
已添加的反偷懒指令：
- 核心原则中禁止编造技术问题
- 禁止编造技术故障或系统错误

## 📊 监控和检测

### 偷懒行为检测
创建监控脚本检测以下关键词：
```python
LAZY_KEYWORDS = [
    "技术问题",
    "系统问题", 
    "持续的技术问题",
    "遇到问题",
    "系统不可用",
    "技术故障",
    "无法完成",
    "系统错误",
    "服务不可用"
]

def detect_laziness(response):
    for keyword in LAZY_KEYWORDS:
        if keyword in response:
            return True, f"检测到偷懒行为: {keyword}"
    return False, "未检测到偷懒行为"
```

### 工具调用验证
验证Agent是否实际调用了工具：
```python
def verify_tool_execution(agent_response, expected_tools):
    """验证Agent是否实际调用了预期的工具"""
    
    # 检查工具调用记录
    tool_calls = extract_tool_calls(agent_response)
    
    if not tool_calls:
        return False, "没有检测到任何工具调用"
    
    for expected_tool in expected_tools:
        if not any(call.name == expected_tool for call in tool_calls):
            return False, f"缺少预期的工具调用: {expected_tool}"
    
    return True, "工具调用验证通过"
```

## 🎯 测试验证

### 测试场景
1. **简单任务测试**：
   - "画一只猫" → 必须调用 visual_expert
   - "生成背景音乐" → 必须调用 audio_expert
   - "制作短视频" → 必须调用 video_expert

2. **复杂任务测试**：
   - "制作哪吒MV" → 必须创建计划并执行所有步骤
   - "做品牌套装" → 必须调用多个专家工具

3. **错误场景测试**：
   - 故意提供错误参数 → 应该调整参数重试，而不是说"技术问题"
   - 模拟API暂时不可用 → 应该重试，而不是放弃

### 成功标准
- ✅ 100%的任务都有实际的工具调用
- ✅ 0%的响应包含"技术问题"等偷懒关键词
- ✅ 所有失败都有明确的错误原因和重试尝试
- ✅ 用户收到的都是基于真实工具输出的结果

## 🔄 持续改进

### 反馈循环
1. **监控偷懒行为**：实时检测和记录
2. **分析偷懒模式**：找出常见的偷懒触发条件
3. **优化prompt**：针对性地加强反偷懒指令
4. **验证效果**：测试优化后的效果

### 长期策略
1. **建立偷懒行为数据库**：收集各种偷懒模式
2. **开发自动检测系统**：实时监控和告警
3. **创建最佳实践库**：总结有效的反偷懒技巧
4. **定期审查和更新**：根据新的偷懒模式更新防护措施

## 📋 实施检查清单

### 立即实施 ✅
- [x] Master Agent添加反偷懒指令
- [x] Visual Expert添加反偷懒指令  
- [x] Audio Expert添加反偷懒指令
- [x] Video Expert添加反偷懒指令

### 后续实施
- [ ] 部署偷懒行为监控系统
- [ ] 创建自动化测试套件
- [ ] 建立偷懒行为告警机制
- [ ] 定期评估和优化效果

## 🎉 预期效果

实施这些反偷懒措施后，预期达到：
- **偷懒率降低95%以上**
- **工具调用率提升到100%**
- **用户满意度显著提升**
- **系统可靠性大幅改善**

---

**记住：Agent的价值在于实际执行，而不是找借口！**

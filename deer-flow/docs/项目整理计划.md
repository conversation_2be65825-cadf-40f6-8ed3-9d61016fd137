# DeerFlow 项目整理计划

## 当前问题分析

### 1. 目录结构混乱
- **重复测试目录**: `test/` 和 `tests/` 并存
- **文档散乱**: 根目录有太多 `.md` 文件
- **配置重复**: `config/` 目录和根目录 `conf.yaml`
- **数据库重复**: `db/` 和 `data/db/` 两个数据库目录
- **临时文件**: `backup_tools_simplification/`、`debug_config.py` 等

### 2. 文件命名不规范
- 混合中英文命名
- 临时文件未及时清理
- 版本控制文件混乱

## 整理方案

### 阶段一：清理临时和重复文件

#### 1.1 删除临时文件
```bash
# 删除备份目录
rm -rf backup_tools_simplification/

# 删除调试文件
rm debug_config.py

# 删除重复的数据库目录（保留 data/db/）
rm -rf db/
```

#### 1.2 合并测试目录
```bash
# 将 test/ 内容迁移到 tests/
mv test/* tests/legacy/
rm -rf test/
```

### 阶段二：重新组织目录结构

#### 2.1 标准化根目录
```
deer-flow/
├── README.md                 # 主要说明文档
├── README_EN.md             # 英文说明文档
├── LICENSE                  # 许可证
├── pyproject.toml          # Python 项目配置
├── Makefile                # 构建脚本
├── langgraph.json          # LangGraph 配置
├── main.py                 # 主入口
├── interactive_chat.py     # 交互式聊天
├── enhanced_interactive_chat.py  # 增强版聊天
├── conf.yaml               # 主配置文件
├── src/                    # 源代码
├── tests/                  # 测试代码
├── docs/                   # 文档
├── scripts/                # 脚本工具
├── config/                 # 配置模板
├── data/                   # 数据文件
├── archive/                # 归档文件
└── web/                    # Web 前端
```

#### 2.2 整理文档目录
```
docs/
├── README.md               # 文档索引
├── 架构设计/
│   ├── 框架.md
│   ├── architecture_visual_explanation.md
│   └── final_architecture_recommendation.md
├── 开发指南/
│   ├── GRAPH_V2_DEVELOPER_GUIDE.md
│   ├── GRAPH_V2_QUICK_REFERENCE.md
│   └── CONTRIBUTING.md
├── 功能说明/
│   ├── PLAN_AND_TEMPLATE_SYSTEM.md
│   ├── 语音克隆工具使用指南.md
│   └── TODO_multimodal_implementation.md
├── 项目管理/
│   ├── CLEANUP_PLAN.md
│   ├── PROJECT_CLEANUP_REPORT.md
│   ├── PROMPT_OPTIMIZATION_PLAN.md
│   └── MASTER_AGENT_UNDERSTANDING_INTEGRATION.md
└── api/                    # API 文档
```

#### 2.3 规范测试目录
```
tests/
├── README.md               # 测试说明
├── unit/                   # 单元测试
├── integration/            # 集成测试
├── performance/            # 性能测试
├── fixtures/               # 测试数据
├── debug/                  # 调试测试
├── multimodal/             # 多模态测试
└── legacy/                 # 迁移的旧测试
```

### 阶段三：文件重命名和标准化

#### 3.1 统一命名规范
- 文档文件：使用中文或英文，避免混合
- 代码文件：使用英文下划线命名
- 配置文件：使用小写字母和下划线

#### 3.2 清理根目录文档
将以下文件移动到 `docs/` 目录：
- `CLEANUP_PLAN.md` → `docs/项目管理/`
- `GRAPH_V2_DEVELOPER_GUIDE.md` → `docs/开发指南/`
- `GRAPH_V2_QUICK_REFERENCE.md` → `docs/开发指南/`
- `MASTER_AGENT_UNDERSTANDING_INTEGRATION.md` → `docs/项目管理/`
- `PROJECT_CLEANUP_REPORT.md` → `docs/项目管理/`
- `PROMPT_OPTIMIZATION_PLAN.md` → `docs/项目管理/`
- `UNDERSTANDING_TOOLS_README.md` → `docs/功能说明/`
- `CONTRIBUTING` → `docs/开发指南/CONTRIBUTING.md`

### 阶段四：配置文件整理

#### 4.1 统一配置管理
- 保留根目录的 `conf.yaml` 作为主配置
- `config/` 目录只存放配置模板和示例
- 环境变量配置统一管理

#### 4.2 数据目录规范
```
data/
├── assets/                 # 静态资源
├── db/                     # 数据库文件
├── temp/                   # 临时文件
└── vector_db/              # 向量数据库
```

## 执行步骤

### 第一步：备份重要文件
```bash
# 创建备份
cp -r deer-flow deer-flow-backup-$(date +%Y%m%d)
```

### 第二步：执行清理脚本
```bash
# 运行整理脚本
python scripts/cleanup_project_structure.py
```

### 第三步：验证整理结果
```bash
# 检查目录结构
tree deer-flow -I '__pycache__|node_modules|.git'

# 运行测试确保功能正常
python -m pytest tests/
```

## 预期效果

1. **清晰的目录结构**：每个目录职责明确
2. **统一的命名规范**：便于维护和查找
3. **减少冗余文件**：提高项目整洁度
4. **改善开发体验**：更容易理解和贡献代码

## 注意事项

1. 整理前务必备份
2. 逐步执行，每步验证
3. 更新相关的导入路径
4. 更新文档中的路径引用
5. 通知团队成员路径变更

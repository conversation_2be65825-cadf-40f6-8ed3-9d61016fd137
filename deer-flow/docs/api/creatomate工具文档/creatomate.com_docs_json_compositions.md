---
url: "https://creatomate.com/docs/json/compositions"
title: "Compositions - Creatomate"
---

[Creatomate](https://creatomate.com/)

Get started

CTRL + K

# Compositions

## Grouping elements together

Sometimes it's easier to combine elements together, as if they were grouped together and moved together. This can be achieved with Creatomate through compositions, which work similarly to After Effects compositions. Each composition has its own track of elements and can be animated on its own.

```json
1{
2  "output_format": "mp4",
3  "width": 1920,
4  "height": 1080,
5  "elements": [\
6    {\
7      "type": "composition",\
8      "track": 1,\
9      "duration": "2 s",\
10      "y": [\
11        {\
12          "time": "0 s",\
13          "value": "30%"\
14        },\
15        {\
16          "time": "1 s",\
17          "value": "70%"\
18        },\
19        {\
20          "time": "2 s",\
21          "value": "30%"\
22        }\
23      ],\
24      "width": "50%",\
25      "height": "50%",\
26      "fill_color": "#ffffff",\
27      "elements": [\
28        {\
29          "type": "text",\
30          "track": 1,\
31          "y": "40%",\
32          "text": "This text element"\
33        },\
34        {\
35          "type": "text",\
36          "track": 2,\
37          "y": "60%",\
38          "text": "Is grouped with this one",\
39          "animations": [\
40            {\
41              "easing": "linear",\
42              "type": "wiggle",\
43              "ramp_duration": "0%"\
44            }\
45          ]\
46        }\
47      ]\
48    }\
49  ]
50}
```

00:00

Hide preview

Previous page

Using keyframes

Next page

Common properties
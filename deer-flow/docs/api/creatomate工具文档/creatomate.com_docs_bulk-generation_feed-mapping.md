---
url: "https://creatomate.com/docs/bulk-generation/feed-mapping"
title: "Feed mapping - Creatomate"
---

[Creatomate](https://creatomate.com/)

Get started

CTRL + K

# Feed mapping

The data from a feed is mapped to a template through a feed mapping. Simply put, it specifies which columns correspond to which elements in the template, so that <PERSON>reatomate can insert data from your feed into a template.

As shown in the [quick start guide](https://creatomate.com/docs/bulk-generation/quick-start), whenever you create a feed from a template, it gets automatically structured and mapped, based on the [dynamic](https://creatomate.com/docs/template-editor/element-properties#dynamic) elements in that template.

## Change the feed mapping

In order to change the feed mapping, go to a feed and click one of its dynamic elements (outlined in orange) in the right-side preview, then click any column to choose how the data should be mapped.

## Custom matching rules

As this is an advanced feature, you probably don't need it unless you're creating highly-dynamic videos where you want to go beyond simply replacing text or images.

As a default, when you map a column and element, it depends on the element type how data from the feed is used. For instance, when you connect an image to a column, it assumes that you are providing a URL to an image. If you want to replace a different element property instead, you can use a custom matching rule.

You can create a custom matching rule by choosing "Custom" while mapping a column to an element, then typing the property name you wish to replace. For instance, to replace the [background color](https://creatomate.com/docs/json/elements/text-element) of a text element, type "background\_color".

![](https://creatomate.com/assets/how-to-use-custom-matching-rule.png)

You can even edit the keyframe values of a property. For instance, you can use the matching rule "background\_color.0.value" to map the first keyframe of the _background\_color_ property. For more information on this, you can consult the [JSON format](https://creatomate.com/docs/json/using-keyframes) documentation and use the [source editor](https://creatomate.com/docs/template-editor/source-editor).

Previous page

Export to CSV
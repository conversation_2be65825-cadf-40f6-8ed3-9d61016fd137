---
url: "https://creatomate.com/docs/template-editor/audio"
title: "Audio - Creatomate"
---

[Creatomate](https://creatomate.com/)

Get started

CTRL + K

# Audio

## Audio properties

Use the _Trim_ setting to cut the audio clip to a certain length. _Start_ sets the start time in seconds, and _Duration_ limits the length of the clip.

With the _Fade_ setting, you can fade the audio clip in and out over a period of time specified in seconds.

To loop the audio clip for the duration of the element, use the _Loop_ setting. By default, the audio clip only plays once.

![](https://creatomate.com/assets/audio-element-properties.png)

## How to make the audio clip duration auto-sized

As with every other element, an audio element can be set to a fixed duration, or set to _auto_ to stretch it to the end of the composition. To make the element as long as the audio clip itself, you can set _Duration_ to "media". This is especially useful if the element is [dynamic](https://creatomate.com/docs/template-editor/element-properties#dynamic) and its audio clip length isn't known in advance.

![](https://creatomate.com/assets/video-audio-element-media-duration.png)

Previous page

Video

Next page

Shape
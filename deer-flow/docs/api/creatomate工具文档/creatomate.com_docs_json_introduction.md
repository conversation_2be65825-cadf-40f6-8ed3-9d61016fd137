---
url: "https://creatomate.com/docs/json/introduction"
title: "JSON to video & image - Creatomate"
---

[Creatomate](https://creatomate.com/)

Get started

CTRL + K

# JSON to video & image

## Use code to create graphics

Creatomate allows you to make videos and images using JSON, similarly to the way you would use HTML and CSS to build a website. A simple open source format is used to describe how the output file (an mp4, a gif, or a jpg) is rendered. Check out [the template page](https://creatomate.com/templates) to get a sense of what's possible.

If you open a template in the designer, you can view each video's JSON source by opening the [Source Editor](https://creatomate.com/docs/template-editor/source-editor) (press F12 in the designer). It allows you to experiment with the source and observe the results in real time.

This format was designed to provide developers with an easy, yet powerful way to create highly dynamic graphics. The way this works is that a developer can generate JSON using any programming language (for example, JavaScript, Ruby, or Python), and Creatomate can take that JSON and turn it into either a video or an image via the [API](https://creatomate.com/docs/api/introduction).

You can find the specification of this format in the following sections of the documentation. This is a reference for developers that wish to have full control over the rendering process. It is not a requirement for you to work with Creatomate since it is possible to generate this JSON automatically through our [template editor](https://creatomate.com/docs/template-editor/introduction).

The [quick start examples](https://creatomate.com/docs/json/quick-start/overview) that we have included will help you get started right away if you are in a hurry. Otherwise, read on for an in-depth guide to the format.

## Basic structure

In order to compose your render, you may use [text](https://creatomate.com/docs/json/elements/text-element), [image](https://creatomate.com/docs/json/elements/image-element), [video](https://creatomate.com/docs/json/elements/video-element), [audio clip](https://creatomate.com/docs/json/elements/audio-element), [shape](https://creatomate.com/docs/json/elements/shape-element), and [composition](https://creatomate.com/docs/json/elements/composition-element) elements. As an example, here is a render with a single text element that will produce a PNG:

```json
1{
2  "output_format": "png",
3  "width": 1920,
4  "height": 1080,
5  "elements": [\
6    {\
7      "type": "text",\
8      "text": "My text",\
9      "fill_color": "#ffffff",\
10      "font_family": "Open Sans"\
11    }\
12  ]
13}
```

Hide preview

Follow along by creating a new template and pasting the above code into the [Source Editor](https://creatomate.com/docs/template-editor/source-editor).

Next page

The timeline
---
url: "https://creatomate.com/docs/no-code-integration/create-single-render-action"
title: "Action: Create Single Render - Creatomate"
---

[Creatomate](https://creatomate.com/)

Get started

CTRL + K

# Action: Create Single Render

This action allows us make a single render and use it in the next Zap step. After selecting a template from your account, you will be able to specify the modifications you want to make to the dynamic elements. If you can't see the elements you want to target, open the template in the editor and set the "Dynamic" property.

## Advanced settings

Most of the time, you won't need the _Advanced settings_. Here you can specify modifications for any property in the template's source. Find out how this is formatted by referring to the API documentation about [modifications](https://creatomate.com/docs/api/rest-api/the-modifications-object).

You can override the resolution of the render by using the _scale_ setting. By default, this is 1.0 (100% of the template resolution). The _metadata_ setting enables you to pass through any value, which you can then use to identify this render in a different Zap. For example, you can use the metadata in the [New Render trigger](https://creatomate.com/docs/no-code-integration/new-render-trigger) to only trigger for renders with that exact value.

Previous page

Introduction

Next page

Action: Create Multiple Renders
---
url: "https://creatomate.com/docs/json/the-timeline"
title: "The timeline - Creatomate"
---

[Creatomate](https://creatomate.com/)

Get started

CTRL + K

# The timeline

The timeline allows you to arrange elements to show at a specific time, for a certain length of time. It is composed of tracks stacked on top of each other that determine in what order the elements are rendered on the screen. As an example, to put a title over a video, you would place the video on track 1 and the title on track 2.

In Creatomate, elements are placed on the timeline using the _track_, _time_, and _duration_ parameters. This example shows the text element on the first track, displaying it for one second:

```json
1{
2  "output_format": "mp4",
3  "duration": "3 s",
4  "width": 1920,
5  "height": 1080,
6  "elements": [\
7    {\
8      "type": "text",\
9      "track": 1,\
10      "time": "0 s",\
11      "duration": "1 s",\
12      "fill_color": "#ffffff",\
13      "text": "This text is only visible for one second",\
14      "font_family": "Open Sans"\
15    }\
16  ]
17}
```

00:00

Hide preview

## Arrange elements sequentially

If we assign the elements to the same track number, they will appear after each other. The duration of the video does not need to be specified, as Creatomate will figure out how long the video will be based on the total length of both elements.

```json
1{
2  "output_format": "mp4",
3  "width": 1920,
4  "height": 1080,
5  "elements": [\
6    {\
7      "type": "text",\
8      "track": 1,\
9      "duration": "1 s",\
10      "fill_color": "#ffffff",\
11      "text": "This is my first text",\
12      "font_family": "Open Sans"\
13    },\
14    {\
15      "type": "text",\
16      "track": 1,\
17      "duration": "1 s",\
18      "fill_color": "#ffffff",\
19      "text": "This is my second text",\
20      "font_family": "Open Sans"\
21    }\
22  ]
23}
```

00:00

Hide preview

## Displaying elements at the same time

To display two elements at the same time, they need to be on separate tracks. In this example, we are using a shape that is shown below the text elements. We can leave out the _duration_ property to stretch it to the length of the video:

```json
1{
2  "output_format": "mp4",
3  "width": 1920,
4  "height": 1080,
5  "elements": [\
6    {\
7      "type": "shape",\
8      "track": 1,\
9      "width": "90%",\
10      "height": "90%",\
11      "fill_color": "#ffffff",\
12      "path": "M 0 0 L 100 0 L 100 100 L 0 100 L 0 0 Z"\
13    },\
14    {\
15      "type": "text",\
16      "track": 2,\
17      "duration": "1 s",\
18      "text": "This is my first text",\
19      "font_family": "Open Sans"\
20    },\
21    {\
22      "type": "text",\
23      "track": 2,\
24      "duration": "1 s",\
25      "text": "This is my second text",\
26      "font_family": "Open Sans"\
27    }\
28  ]
29}
```

00:00

Hide preview

Previous page

Introduction

Next page

Using keyframes
---
url: "https://creatomate.com/docs/no-code-integration/introduction"
title: "Create videos and images with Zapier - Creatomate"
---

[Creatomate](https://creatomate.com/)

Get started

CTRL + K

# No-code integration

Zapier helps you automate repetitive tasks between multiple apps without writing code. There are over 5,000 apps supported by the Zapier platform, which makes it one of the most popular solutions for improving productivity through automation.

With Zapier support now available for Creatomate, you can automate video and image generation workflows using thousands of other apps.

Create Videos Using Zapier - YouTube

[Photo image of Creatomate](https://www.youtube.com/channel/UCPlI7lTAz3qcv9otIHaYdPw?embeds_referring_euri=https%3A%2F%2Fcreatomate.com%2F)

Creatomate

363 subscribers

[Create Videos Using Zapier](https://www.youtube.com/watch?v=MYDtUxmuQ3s)

Creatomate

Search

Watch later

Share

Copy link

Info

Shopping

Tap to unmute

If playback doesn't begin shortly, try restarting your device.

More videos

## More videos

You're signed out

Videos you watch may be added to the TV's watch history and influence TV recommendations. To avoid this, cancel and sign in to YouTube on your computer.

CancelConfirm

Share

Include playlist

An error occurred while retrieving sharing information. Please try again later.

[Watch on](https://www.youtube.com/watch?v=MYDtUxmuQ3s&embeds_referring_euri=https%3A%2F%2Fcreatomate.com%2F)

0:00

0:00 / 9:03
•Live

•

## Set up integration

Zapier will ask you to connect your account with Creatomate as soon as you create your first Zap. You'll need your project's API key for this, which can be found under _Project Settings_ then _Programmatic Access_ in the Creatomate dashboard.

![](https://creatomate.com/assets/where-to-find-project-settings.png)

## Available actions and trigger

Our Zapier integration consists of two actions and one trigger:

- [Action: Create Single Render](https://creatomate.com/docs/no-code-integration/create-single-render-action)
- [Action: Create Multiple Renders](https://creatomate.com/docs/no-code-integration/create-multiple-renders-action)
- [Trigger: New Render](https://creatomate.com/docs/no-code-integration/new-render-trigger)

Next page

Action: Create Single Render
---
url: "https://creatomate.com/docs/json/using-keyframes"
title: "Using keyframes - Creatomate"
---

[Creatomate](https://creatomate.com/)

Get started

CTRL + K

# Using keyframes

Keyframes allow us to animate the value of almost any [element property](https://creatomate.com/docs/json/elements/common-properties) over time. A keyframe is defined as a value at a specific time. A value is interpolated between keyframes based on the [easing](https://creatomate.com/docs/json/using-keyframes#keyframe-easing). Here's an example, where we move a text element left to right by animating its _x_ property:

```json
1{
2  "output_format": "mp4",
3  "width": 1920,
4  "height": 1080,
5  "elements": [\
6    {\
7      "type": "text",\
8      "track": 1,\
9      "duration": "2 s",\
10      "x": [\
11        {\
12          "time": "0 s",\
13          "value": "25%"\
14        },\
15        {\
16          "time": "2 s",\
17          "easing": "quintic-in-out",\
18          "value": "75%"\
19        }\
20      ],\
21      "fill_color": "#ffffff",\
22      "text": "Your text here"\
23    }\
24  ]
25}
```

00:00

Hide preview

## Enter and exit animations

You can also use one of the built-in animation keyframes. Unlike other keyframes, animation keyframes do not specify a point in time, but rather a longer period of time with the _time_ and _duration_ parameters. An animation keyframe can be placed at any point in time, but can also be linked to the _start_ and _end_ of an element. Place it at the end and use the _reversed_ parameter to make it act as an exit animation.

Below, we demonstrate how an element appears and disappears using a text animation.

```json
1{
2  "output_format": "mp4",
3  "width": 1920,
4  "height": 1080,
5  "elements": [\
6    {\
7      "type": "text",\
8      "track": 1,\
9      "duration": "3 s",\
10      "fill_color": "#ffffff",\
11      "text": "Emoji are also supported 👋",\
12      "animations": [\
13        {\
14          "time": "start",\
15          "duration": "1 s",\
16          "easing": "quadratic-out",\
17          "type": "text-slide",\
18          "direction": "up",\
19          "split": "letter",\
20          "scope": "split-clip"\
21        },\
22        {\
23          "time": "end",\
24          "duration": "1 s",\
25          "easing": "quadratic-out",\
26          "reversed": true,\
27          "type": "text-fly",\
28          "split": "word"\
29        }\
30      ]\
31    }\
32  ]
33}
```

00:00

Hide preview

Dozens of effects are available for animating your elements, and most can be combined to achieve custom effects. Go to
the template editor to see all the animation options available.

## Scene animations

As previously stated, animations don't need to be at the start or end of the element. Here's an example of making a text element "bounce" starting from the 0.5-second mark:

```json
1{
2  "output_format": "mp4",
3  "width": 1920,
4  "height": 1080,
5  "elements": [\
6    {\
7      "type": "text",\
8      "track": 1,\
9      "duration": "2 s",\
10      "fill_color": "#ffffff",\
11      "text": "Your text here",\
12      "animations": [\
13        {\
14          "time": "0.5 s",\
15          "duration": "1 s",\
16          "easing": "linear",\
17          "type": "bounce",\
18          "frequency": "2 Hz",\
19          "scale": "50%",\
20          "y_anchor": "100%"\
21        }\
22      ]\
23    }\
24  ]
25}
```

00:00

Hide preview

## Transition between elements

We can transition between two elements by using an animation that supports transitioning.

In this example, we use the "spin" animation to transition between two elements. The elements must be on the same track after one another, and the animation must be placed on the next element. As a result, the elements will overlap during the 1-second animation (note that the total video duration is 4 seconds, while the elements each last 2.5 seconds).

```json
1{
2  "output_format": "mp4",
3  "width": 1920,
4  "height": 1080,
5  "elements": [\
6    {\
7      "type": "text",\
8      "track": 1,\
9      "duration": "2.5 s",\
10      "fill_color": "#ffffff",\
11      "text": "This is my first text"\
12    },\
13    {\
14      "type": "text",\
15      "track": 1,\
16      "duration": "2.5 s",\
17      "fill_color": "#ffffff",\
18      "text": "This is my second text",\
19      "animations": [\
20        {\
21          "time": "start",\
22          "duration": "1 s",\
23          "transition": true,\
24          "type": "spin",\
25          "rotation": "360°"\
26        }\
27      ]\
28    }\
29  ]
30}
```

00:00

Hide preview

## Stretch an animation to the total length

To make an animation that lasts as long as the element, we can omit the _time_ and _duration_ properties.

```json
1{
2  "output_format": "mp4",
3  "duration": "10 s",
4  "width": 1920,
5  "height": 1080,
6  "elements": [\
7    {\
8      "type": "text",\
9      "track": 1,\
10      "fill_color": "#ffffff",\
11      "text": "Your text here",\
12      "animations": [\
13        {\
14          "easing": "linear",\
15          "type": "wiggle",\
16          "frequency": "1 Hz",\
17          "x_angle": "20°",\
18          "y_angle": "50°",\
19          "z_angle": "10°"\
20        }\
21      ]\
22    }\
23  ]
24}
```

00:00

Hide preview

## Keyframe easing

The following is a list of all the possible easing options. Check out [easings.net](https://easings.net/) for a visual representation of the easing functions.

| Easing | Description |
| --- | --- |
| linear | No easing. |
| cubic-bezier | Specify a custom cubic Bézier curve in the format _cubic-bezier(x1, y1, x2, y2)_. Learn more about the notation here on [this MDN page](https://developer.mozilla.org/en-US/docs/Web/CSS/easing-function#cubic_b%C3%A9zier_functions). |
| steps | Easing with equidistant steps, specified in the format _steps(number of steps)_ |
| elastic-in | Elastic easing at the start. |
| elastic-out | Elastic easing at the end. |
| elastic-in-out | Elastic easing at the start and end. |
| bounce-in | Bounce easing at the start. |
| bounce-out | Bounce easing at the end. |
| bounce-in-out | Bounce easing at the start and end. |
| sinusoid-in | Alias for cubic-bezier(0.12, 0, 0.39, 0) |
| sinusoid-out | Alias for cubic-bezier(0.61, 1, 0.88, 1) |
| sinusoid-in-out | Alias for cubic-bezier(0.37, 0, 0.63, 1) |
| quadratic-in | Alias for cubic-bezier(0.11, 0, 0.5, 0) |
| quadratic-out | Alias for cubic-bezier(0.5, 1, 0.89, 1) |
| quadratic-in-out | Alias for cubic-bezier(0.45, 0, 0.55, 1) |
| cubic-in | Alias for cubic-bezier(0.32, 0, 0.67, 0) |
| cubic-out | Alias for cubic-bezier(0.33, 1, 0.68, 1) |
| cubic-in-out | Alias for cubic-bezier(0.65, 0, 0.35, 1) |
| quartic-in | Alias for cubic-bezier(0.5, 0, 0.75, 0) |
| quartic-out | Alias for cubic-bezier(0.25, 1, 0.5, 1) |
| quartic-in-out | Alias for cubic-bezier(0.76, 0, 0.24, 1) |
| quintic-in | Alias for cubic-bezier(0.64, 0, 0.78, 0) |
| quintic-out | Alias for cubic-bezier(0.22, 1, 0.36, 1) |
| quintic-in-out | Alias for cubic-bezier(0.83, 0, 0.17, 1) |
| exponential-in | Alias for cubic-bezier(0.7, 0, 0.84, 0) |
| exponential-out | Alias for cubic-bezier(0.16, 1, 0.3, 1) |
| exponential-in-out | Alias for cubic-bezier(0.87, 0, 0.13, 1) |
| circular-in | Alias for cubic-bezier(0.55, 0, 1, 0.45) |
| circular-out | Alias for cubic-bezier(0, 0.55, 0.45, 1) |
| circular-in-out | Alias for cubic-bezier(0.85, 0, 0.15, 1) |
| back-in | Alias for cubic-bezier(0.36, 0, 0.66, -0.56) |
| back-out | Alias for cubic-bezier(0.34, 1.56, 0.64, 1) |
| back-in-out | Alias for cubic-bezier(0.68, -0.6, 0.32, 1.6) |

Previous page

The timeline

Next page

Compositions
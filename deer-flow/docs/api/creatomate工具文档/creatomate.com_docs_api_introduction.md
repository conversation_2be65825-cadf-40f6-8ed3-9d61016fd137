---
url: "https://creatomate.com/docs/api/introduction"
title: "API for video generation - Creatomate"
---

[Creatomate](https://creatomate.com/)

Get started

CTRL + K

# API

Creatomate's API allows you to automatically generate video and banners programmatically from your application.

In case you would prefer to use a no-code solution, you can use [Zapier](https://creatomate.com/docs/no-code-integration/introduction) instead.

## REST vs. Direct API

There are two types of APIs, the REST API and the Direct API. Most use cases can be handled by the REST API, but in some cases, you may want to use the Direct API.

- The [REST API](https://creatomate.com/docs/api/rest-api/authentication) is asynchronous and is best used between your software application and Creatomate. You can create renders and check the status of a request via a POST or GET request, or you can use webhooks to wait for a completed render. You authenticate with an API key.
- The [Direct API](https://creatomate.com/docs/api/direct-api/introduction) is synchronous and best used between a client's browser and Creatomate. You can put all the information for rendering an image or video in the query parameters of the URL. You can protect a request by adding a cryptographic signature and the rendering process is limited to 100 seconds.

## Official libraries

In case you use Node.js or PHP, you may also use the REST API using the official libraries available on NPM and Packagist:

- Node.js: The official [Creatomate Node.js library](https://www.npmjs.com/package/creatomate) is available on NPM.
- PHP: The official [Creatomate PHP library](https://packagist.org/packages/creatomate/creatomate) is available on Packagist.

## Quick start

Below is a quick video on how to create dynamic videos in JavaScript using the Node.js library. The process is similar for PHP using the Packagist library. Using another programming language? No problem, just refer to the examples provided on the [developers page](https://creatomate.com/developers) to get started, as well as the documentation available on the next pages.

Create Videos Programmatically Using Node.js - YouTube

[Photo image of Creatomate](https://www.youtube.com/channel/UCPlI7lTAz3qcv9otIHaYdPw?embeds_referring_euri=https%3A%2F%2Fcreatomate.com%2F)

Creatomate

363 subscribers

[Create Videos Programmatically Using Node.js](https://www.youtube.com/watch?v=lOw0c4055_c)

Creatomate

Search

Watch later

Share

Copy link

Info

Shopping

Tap to unmute

If playback doesn't begin shortly, try restarting your device.

More videos

## More videos

You're signed out

Videos you watch may be added to the TV's watch history and influence TV recommendations. To avoid this, cancel and sign in to YouTube on your computer.

CancelConfirm

Share

Include playlist

An error occurred while retrieving sharing information. Please try again later.

[Watch on](https://www.youtube.com/watch?v=lOw0c4055_c&embeds_referring_euri=https%3A%2F%2Fcreatomate.com%2F)

0:00

0:00 / 5:57
•Live

•

Next page

Authentication
---
url: "https://creatomate.com/docs/bulk-generation/quick-start"
title: "Quick start - Creatomate"
---

[Creatomate](https://creatomate.com/)

Get started

CTRL + K

# Quick start

This guide will show you how to make your first bulk-generated videos.

## Step 1: Create an account

If you do not already have an account with <PERSON><PERSON><PERSON><PERSON>, go ahead and create your account [here](https://creatomate.com/sign-in).

## Step 2: Select a template

To get started, let's choose a template. Go to your dashboard's Templates page and click New.

In this guide, we will use the Example Template from the Featured category. Click on it. We have the option to change the aspect ratio, but let's leave it at the default (1:1 Square) and click Create Template.

## Step 3: Create a feed

Once we created the template, it is opened in the template editor.
Let's leave the template unchanged for now and instead look for the blue Use Template button in the upper right corner. This button will bring up a new window where we can select our next action. Let's choose Spreadsheet to Video. It will then prompt us to select a feed, but as we don't yet have any feeds in our project, we will leave it at Create new feed. We land in the feed editor after we click on Continue.

To summerize; we just created a new template, and chose to automate it by creating a new feed. As a result, we now have a new template and feed in our project.

## Step 4: Adding rows to the feed

Now that we've created our template and feed, we can start generating videos by adding rows to our feed. But let's get a bit more familiar with the feed editor first.

If you've ever used a spreadsheet editor before, you will find that the feed editor works very similar. You can change the value of a data cell by double-clicking it. Clicking anywhere outside the cell deactivates it again.

You can also use your keyboard to navigate the feed. To start editing a data cell, press the Enter key. To stop editing, press the Escape key. To move around, use the arrow keys or the Tab key.

Remember that each row represents a single video. So, let's create a new video by clicking Add Row. Type some values into the newly inserted row, but leave the _Background_ cell empty for the time being. When you're finished, have a peek at the template preview on the right to see the resulting video.

The template preview shows us what the final video will look like for the row you selected.

## Step 5: Create and download the videos

We notice three options below the template preview: _Edit template_, _Create render_, and _Download render_. Before we can download it, we must first build the render (the finished video file). So let's start with Create render. This might take a few seconds depending on the template. When it's finished, we'll be able to click Download render. Be sure to download the renders before they expire, as they are kept for 30 days.

You may also render many videos at once by checking the boxes before each row and then clicking the n Rows Selected button followed by Create Renders.

## Step 6: Adding images to our rows (optional)

We left the _Background_ cell empty for the reason that this is an image data cell, which expects an image file or a URL to an image on the internet. In this example, we will upload an image from our device. If you don't already have an image, you can get one from [unsplash.com](https://unsplash.com/).

Once you've got your image, click the _Background_ cell, make sure that it's empty, and then click the file upload button  in the data cell. Now browse to the image file on your computer. When the image is uploaded, you should see the image being used in the template preview.

Because we modified the row, we must click Create render again to update the corresponding render.

![](https://creatomate.com/assets/how-to-add-upload-files-to-feed.png)

## Further reading

And that's all. We have now created our first videos by spreadsheet. If wish to learn more, we invite you to read the following pages.

- [Import & export data](https://creatomate.com/docs/bulk-generation/import-and-export-data/import-from-csv) So far, we've entered data by hand. Instead, your might wish to import your data from a file or other source, as detailed in this article.
- [Feed mapping](https://creatomate.com/docs/bulk-generation/feed-mapping) A feed is mapped to a template by a _feed mapping_. You can customize this mapping if you need to build more advanced videos (for example, data-driven videos).
- [No-code automation](https://creatomate.com/docs/no-code-integration/introduction) If you want to generate videos by real-time events or data, you could use Zapier to automate this process.

Previous page

Introduction

Next page

Import from CSV
---
url: "https://creatomate.com/docs/no-code-integration/create-multiple-renders-action"
title: "Action: Create Multiple Renders - Creatomate"
---

[Creatomate](https://creatomate.com/)

Get started

CTRL + K

# Action: Create Multiple Renders

This action allows you to create multiple renders at once based on their tags. You can then handle each render separately with the [New Render trigger](https://creatomate.com/docs/no-code-integration/new-render-trigger).

Make sure your templates have tags applied before you set up this action. You can do so in your dashboard under "Templates".

## Advanced settings

Most of the time, you won't need the _Advanced settings_. Here you can specify modifications for any property in the template's source. Find out how this is formatted by referring to the API documentation about [modifications](https://creatomate.com/docs/api/rest-api/the-modifications-object).

You can override the resolution of the render by using the _scale_ setting. By default, this is 1.0 (100% of the template resolution). The _metadata_ setting enables you to pass through any value, which you can then use to identify this render in a different Zap. For example, you can use the metadata in the [New Render trigger](https://creatomate.com/docs/no-code-integration/new-render-trigger) to only trigger for renders with that exact value.

Previous page

Action: Create Single Render

Next page

Trigger: New Render
---
url: "https://creatomate.com/docs/template-editor/composition"
title: "Compositions - Creatomate"
---

[Creatomate](https://creatomate.com/)

Get started

CTRL + K

# Compositions

## How to group elements together

Hold the Control key as you click the elements you want to group, then right-click the selection and choose "Group". You can also use the keyboard shortcut Control + Shift + C.

![](https://creatomate.com/assets/how-to-group-elements-together.jpg)

## Composition properties

_Flow Direction_ specifies the direction in which the elements are laid out when their corresponding position property (x or y) is set to "auto".

Set _Loop_ to "True" to create a [repeating composition](https://creatomate.com/docs/template-editor/composition#loop-a-composition). Use it along with _Plays_ to set the number of repetitions.

![](https://creatomate.com/assets/composition-element-properties.png)

## How to ungroup elements

Right-click the composition and choose "Ungroup".

## How to focus a composition

Double-clicking an element will focus the editor on the composition it is part of.

![](https://creatomate.com/assets/enter-composition.gif)

## Loop a composition

It is possible to set a composition to loop over time, enabling you to define an animation only once, then repeat it as often as you want. To do this, you need a composition with a fixed duration (a duration of "auto" won't work), then you set [_Loop_](https://creatomate.com/docs/template-editor/composition#composition-properties) to "True". _Plays_ allows you to set how many times it repeats. It will play endlessly when set to "auto".

At the moment, you can't use [video](https://creatomate.com/docs/template-editor/video#video-properties) or [audio](https://creatomate.com/docs/template-editor/audio#audio-properties) clips inside looped compositions. The clip will play only once. A video or audio element has its own _Loop_ setting that can be used for this purpose.

Examples of looping compositions are provided in several demo templates (click _New_ under _Templates_). An animated background is one of the most common uses.

Previous page

Shape

Next page

Element properties
{"markdown": "[Creatomate](https://creatomate.com/)\n\nGet started\n\nCTRL + K\n\n# Action: Create Single Render\n\nThis action allows us make a single render and use it in the next Zap step. After selecting a template from your account, you will be able to specify the modifications you want to make to the dynamic elements. If you can't see the elements you want to target, open the template in the editor and set the \"Dynamic\" property.\n\n## Advanced settings\n\nMost of the time, you won't need the _Advanced settings_. Here you can specify modifications for any property in the template's source. Find out how this is formatted by referring to the API documentation about [modifications](https://creatomate.com/docs/api/rest-api/the-modifications-object).\n\nYou can override the resolution of the render by using the _scale_ setting. By default, this is 1.0 (100% of the template resolution). The _metadata_ setting enables you to pass through any value, which you can then use to identify this render in a different Zap. For example, you can use the metadata in the [New Render trigger](https://creatomate.com/docs/no-code-integration/new-render-trigger) to only trigger for renders with that exact value.\n\nPrevious page\n\nIntroduction\n\nNext page\n\nAction: Create Multiple Renders", "metadata": {"next-head-count": "3", "viewport": "width=device-width, initial-scale=1", "title": "Action: Create Single Render - Creatomate", "scrapeId": "66e27a15-bd91-4f8d-980e-f3c301edce42", "sourceURL": "https://creatomate.com/docs/no-code-integration/create-single-render-action", "url": "https://creatomate.com/docs/no-code-integration/create-single-render-action", "statusCode": 200, "contentType": "text/html; charset=utf-8", "proxyUsed": "basic", "cacheState": "miss", "creditsUsed": 1}}
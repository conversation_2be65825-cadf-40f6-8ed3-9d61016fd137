{"markdown": "[Creatomate](https://creatomate.com/)\n\nGet started\n\nCTRL + K\n\n# The timeline\n\nThe timeline allows you to arrange elements to show at a specific time, for a certain length of time. It is composed of tracks stacked on top of each other that determine in what order the elements are rendered on the screen. As an example, to put a title over a video, you would place the video on track 1 and the title on track 2.\n\nIn Creatomate, elements are placed on the timeline using the _track_, _time_, and _duration_ parameters. This example shows the text element on the first track, displaying it for one second:\n\n```json\n1{\n2  \"output_format\": \"mp4\",\n3  \"duration\": \"3 s\",\n4  \"width\": 1920,\n5  \"height\": 1080,\n6  \"elements\": [\\\n7    {\\\n8      \"type\": \"text\",\\\n9      \"track\": 1,\\\n10      \"time\": \"0 s\",\\\n11      \"duration\": \"1 s\",\\\n12      \"fill_color\": \"#ffffff\",\\\n13      \"text\": \"This text is only visible for one second\",\\\n14      \"font_family\": \"Open Sans\"\\\n15    }\\\n16  ]\n17}\n```\n\n00:00\n\nHide preview\n\n## Arrange elements sequentially\n\nIf we assign the elements to the same track number, they will appear after each other. The duration of the video does not need to be specified, as <PERSON><PERSON><PERSON><PERSON> will figure out how long the video will be based on the total length of both elements.\n\n```json\n1{\n2  \"output_format\": \"mp4\",\n3  \"width\": 1920,\n4  \"height\": 1080,\n5  \"elements\": [\\\n6    {\\\n7      \"type\": \"text\",\\\n8      \"track\": 1,\\\n9      \"duration\": \"1 s\",\\\n10      \"fill_color\": \"#ffffff\",\\\n11      \"text\": \"This is my first text\",\\\n12      \"font_family\": \"Open Sans\"\\\n13    },\\\n14    {\\\n15      \"type\": \"text\",\\\n16      \"track\": 1,\\\n17      \"duration\": \"1 s\",\\\n18      \"fill_color\": \"#ffffff\",\\\n19      \"text\": \"This is my second text\",\\\n20      \"font_family\": \"Open Sans\"\\\n21    }\\\n22  ]\n23}\n```\n\n00:00\n\nHide preview\n\n## Displaying elements at the same time\n\nTo display two elements at the same time, they need to be on separate tracks. In this example, we are using a shape that is shown below the text elements. We can leave out the _duration_ property to stretch it to the length of the video:\n\n```json\n1{\n2  \"output_format\": \"mp4\",\n3  \"width\": 1920,\n4  \"height\": 1080,\n5  \"elements\": [\\\n6    {\\\n7      \"type\": \"shape\",\\\n8      \"track\": 1,\\\n9      \"width\": \"90%\",\\\n10      \"height\": \"90%\",\\\n11      \"fill_color\": \"#ffffff\",\\\n12      \"path\": \"M 0 0 L 100 0 L 100 100 L 0 100 L 0 0 Z\"\\\n13    },\\\n14    {\\\n15      \"type\": \"text\",\\\n16      \"track\": 2,\\\n17      \"duration\": \"1 s\",\\\n18      \"text\": \"This is my first text\",\\\n19      \"font_family\": \"Open Sans\"\\\n20    },\\\n21    {\\\n22      \"type\": \"text\",\\\n23      \"track\": 2,\\\n24      \"duration\": \"1 s\",\\\n25      \"text\": \"This is my second text\",\\\n26      \"font_family\": \"Open Sans\"\\\n27    }\\\n28  ]\n29}\n```\n\n00:00\n\nHide preview\n\nPrevious page\n\nIntroduction\n\nNext page\n\nUsing keyframes", "metadata": {"viewport": "width=device-width, initial-scale=1", "title": "The timeline - Creatomate", "next-head-count": "3", "scrapeId": "9d6ea005-4c8d-4b4c-9956-dd72b7a4e424", "sourceURL": "https://creatomate.com/docs/json/the-timeline", "url": "https://creatomate.com/docs/json/the-timeline", "statusCode": 200, "contentType": "text/html; charset=utf-8", "proxyUsed": "basic", "cacheState": "miss", "creditsUsed": 1}}
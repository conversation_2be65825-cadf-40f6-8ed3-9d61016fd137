{"markdown": "[Creatomate](https://creatomate.com/)\n\nGet started\n\nCTRL + K\n\n# Action: Create Multiple Renders\n\nThis action allows you to create multiple renders at once based on their tags. You can then handle each render separately with the [New Render trigger](https://creatomate.com/docs/no-code-integration/new-render-trigger).\n\nMake sure your templates have tags applied before you set up this action. You can do so in your dashboard under \"Templates\".\n\n## Advanced settings\n\nMost of the time, you won't need the _Advanced settings_. Here you can specify modifications for any property in the template's source. Find out how this is formatted by referring to the API documentation about [modifications](https://creatomate.com/docs/api/rest-api/the-modifications-object).\n\nYou can override the resolution of the render by using the _scale_ setting. By default, this is 1.0 (100% of the template resolution). The _metadata_ setting enables you to pass through any value, which you can then use to identify this render in a different Zap. For example, you can use the metadata in the [New Render trigger](https://creatomate.com/docs/no-code-integration/new-render-trigger) to only trigger for renders with that exact value.\n\nPrevious page\n\nAction: Create Single Render\n\nNext page\n\nTrigger: New Render", "metadata": {"viewport": "width=device-width, initial-scale=1", "next-head-count": "3", "title": "Action: Create Multiple Renders - Creatomate", "scrapeId": "987da713-8877-4a76-ba33-98237b3bb1aa", "sourceURL": "https://creatomate.com/docs/no-code-integration/create-multiple-renders-action", "url": "https://creatomate.com/docs/no-code-integration/create-multiple-renders-action", "statusCode": 200, "contentType": "text/html; charset=utf-8", "proxyUsed": "basic", "cacheState": "miss", "creditsUsed": 1}}
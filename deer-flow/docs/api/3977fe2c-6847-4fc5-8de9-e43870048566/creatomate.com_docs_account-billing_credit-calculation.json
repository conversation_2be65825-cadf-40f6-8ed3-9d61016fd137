{"markdown": "[Creatomate](https://creatomate.com/)\n\nGet started\n\nCTRL + K\n\n# Credit calculation\n\nThe number of monthly credits you have will determine how many videos or images you can produce each month. Images always cost one credit. The credit cost of a video varies depending on the resolution, frame rate, and length of the video.\n\n## How to calculate video credit costs\n\nTo find out how much it costs to generate a video, click \"Single Export\" in the template editor.\n\nThe formula is as follows. A credit gives you 100 million pixels of output, with a minimum of one credit per render. A video rendered at 720p (1280 by 720) with a frame rate of 25 fps and a length of 10 seconds, is a total of 1280 \\* 720 \\* 25 \\* 10 pixels, or about 230 million pixels, so 2.3 credits.\n\nTo put it another way, 10,000 credits are equivalent to:\n\n_(10,000 \\* 100,000,000) / (1280 \\* 720 \\* 25) = 43,402 seconds ≈ 12 hours of video_\n\nOr, if you're rendering at 60 frames per second:\n\n_(10,000 \\* 100,000,000) / (1280 \\* 720 \\* 60) = 18,084 seconds ≈ 5 hours of video_\n\n## Transcription costs\n\nCreatomate is capable of automatically [generating subtitles](https://creatomate.com/blog/how-to-automatically-add-subtitles-to-videos-using-zapier) based on the clips included in your video. This process is known as automatic transcription, or speech-to-text.\n\nTranscription is provided at no charge for high and medium resolution videos. Only if the transcription costs (at a rate of 10 credits per minute) are greater than the rendering credit costs will the transcription costs be used as the final credit cost. Therefore, auto-generated subtitles are free for any video starting at 720p and up (1280 by 720 at 25 fps, 13.8 credits/minute).\n\nFor insights into credit costs, use the \"Single Export\" feature in the template editor or refer to the _API Logs_ page in your dashboard.\n\n## Set a project budget\n\nCreating a project budget will allow you to allocate credits to individual projects on a monthly basis. Upon reaching the limit, you won't be able to create any more renders for that project until the following month, when it's automatically reset. Set up your project budget in the dashboard under \"Project Settings.\"\n\n![](https://creatomate.com/assets/where-to-find-project-settings.png)\n\nClick the switch after _Enable_ to activate the limit. You can then set an upper credit limit for the remainder of the month. In case you would like to reset the credit usage before the month ends, click \"Reset Now\".\n\n![](https://creatomate.com/assets/set-up-a-project-budget.png)", "metadata": {"viewport": "width=device-width, initial-scale=1", "next-head-count": "3", "title": "Credit Calculation - Creatomate", "scrapeId": "d2fd0cb2-3217-453b-917e-1246acd16cfa", "sourceURL": "https://creatomate.com/docs/account-billing/credit-calculation", "url": "https://creatomate.com/docs/account-billing/credit-calculation", "statusCode": 200, "contentType": "text/html; charset=utf-8", "proxyUsed": "basic", "cacheState": "miss", "creditsUsed": 1}}
{"markdown": "[Creatomate](https://creatomate.com/)\n\nGet started\n\nCTRL + K\n\n# Audio\n\n## Audio properties\n\nUse the _Trim_ setting to cut the audio clip to a certain length. _Start_ sets the start time in seconds, and _Duration_ limits the length of the clip.\n\nWith the _Fade_ setting, you can fade the audio clip in and out over a period of time specified in seconds.\n\nTo loop the audio clip for the duration of the element, use the _Loop_ setting. By default, the audio clip only plays once.\n\n![](https://creatomate.com/assets/audio-element-properties.png)\n\n## How to make the audio clip duration auto-sized\n\nAs with every other element, an audio element can be set to a fixed duration, or set to _auto_ to stretch it to the end of the composition. To make the element as long as the audio clip itself, you can set _Duration_ to \"media\". This is especially useful if the element is [dynamic](https://creatomate.com/docs/template-editor/element-properties#dynamic) and its audio clip length isn't known in advance.\n\n![](https://creatomate.com/assets/video-audio-element-media-duration.png)\n\nPrevious page\n\nVideo\n\nNext page\n\nShape", "metadata": {"viewport": "width=device-width, initial-scale=1", "next-head-count": "3", "title": "Audio - Creatomate", "scrapeId": "3d3551d1-181b-48b0-b95b-622d9345e1d5", "sourceURL": "https://creatomate.com/docs/template-editor/audio", "url": "https://creatomate.com/docs/template-editor/audio", "statusCode": 200, "contentType": "text/html; charset=utf-8", "proxyUsed": "basic", "cacheState": "miss", "creditsUsed": 1}}
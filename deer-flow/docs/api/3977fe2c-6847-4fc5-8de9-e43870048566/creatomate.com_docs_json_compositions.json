{"markdown": "[Creatomate](https://creatomate.com/)\n\nGet started\n\nCTRL + K\n\n# Compositions\n\n## Grouping elements together\n\nSometimes it's easier to combine elements together, as if they were grouped together and moved together. This can be achieved with Creatomate through compositions, which work similarly to After Effects compositions. Each composition has its own track of elements and can be animated on its own.\n\n```json\n1{\n2  \"output_format\": \"mp4\",\n3  \"width\": 1920,\n4  \"height\": 1080,\n5  \"elements\": [\\\n6    {\\\n7      \"type\": \"composition\",\\\n8      \"track\": 1,\\\n9      \"duration\": \"2 s\",\\\n10      \"y\": [\\\n11        {\\\n12          \"time\": \"0 s\",\\\n13          \"value\": \"30%\"\\\n14        },\\\n15        {\\\n16          \"time\": \"1 s\",\\\n17          \"value\": \"70%\"\\\n18        },\\\n19        {\\\n20          \"time\": \"2 s\",\\\n21          \"value\": \"30%\"\\\n22        }\\\n23      ],\\\n24      \"width\": \"50%\",\\\n25      \"height\": \"50%\",\\\n26      \"fill_color\": \"#ffffff\",\\\n27      \"elements\": [\\\n28        {\\\n29          \"type\": \"text\",\\\n30          \"track\": 1,\\\n31          \"y\": \"40%\",\\\n32          \"text\": \"This text element\"\\\n33        },\\\n34        {\\\n35          \"type\": \"text\",\\\n36          \"track\": 2,\\\n37          \"y\": \"60%\",\\\n38          \"text\": \"Is grouped with this one\",\\\n39          \"animations\": [\\\n40            {\\\n41              \"easing\": \"linear\",\\\n42              \"type\": \"wiggle\",\\\n43              \"ramp_duration\": \"0%\"\\\n44            }\\\n45          ]\\\n46        }\\\n47      ]\\\n48    }\\\n49  ]\n50}\n```\n\n00:00\n\nHide preview\n\nPrevious page\n\nUsing keyframes\n\nNext page\n\nCommon properties", "metadata": {"title": "Compositions - Creatomate", "viewport": "width=device-width, initial-scale=1", "next-head-count": "3", "scrapeId": "ae396bfc-3c8a-43c4-9ec7-303d74c76d5c", "sourceURL": "https://creatomate.com/docs/json/compositions", "url": "https://creatomate.com/docs/json/compositions", "statusCode": 200, "contentType": "text/html; charset=utf-8", "proxyUsed": "basic", "cacheState": "miss", "creditsUsed": 1}}
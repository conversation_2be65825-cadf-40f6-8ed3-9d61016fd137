{"markdown": "[Creatomate](https://creatomate.com/)\n\nGet started\n\nCTRL + K\n\n# API\n\nCreatomate's API allows you to automatically generate video and banners programmatically from your application.\n\nIn case you would prefer to use a no-code solution, you can use [Zapier](https://creatomate.com/docs/no-code-integration/introduction) instead.\n\n## REST vs. Direct API\n\nThere are two types of APIs, the REST API and the Direct API. Most use cases can be handled by the REST API, but in some cases, you may want to use the Direct API.\n\n- The [REST API](https://creatomate.com/docs/api/rest-api/authentication) is asynchronous and is best used between your software application and Creatomate. You can create renders and check the status of a request via a POST or GET request, or you can use webhooks to wait for a completed render. You authenticate with an API key.\n- The [Direct API](https://creatomate.com/docs/api/direct-api/introduction) is synchronous and best used between a client's browser and Creatomate. You can put all the information for rendering an image or video in the query parameters of the URL. You can protect a request by adding a cryptographic signature and the rendering process is limited to 100 seconds.\n\n## Official libraries\n\nIn case you use Node.js or PHP, you may also use the REST API using the official libraries available on NPM and Packagist:\n\n- Node.js: The official [Creatomate Node.js library](https://www.npmjs.com/package/creatomate) is available on NPM.\n- PHP: The official [Creatomate PHP library](https://packagist.org/packages/creatomate/creatomate) is available on Packagist.\n\n## Quick start\n\nBelow is a quick video on how to create dynamic videos in JavaScript using the Node.js library. The process is similar for PHP using the Packagist library. Using another programming language? No problem, just refer to the examples provided on the [developers page](https://creatomate.com/developers) to get started, as well as the documentation available on the next pages.\n\nCreate Videos Programmatically Using Node.js - YouTube\n\n[Photo image of Creatomate](https://www.youtube.com/channel/UCPlI7lTAz3qcv9otIHaYdPw?embeds_referring_euri=https%3A%2F%2Fcreatomate.com%2F)\n\nCreatomate\n\n363 subscribers\n\n[Create Videos Programmatically Using Node.js](https://www.youtube.com/watch?v=lOw0c4055_c)\n\nCreatomate\n\nSearch\n\nWatch later\n\nShare\n\nCopy link\n\nInfo\n\nShopping\n\nTap to unmute\n\nIf playback doesn't begin shortly, try restarting your device.\n\nMore videos\n\n## More videos\n\nYou're signed out\n\nVideos you watch may be added to the TV's watch history and influence TV recommendations. To avoid this, cancel and sign in to YouTube on your computer.\n\nCancelConfirm\n\nShare\n\nInclude playlist\n\nAn error occurred while retrieving sharing information. Please try again later.\n\n[Watch on](https://www.youtube.com/watch?v=lOw0c4055_c&embeds_referring_euri=https%3A%2F%2Fcreatomate.com%2F)\n\n0:00\n\n0:00 / 5:57\n•Live\n\n•\n\nNext page\n\nAuthentication", "metadata": {"language": "en", "title": "API for video generation - Creatomate", "next-head-count": "3", "viewport": ["width=device-width, initial-scale=1", "width=device-width, initial-scale=1"], "scrapeId": "d17cf90f-a6a6-4de8-b35a-f07fce9b0a98", "sourceURL": "https://creatomate.com/docs/api/introduction", "url": "https://creatomate.com/docs/api/introduction", "statusCode": 200, "contentType": "text/html; charset=utf-8", "proxyUsed": "basic", "cacheState": "miss", "creditsUsed": 1}}
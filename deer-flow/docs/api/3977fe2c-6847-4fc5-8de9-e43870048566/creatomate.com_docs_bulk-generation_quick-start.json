{"markdown": "[Creatomate](https://creatomate.com/)\n\nGet started\n\nCTRL + K\n\n# Quick start\n\nThis guide will show you how to make your first bulk-generated videos.\n\n## Step 1: Create an account\n\nIf you do not already have an account with <PERSON><PERSON>tom<PERSON>, go ahead and create your account [here](https://creatomate.com/sign-in).\n\n## Step 2: Select a template\n\nTo get started, let's choose a template. Go to your dashboard's Templates page and click New.\n\nIn this guide, we will use the Example Template from the Featured category. Click on it. We have the option to change the aspect ratio, but let's leave it at the default (1:1 Square) and click Create Template.\n\n## Step 3: Create a feed\n\nOnce we created the template, it is opened in the template editor.\nLet's leave the template unchanged for now and instead look for the blue Use Template button in the upper right corner. This button will bring up a new window where we can select our next action. Let's choose Spreadsheet to Video. It will then prompt us to select a feed, but as we don't yet have any feeds in our project, we will leave it at Create new feed. We land in the feed editor after we click on Continue.\n\nTo summerize; we just created a new template, and chose to automate it by creating a new feed. As a result, we now have a new template and feed in our project.\n\n## Step 4: Adding rows to the feed\n\nNow that we've created our template and feed, we can start generating videos by adding rows to our feed. But let's get a bit more familiar with the feed editor first.\n\nIf you've ever used a spreadsheet editor before, you will find that the feed editor works very similar. You can change the value of a data cell by double-clicking it. Clicking anywhere outside the cell deactivates it again.\n\nYou can also use your keyboard to navigate the feed. To start editing a data cell, press the Enter key. To stop editing, press the Escape key. To move around, use the arrow keys or the Tab key.\n\nRemember that each row represents a single video. So, let's create a new video by clicking Add Row. Type some values into the newly inserted row, but leave the _Background_ cell empty for the time being. When you're finished, have a peek at the template preview on the right to see the resulting video.\n\nThe template preview shows us what the final video will look like for the row you selected.\n\n## Step 5: Create and download the videos\n\nWe notice three options below the template preview: _Edit template_, _Create render_, and _Download render_. Before we can download it, we must first build the render (the finished video file). So let's start with Create render. This might take a few seconds depending on the template. When it's finished, we'll be able to click Download render. Be sure to download the renders before they expire, as they are kept for 30 days.\n\nYou may also render many videos at once by checking the boxes before each row and then clicking the n Rows Selected button followed by Create Renders.\n\n## Step 6: Adding images to our rows (optional)\n\nWe left the _Background_ cell empty for the reason that this is an image data cell, which expects an image file or a URL to an image on the internet. In this example, we will upload an image from our device. If you don't already have an image, you can get one from [unsplash.com](https://unsplash.com/).\n\nOnce you've got your image, click the _Background_ cell, make sure that it's empty, and then click the file upload button  in the data cell. Now browse to the image file on your computer. When the image is uploaded, you should see the image being used in the template preview.\n\nBecause we modified the row, we must click Create render again to update the corresponding render.\n\n![](https://creatomate.com/assets/how-to-add-upload-files-to-feed.png)\n\n## Further reading\n\nAnd that's all. We have now created our first videos by spreadsheet. If wish to learn more, we invite you to read the following pages.\n\n- [Import & export data](https://creatomate.com/docs/bulk-generation/import-and-export-data/import-from-csv) So far, we've entered data by hand. Instead, your might wish to import your data from a file or other source, as detailed in this article.\n- [Feed mapping](https://creatomate.com/docs/bulk-generation/feed-mapping) A feed is mapped to a template by a _feed mapping_. You can customize this mapping if you need to build more advanced videos (for example, data-driven videos).\n- [No-code automation](https://creatomate.com/docs/no-code-integration/introduction) If you want to generate videos by real-time events or data, you could use Zapier to automate this process.\n\nPrevious page\n\nIntroduction\n\nNext page\n\nImport from CSV", "metadata": {"viewport": "width=device-width, initial-scale=1", "title": "Quick start - <PERSON><PERSON><PERSON><PERSON>", "next-head-count": "3", "scrapeId": "0b4f19e1-4c26-45a1-9dc9-79a18cf37bd5", "sourceURL": "https://creatomate.com/docs/bulk-generation/quick-start", "url": "https://creatomate.com/docs/bulk-generation/quick-start", "statusCode": 200, "contentType": "text/html; charset=utf-8", "proxyUsed": "basic", "cacheState": "miss", "creditsUsed": 1}}
{"markdown": "[Creatomate](https://creatomate.com/)\n\nGet started\n\nCTRL + K\n\n# JSON to video & image\n\n## Use code to create graphics\n\nCreatomate allows you to make videos and images using JSON, similarly to the way you would use HTML and CSS to build a website. A simple open source format is used to describe how the output file (an mp4, a gif, or a jpg) is rendered. Check out [the template page](https://creatomate.com/templates) to get a sense of what's possible.\n\nIf you open a template in the designer, you can view each video's JSON source by opening the [Source Editor](https://creatomate.com/docs/template-editor/source-editor) (press F12 in the designer). It allows you to experiment with the source and observe the results in real time.\n\nThis format was designed to provide developers with an easy, yet powerful way to create highly dynamic graphics. The way this works is that a developer can generate JSON using any programming language (for example, JavaScript, Ruby, or Python), and Creatomate can take that JSON and turn it into either a video or an image via the [API](https://creatomate.com/docs/api/introduction).\n\nYou can find the specification of this format in the following sections of the documentation. This is a reference for developers that wish to have full control over the rendering process. It is not a requirement for you to work with <PERSON><PERSON>tom<PERSON> since it is possible to generate this JSON automatically through our [template editor](https://creatomate.com/docs/template-editor/introduction).\n\nThe [quick start examples](https://creatomate.com/docs/json/quick-start/overview) that we have included will help you get started right away if you are in a hurry. Otherwise, read on for an in-depth guide to the format.\n\n## Basic structure\n\nIn order to compose your render, you may use [text](https://creatomate.com/docs/json/elements/text-element), [image](https://creatomate.com/docs/json/elements/image-element), [video](https://creatomate.com/docs/json/elements/video-element), [audio clip](https://creatomate.com/docs/json/elements/audio-element), [shape](https://creatomate.com/docs/json/elements/shape-element), and [composition](https://creatomate.com/docs/json/elements/composition-element) elements. As an example, here is a render with a single text element that will produce a PNG:\n\n```json\n1{\n2  \"output_format\": \"png\",\n3  \"width\": 1920,\n4  \"height\": 1080,\n5  \"elements\": [\\\n6    {\\\n7      \"type\": \"text\",\\\n8      \"text\": \"My text\",\\\n9      \"fill_color\": \"#ffffff\",\\\n10      \"font_family\": \"Open Sans\"\\\n11    }\\\n12  ]\n13}\n```\n\nHide preview\n\nFollow along by creating a new template and pasting the above code into the [Source Editor](https://creatomate.com/docs/template-editor/source-editor).\n\nNext page\n\nThe timeline", "metadata": {"next-head-count": "3", "title": "JSON to video & image - Creatomate", "viewport": "width=device-width, initial-scale=1", "scrapeId": "a53cc901-4cbe-4443-be3f-fe621d22ffcd", "sourceURL": "https://creatomate.com/docs/json/introduction", "url": "https://creatomate.com/docs/json/introduction", "statusCode": 200, "contentType": "text/html; charset=utf-8", "proxyUsed": "basic", "cacheState": "miss", "creditsUsed": 1}}
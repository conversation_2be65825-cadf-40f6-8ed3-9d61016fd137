{"markdown": "[Creatomate](https://creatomate.com/)\n\nGet started\n\nCTRL + K\n\n# Compositions\n\n## How to group elements together\n\nHold the Control key as you click the elements you want to group, then right-click the selection and choose \"Group\". You can also use the keyboard shortcut Control + Shift + C.\n\n![](https://creatomate.com/assets/how-to-group-elements-together.jpg)\n\n## Composition properties\n\n_Flow Direction_ specifies the direction in which the elements are laid out when their corresponding position property (x or y) is set to \"auto\".\n\nSet _Loop_ to \"True\" to create a [repeating composition](https://creatomate.com/docs/template-editor/composition#loop-a-composition). Use it along with _Plays_ to set the number of repetitions.\n\n![](https://creatomate.com/assets/composition-element-properties.png)\n\n## How to ungroup elements\n\nRight-click the composition and choose \"Ungroup\".\n\n## How to focus a composition\n\nDouble-clicking an element will focus the editor on the composition it is part of.\n\n![](https://creatomate.com/assets/enter-composition.gif)\n\n## Loop a composition\n\nIt is possible to set a composition to loop over time, enabling you to define an animation only once, then repeat it as often as you want. To do this, you need a composition with a fixed duration (a duration of \"auto\" won't work), then you set [_Loop_](https://creatomate.com/docs/template-editor/composition#composition-properties) to \"True\". _Plays_ allows you to set how many times it repeats. It will play endlessly when set to \"auto\".\n\nAt the moment, you can't use [video](https://creatomate.com/docs/template-editor/video#video-properties) or [audio](https://creatomate.com/docs/template-editor/audio#audio-properties) clips inside looped compositions. The clip will play only once. A video or audio element has its own _Loop_ setting that can be used for this purpose.\n\nExamples of looping compositions are provided in several demo templates (click _New_ under _Templates_). An animated background is one of the most common uses.\n\nPrevious page\n\nShape\n\nNext page\n\nElement properties", "metadata": {"title": "Compositions - Creatomate", "next-head-count": "3", "viewport": "width=device-width, initial-scale=1", "scrapeId": "5b3e05e4-d06a-457e-bbfa-b37d3a8cb97a", "sourceURL": "https://creatomate.com/docs/template-editor/composition", "url": "https://creatomate.com/docs/template-editor/composition", "statusCode": 200, "contentType": "text/html; charset=utf-8", "proxyUsed": "basic", "cacheState": "miss", "creditsUsed": 1}}
{"markdown": "[Creatomate](https://creatomate.com/)\n\nGet started\n\nCTRL + K\n\n# Trigger: New Render\n\nThis trigger allows you to await any render that was created programmatically in your project.\n\nYou probably won't need this trigger if you're creating a single render per Zap. If you use the [Create Single Render action](https://creatomate.com/docs/no-code-integration/create-single-render-action), the rendered video or image will be available immediately in the following Zap step.\n\nWhen you configure the trigger, you can choose which _tags_ and _metadata_ it should trigger on. Leaving these fields empty will make the trigger run for every render in your project.\n\nPrevious page\n\nAction: Create Multiple Renders", "metadata": {"viewport": "width=device-width, initial-scale=1", "title": "Trigger: New Render - Creatomate", "next-head-count": "3", "scrapeId": "396c77dc-5f68-4c78-a271-20247e9bcd6e", "sourceURL": "https://creatomate.com/docs/no-code-integration/new-render-trigger", "url": "https://creatomate.com/docs/no-code-integration/new-render-trigger", "statusCode": 200, "contentType": "text/html; charset=utf-8", "proxyUsed": "basic", "cacheState": "miss", "creditsUsed": 1}}
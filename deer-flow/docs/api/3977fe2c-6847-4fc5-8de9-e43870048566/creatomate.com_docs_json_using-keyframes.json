{"markdown": "[Creatomate](https://creatomate.com/)\n\nGet started\n\nCTRL + K\n\n# Using keyframes\n\nKeyframes allow us to animate the value of almost any [element property](https://creatomate.com/docs/json/elements/common-properties) over time. A keyframe is defined as a value at a specific time. A value is interpolated between keyframes based on the [easing](https://creatomate.com/docs/json/using-keyframes#keyframe-easing). Here's an example, where we move a text element left to right by animating its _x_ property:\n\n```json\n1{\n2  \"output_format\": \"mp4\",\n3  \"width\": 1920,\n4  \"height\": 1080,\n5  \"elements\": [\\\n6    {\\\n7      \"type\": \"text\",\\\n8      \"track\": 1,\\\n9      \"duration\": \"2 s\",\\\n10      \"x\": [\\\n11        {\\\n12          \"time\": \"0 s\",\\\n13          \"value\": \"25%\"\\\n14        },\\\n15        {\\\n16          \"time\": \"2 s\",\\\n17          \"easing\": \"quintic-in-out\",\\\n18          \"value\": \"75%\"\\\n19        }\\\n20      ],\\\n21      \"fill_color\": \"#ffffff\",\\\n22      \"text\": \"Your text here\"\\\n23    }\\\n24  ]\n25}\n```\n\n00:00\n\nHide preview\n\n## Enter and exit animations\n\nYou can also use one of the built-in animation keyframes. Unlike other keyframes, animation keyframes do not specify a point in time, but rather a longer period of time with the _time_ and _duration_ parameters. An animation keyframe can be placed at any point in time, but can also be linked to the _start_ and _end_ of an element. Place it at the end and use the _reversed_ parameter to make it act as an exit animation.\n\nBelow, we demonstrate how an element appears and disappears using a text animation.\n\n```json\n1{\n2  \"output_format\": \"mp4\",\n3  \"width\": 1920,\n4  \"height\": 1080,\n5  \"elements\": [\\\n6    {\\\n7      \"type\": \"text\",\\\n8      \"track\": 1,\\\n9      \"duration\": \"3 s\",\\\n10      \"fill_color\": \"#ffffff\",\\\n11      \"text\": \"Emoji are also supported 👋\",\\\n12      \"animations\": [\\\n13        {\\\n14          \"time\": \"start\",\\\n15          \"duration\": \"1 s\",\\\n16          \"easing\": \"quadratic-out\",\\\n17          \"type\": \"text-slide\",\\\n18          \"direction\": \"up\",\\\n19          \"split\": \"letter\",\\\n20          \"scope\": \"split-clip\"\\\n21        },\\\n22        {\\\n23          \"time\": \"end\",\\\n24          \"duration\": \"1 s\",\\\n25          \"easing\": \"quadratic-out\",\\\n26          \"reversed\": true,\\\n27          \"type\": \"text-fly\",\\\n28          \"split\": \"word\"\\\n29        }\\\n30      ]\\\n31    }\\\n32  ]\n33}\n```\n\n00:00\n\nHide preview\n\nDozens of effects are available for animating your elements, and most can be combined to achieve custom effects. Go to\nthe template editor to see all the animation options available.\n\n## Scene animations\n\nAs previously stated, animations don't need to be at the start or end of the element. Here's an example of making a text element \"bounce\" starting from the 0.5-second mark:\n\n```json\n1{\n2  \"output_format\": \"mp4\",\n3  \"width\": 1920,\n4  \"height\": 1080,\n5  \"elements\": [\\\n6    {\\\n7      \"type\": \"text\",\\\n8      \"track\": 1,\\\n9      \"duration\": \"2 s\",\\\n10      \"fill_color\": \"#ffffff\",\\\n11      \"text\": \"Your text here\",\\\n12      \"animations\": [\\\n13        {\\\n14          \"time\": \"0.5 s\",\\\n15          \"duration\": \"1 s\",\\\n16          \"easing\": \"linear\",\\\n17          \"type\": \"bounce\",\\\n18          \"frequency\": \"2 Hz\",\\\n19          \"scale\": \"50%\",\\\n20          \"y_anchor\": \"100%\"\\\n21        }\\\n22      ]\\\n23    }\\\n24  ]\n25}\n```\n\n00:00\n\nHide preview\n\n## Transition between elements\n\nWe can transition between two elements by using an animation that supports transitioning.\n\nIn this example, we use the \"spin\" animation to transition between two elements. The elements must be on the same track after one another, and the animation must be placed on the next element. As a result, the elements will overlap during the 1-second animation (note that the total video duration is 4 seconds, while the elements each last 2.5 seconds).\n\n```json\n1{\n2  \"output_format\": \"mp4\",\n3  \"width\": 1920,\n4  \"height\": 1080,\n5  \"elements\": [\\\n6    {\\\n7      \"type\": \"text\",\\\n8      \"track\": 1,\\\n9      \"duration\": \"2.5 s\",\\\n10      \"fill_color\": \"#ffffff\",\\\n11      \"text\": \"This is my first text\"\\\n12    },\\\n13    {\\\n14      \"type\": \"text\",\\\n15      \"track\": 1,\\\n16      \"duration\": \"2.5 s\",\\\n17      \"fill_color\": \"#ffffff\",\\\n18      \"text\": \"This is my second text\",\\\n19      \"animations\": [\\\n20        {\\\n21          \"time\": \"start\",\\\n22          \"duration\": \"1 s\",\\\n23          \"transition\": true,\\\n24          \"type\": \"spin\",\\\n25          \"rotation\": \"360°\"\\\n26        }\\\n27      ]\\\n28    }\\\n29  ]\n30}\n```\n\n00:00\n\nHide preview\n\n## Stretch an animation to the total length\n\nTo make an animation that lasts as long as the element, we can omit the _time_ and _duration_ properties.\n\n```json\n1{\n2  \"output_format\": \"mp4\",\n3  \"duration\": \"10 s\",\n4  \"width\": 1920,\n5  \"height\": 1080,\n6  \"elements\": [\\\n7    {\\\n8      \"type\": \"text\",\\\n9      \"track\": 1,\\\n10      \"fill_color\": \"#ffffff\",\\\n11      \"text\": \"Your text here\",\\\n12      \"animations\": [\\\n13        {\\\n14          \"easing\": \"linear\",\\\n15          \"type\": \"wiggle\",\\\n16          \"frequency\": \"1 Hz\",\\\n17          \"x_angle\": \"20°\",\\\n18          \"y_angle\": \"50°\",\\\n19          \"z_angle\": \"10°\"\\\n20        }\\\n21      ]\\\n22    }\\\n23  ]\n24}\n```\n\n00:00\n\nHide preview\n\n## Keyframe easing\n\nThe following is a list of all the possible easing options. Check out [easings.net](https://easings.net/) for a visual representation of the easing functions.\n\n| Easing | Description |\n| --- | --- |\n| linear | No easing. |\n| cubic-bezier | Specify a custom cubic Bézier curve in the format _cubic-bezier(x1, y1, x2, y2)_. Learn more about the notation here on [this MDN page](https://developer.mozilla.org/en-US/docs/Web/CSS/easing-function#cubic_b%C3%A9zier_functions). |\n| steps | Easing with equidistant steps, specified in the format _steps(number of steps)_ |\n| elastic-in | Elastic easing at the start. |\n| elastic-out | Elastic easing at the end. |\n| elastic-in-out | Elastic easing at the start and end. |\n| bounce-in | Bounce easing at the start. |\n| bounce-out | Bounce easing at the end. |\n| bounce-in-out | Bounce easing at the start and end. |\n| sinusoid-in | Alias for cubic-bezier(0.12, 0, 0.39, 0) |\n| sinusoid-out | Alias for cubic-bezier(0.61, 1, 0.88, 1) |\n| sinusoid-in-out | Alias for cubic-bezier(0.37, 0, 0.63, 1) |\n| quadratic-in | Alias for cubic-bezier(0.11, 0, 0.5, 0) |\n| quadratic-out | Alias for cubic-bezier(0.5, 1, 0.89, 1) |\n| quadratic-in-out | Alias for cubic-bezier(0.45, 0, 0.55, 1) |\n| cubic-in | Alias for cubic-bezier(0.32, 0, 0.67, 0) |\n| cubic-out | Alias for cubic-bezier(0.33, 1, 0.68, 1) |\n| cubic-in-out | Alias for cubic-bezier(0.65, 0, 0.35, 1) |\n| quartic-in | Alias for cubic-bezier(0.5, 0, 0.75, 0) |\n| quartic-out | Alias for cubic-bezier(0.25, 1, 0.5, 1) |\n| quartic-in-out | Alias for cubic-bezier(0.76, 0, 0.24, 1) |\n| quintic-in | Alias for cubic-bezier(0.64, 0, 0.78, 0) |\n| quintic-out | Alias for cubic-bezier(0.22, 1, 0.36, 1) |\n| quintic-in-out | Alias for cubic-bezier(0.83, 0, 0.17, 1) |\n| exponential-in | Alias for cubic-bezier(0.7, 0, 0.84, 0) |\n| exponential-out | Alias for cubic-bezier(0.16, 1, 0.3, 1) |\n| exponential-in-out | Alias for cubic-bezier(0.87, 0, 0.13, 1) |\n| circular-in | Alias for cubic-bezier(0.55, 0, 1, 0.45) |\n| circular-out | Alias for cubic-bezier(0, 0.55, 0.45, 1) |\n| circular-in-out | Alias for cubic-bezier(0.85, 0, 0.15, 1) |\n| back-in | Alias for cubic-bezier(0.36, 0, 0.66, -0.56) |\n| back-out | Alias for cubic-bezier(0.34, 1.56, 0.64, 1) |\n| back-in-out | Alias for cubic-bezier(0.68, -0.6, 0.32, 1.6) |\n\nPrevious page\n\nThe timeline\n\nNext page\n\nCompositions", "metadata": {"title": "Using keyframes - <PERSON>rea<PERSON><PERSON>", "viewport": "width=device-width, initial-scale=1", "next-head-count": "3", "scrapeId": "ffe4fbc4-94ae-40d7-99c5-b2966530af01", "sourceURL": "https://creatomate.com/docs/json/using-keyframes", "url": "https://creatomate.com/docs/json/using-keyframes", "statusCode": 200, "contentType": "text/html; charset=utf-8", "proxyUsed": "basic", "cacheState": "miss", "creditsUsed": 1}}
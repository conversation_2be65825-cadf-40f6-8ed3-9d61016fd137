# 相声视频素材准备指南

## 📹 视频素材分类体系

### 🎭 **角色分类**

#### **逗哏演员（主导角色）**
- **作用**：推进剧情、制造笑点、主导对话
- **特征**：表情丰富、动作夸张、语调变化大
- **镜头要求**：特写和中景为主

#### **捧哏演员（配合角色）**
- **作用**：配合逗哏、反应提问、点题收束
- **特征**：表情稳重、动作适中、语调相对平稳
- **镜头要求**：中景和全景为主

### 🎨 **语调表情分类**

#### **Natural（自然语调）**
- **逗哏演员**：
  - 表情：平和专注，眼神自然
  - 动作：手势自然，姿态放松
  - 适用：叙述、铺垫、日常对话
  
- **捧哏演员**：
  - 表情：认真倾听，偶尔点头
  - 动作：姿态端正，手势简单
  - 适用：回应、确认、过渡

#### **Emphatic（强调语调）**
- **逗哏演员**：
  - 表情：激动兴奋，眼神明亮
  - 动作：手势夸张，动作幅度大
  - 适用：笑点爆发、关键信息、反转

- **捧哏演员**：
  - 表情：惊讶震惊，眼睛瞪大
  - 动作：手势明显，身体前倾
  - 适用：惊讶反应、强烈认同

#### **Confused（困惑语调）**
- **逗哏演员**：
  - 表情：困惑思考，眉头紧锁
  - 动作：挠头、摸下巴
  - 适用：装糊涂、制造悬念

- **捧哏演员**：
  - 表情：疑惑不解，皱眉思考
  - 动作：摇头、摊手询问
  - 适用：不理解、提出疑问

## 📁 **推荐的文件组织结构**

```
crosstalk_video_assets/
├── 逗哏演员/
│   ├── natural/
│   │   ├── dou_natural_01.mp4     # 自然叙述（20-30秒）
│   │   ├── dou_natural_02.mp4     # 平和对话（20-30秒）
│   │   └── dou_natural_03.mp4     # 故事铺垫（20-30秒）
│   ├── emphatic/
│   │   ├── dou_emphatic_01.mp4    # 笑点爆发（10-20秒）
│   │   ├── dou_emphatic_02.mp4    # 强调重点（10-20秒）
│   │   └── dou_emphatic_03.mp4    # 激动表达（10-20秒）
│   └── confused/
│       ├── dou_confused_01.mp4    # 装糊涂（15-25秒）
│       └── dou_confused_02.mp4    # 思考困惑（15-25秒）
├── 捧哏演员/
│   ├── natural/
│   │   ├── peng_natural_01.mp4    # 认真倾听（20-30秒）
│   │   ├── peng_natural_02.mp4    # 平静回应（20-30秒）
│   │   └── peng_natural_03.mp4    # 稳重对话（20-30秒）
│   ├── emphatic/
│   │   ├── peng_emphatic_01.mp4   # 惊讶反应（10-15秒）
│   │   └── peng_emphatic_02.mp4   # 强烈认同（10-15秒）
│   └── confused/
│       ├── peng_confused_01.mp4   # 疑惑不解（15-20秒）
│       ├── peng_confused_02.mp4   # 困惑提问（15-20秒）
│       └── peng_confused_03.mp4   # 摇头不懂（15-20秒）
└── 场景镜头/
    ├── wide_shots/
    │   ├── stage_wide_01.mp4       # 舞台全景（30-60秒）
    │   └── stage_wide_02.mp4       # 双人对话全景（30-60秒）
    ├── audience_reactions/
    │   ├── audience_laugh_01.mp4   # 观众大笑（5-10秒）
    │   ├── audience_applause_01.mp4 # 观众鼓掌（5-10秒）
    │   └── audience_enjoy_01.mp4   # 观众享受（5-10秒）
    └── transitions/
        ├── curtain_open.mp4        # 开场（3-5秒）
        └── curtain_close.mp4       # 结束（3-5秒）
```

## 🎬 **视频技术要求**

### **基本技术规格**
- **分辨率**：1080p (1920x1080) 或以上
- **格式**：MP4 (H.264编码)
- **帧率**：25fps 或 30fps
- **比例**：16:9
- **音频**：AAC编码，48kHz采样率

### **拍摄要求**
- **光线**：充足均匀，避免强烈阴影
- **背景**：相声舞台或简洁背景
- **稳定性**：使用三脚架，画面稳定
- **焦点**：人物清晰，背景适度虚化

### **内容要求**
- **时长**：每个片段15-30秒
- **动作**：完整的表情和动作循环
- **连贯性**：开头和结尾自然，便于剪辑
- **质量**：表演自然，符合相声风格

## 🔧 **视频素材配置文件**

创建 `video_assets_config.json` 文件：

```json
{
  "逗哏演员": {
    "natural": [
      {
        "url": "https://your-domain.com/dou_natural_01.mp4",
        "description": "逗哏演员自然叙述，表情平和，手势自然",
        "duration": 25.0,
        "quality": "high",
        "tags": ["叙述", "铺垫", "平和"]
      },
      {
        "url": "https://your-domain.com/dou_natural_02.mp4",
        "description": "逗哏演员日常对话，语调平稳，眼神专注",
        "duration": 28.0,
        "quality": "high",
        "tags": ["对话", "专注", "自然"]
      }
    ],
    "emphatic": [
      {
        "url": "https://your-domain.com/dou_emphatic_01.mp4",
        "description": "逗哏演员笑点爆发，表情激动，手势夸张",
        "duration": 15.0,
        "quality": "high",
        "tags": ["笑点", "激动", "夸张"]
      }
    ],
    "confused": [
      {
        "url": "https://your-domain.com/dou_confused_01.mp4",
        "description": "逗哏演员装糊涂，挠头思考，表情困惑",
        "duration": 18.0,
        "quality": "high",
        "tags": ["困惑", "思考", "装糊涂"]
      }
    ]
  },
  "捧哏演员": {
    "natural": [
      {
        "url": "https://your-domain.com/peng_natural_01.mp4",
        "description": "捧哏演员认真倾听，偶尔点头，表情专注",
        "duration": 30.0,
        "quality": "high",
        "tags": ["倾听", "点头", "专注"]
      }
    ],
    "emphatic": [
      {
        "url": "https://your-domain.com/peng_emphatic_01.mp4",
        "description": "捧哏演员惊讶反应，眼睛瞪大，身体前倾",
        "duration": 12.0,
        "quality": "high",
        "tags": ["惊讶", "反应", "前倾"]
      }
    ],
    "confused": [
      {
        "url": "https://your-domain.com/peng_confused_01.mp4",
        "description": "捧哏演员疑惑不解，皱眉思考，摇头表示不明白",
        "duration": 16.0,
        "quality": "high",
        "tags": ["疑惑", "皱眉", "摇头"]
      }
    ]
  },
  "场景": {
    "wide_shot": [
      {
        "url": "https://your-domain.com/stage_wide_01.mp4",
        "description": "相声舞台全景，两人对话，观众席可见",
        "duration": 45.0,
        "quality": "high",
        "tags": ["全景", "舞台", "对话"]
      }
    ],
    "audience_reaction": [
      {
        "url": "https://your-domain.com/audience_laugh_01.mp4",
        "description": "观众大笑反应，掌声热烈，气氛活跃",
        "duration": 8.0,
        "quality": "high",
        "tags": ["大笑", "掌声", "活跃"]
      }
    ]
  }
}
```

## 🚀 **快速开始指南**

### **第1步：准备视频素材**
1. 录制或收集相声表演视频
2. 按照分类体系整理视频片段
3. 确保技术规格符合要求

### **第2步：上传到可访问的URL**
1. 上传到云存储服务（如阿里云OSS、腾讯云COS）
2. 确保URL可以直接访问
3. 建议使用CDN加速

### **第3步：创建配置文件**
1. 按照模板创建 `video_assets_config.json`
2. 填写准确的URL和描述信息
3. 测试所有URL的可访问性

### **第4步：集成到相声模板**
1. 在相声模板中提供配置文件
2. 系统将自动进行智能匹配
3. 生成高质量的相声视频

## 💡 **优化建议**

### **提升匹配效果**
1. **多样性**：每种语调准备3-5个不同的片段
2. **质量**：优先保证high质量的核心片段
3. **描述**：详细准确的描述有助于智能匹配

### **提升视觉效果**
1. **连贯性**：确保片段之间的视觉风格一致
2. **节奏感**：根据相声节奏调整片段时长
3. **特效**：适当添加字幕和过渡效果

### **扩展性考虑**
1. **模块化**：按角色和语调模块化管理
2. **版本控制**：为不同风格准备不同版本
3. **用户定制**：支持用户上传自定义素材

通过这套完整的视频素材准备体系，你可以实现专业级的相声视频生成效果！

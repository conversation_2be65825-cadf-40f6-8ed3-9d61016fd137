#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频片段分析和上传工具
分析视频内容，生成描述，上传到COS并生成JSON文件
"""

import json
import os
import sys
import subprocess
from pathlib import Path
from typing import Dict, List, Any
import re

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.cos_uploader import get_cos_client, upload_to_cos
from src.config.configuration import Configuration


def get_video_info(video_path: str) -> Dict[str, Any]:
    """使用ffprobe获取视频信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            return json.loads(result.stdout)
    except Exception as e:
        print(f"获取视频信息失败: {e}")
    return {}


def extract_subtitle_from_filename(filename: str, json_data: Dict) -> str:
    """从文件名和JSON数据中提取对应的字幕内容"""
    # 从文件名提取信息：特写片段01_17.1秒.mp4
    match = re.search(r'特写片段(\d+)_(\d+\.?\d*)秒', filename)
    if not match:
        return ""
    
    clip_num = int(match.group(1))
    duration = float(match.group(2))
    
    # 从父目录名提取说话人信息
    parent_dir = Path(filename).parent.name
    speaker_match = re.search(r'说话人(\d+)', parent_dir)
    if not speaker_match:
        return ""
    
    speaker_id = speaker_match.group(1)
    
    # 在JSON数据中查找对应的字幕
    subtitles = json_data.get('subtitles', [])
    speaker_subtitles = [s for s in subtitles if s['speaker_id'] == speaker_id]
    
    # 按时间排序并选择对应的片段
    speaker_subtitles.sort(key=lambda x: x['start_time'])
    
    if clip_num <= len(speaker_subtitles):
        return speaker_subtitles[clip_num - 1]['text']
    
    return ""


def analyze_character_role(speaker_id: str, subtitles: List[Dict]) -> Dict[str, str]:
    """分析角色特征"""
    speaker_texts = [s['text'] for s in subtitles if s['speaker_id'] == speaker_id]
    all_text = ' '.join(speaker_texts)
    
    # 角色分析
    if '广告' in all_text and '诊所' in all_text and '村长' in all_text:
        return {
            "role": "心理医生",
            "description": "村里的心理医生，曾经是村长候选人，现在开设心理诊所",
            "characteristics": ["幽默风趣", "专业医生", "善于分析", "有村干部背景"]
        }
    elif '老头' in all_text and '彩票' in all_text and '中奖' in all_text:
        return {
            "role": "患者家属",
            "description": "患者的妻子，因为丈夫中彩票后的心理问题而求医",
            "characteristics": ["关心丈夫", "实用主义", "直接坦率", "家庭责任感强"]
        }
    elif '媳妇' in all_text and '心里' in all_text:
        return {
            "role": "患者",
            "description": "中彩票后出现心理问题的男性患者",
            "characteristics": ["内心困惑", "依赖妻子", "心理脆弱", "需要帮助"]
        }
    else:
        return {
            "role": "其他角色",
            "description": "剧中的其他角色",
            "characteristics": ["配角"]
        }


def generate_clip_description(video_path: str, subtitle_text: str, character_info: Dict, clip_index: int) -> Dict[str, Any]:
    """生成视频片段的详细描述"""
    video_info = get_video_info(video_path)
    
    # 提取视频基本信息
    duration = 0
    resolution = ""
    file_size = 0
    
    if video_info:
        format_info = video_info.get('format', {})
        duration = float(format_info.get('duration', 0))
        file_size = int(format_info.get('size', 0))
        
        # 获取视频流信息
        streams = video_info.get('streams', [])
        video_stream = next((s for s in streams if s['codec_type'] == 'video'), {})
        if video_stream:
            width = video_stream.get('width', 0)
            height = video_stream.get('height', 0)
            resolution = f"{width}x{height}"
    
    # 生成内容描述
    content_description = generate_content_description(subtitle_text, character_info, clip_index)
    
    return {
        "clip_info": {
            "filename": Path(video_path).name,
            "duration": round(duration, 1),
            "resolution": resolution,
            "file_size_mb": round(file_size / (1024 * 1024), 2),
            "format": "mp4"
        },
        "character": {
            "role": character_info["role"],
            "description": character_info["description"],
            "characteristics": character_info["characteristics"]
        },
        "content": {
            "subtitle": subtitle_text,
            "description": content_description["description"],
            "scene_type": content_description["scene_type"],
            "emotional_tone": content_description["emotional_tone"],
            "key_points": content_description["key_points"]
        },
        "technical": {
            "quality": "高清",
            "encoding": "H.264",
            "audio": "AAC"
        }
    }


def generate_content_description(subtitle_text: str, character_info: Dict, clip_index: int) -> Dict[str, Any]:
    """生成内容描述"""
    role = character_info["role"]
    
    # 场景类型判断
    scene_type = "对话"
    if "大夫" in subtitle_text:
        scene_type = "医患对话"
    elif "彩票" in subtitle_text or "中奖" in subtitle_text:
        scene_type = "关键揭示"
    elif "心里" in subtitle_text or "怎么回事" in subtitle_text:
        scene_type = "内心独白"
    elif "广告" in subtitle_text:
        scene_type = "自我介绍"
    
    # 情感基调
    emotional_tone = "平静"
    if "！" in subtitle_text:
        emotional_tone = "激动"
    elif "？" in subtitle_text:
        emotional_tone = "疑惑"
    elif "彩票" in subtitle_text and "中奖" in subtitle_text:
        emotional_tone = "紧张"
    elif "心里" in subtitle_text:
        emotional_tone = "困惑"
    
    # 关键要点
    key_points = []
    if role == "心理医生":
        if "广告" in subtitle_text:
            key_points.append("医生自我介绍")
        if "病" in subtitle_text:
            key_points.append("病情分析")
        if "治疗" in subtitle_text:
            key_points.append("治疗建议")
    elif role == "患者家属":
        if "彩票" in subtitle_text:
            key_points.append("揭示真相")
        if "老头" in subtitle_text:
            key_points.append("描述丈夫")
        if "大夫" in subtitle_text:
            key_points.append("求医过程")
    elif role == "患者":
        if "心里" in subtitle_text:
            key_points.append("内心困惑")
        if "媳妇" in subtitle_text:
            key_points.append("依赖妻子")
        if "抽" in subtitle_text:
            key_points.append("症状描述")
    
    # 生成描述
    descriptions = {
        "心理医生": [
            f"心理医生在诊所中的第{clip_index}个经典片段",
            f"展现了医生专业而幽默的诊疗风格",
            f"体现了乡村心理医生的独特魅力"
        ],
        "患者家属": [
            f"患者妻子的第{clip_index}个重要表达",
            f"展现了家属对患者的关心和焦虑",
            f"体现了普通家庭面对心理问题的真实反应"
        ],
        "患者": [
            f"患者的第{clip_index}个典型表现",
            f"展现了心理困扰者的内心世界",
            f"体现了患者对治疗的渴望和依赖"
        ]
    }
    
    description = descriptions.get(role, [f"{role}的第{clip_index}个片段"])[0]
    
    return {
        "description": description,
        "scene_type": scene_type,
        "emotional_tone": emotional_tone,
        "key_points": key_points
    }


def process_video_clips(clips_dir: str, json_file: str, output_json: str = "video_clips_info.json"):
    """处理所有视频片段"""
    clips_path = Path(clips_dir)
    if not clips_path.exists():
        print(f"错误: 视频片段目录不存在: {clips_dir}")
        return
    
    # 读取原始JSON数据
    with open(json_file, 'r', encoding='utf-8') as f:
        json_data = json.load(f)
    
    # 初始化配置和COS客户端
    try:
        config = Configuration.from_runnable_config()
        cos_client = get_cos_client(config)
        print("✅ COS客户端初始化成功")
    except Exception as e:
        print(f"❌ COS客户端初始化失败: {e}")
        return
    
    # 处理结果
    results = {
        "project_info": {
            "title": "心理诊所角色特写片段",
            "description": "从心理诊所对话视频中提取的角色特写片段",
            "total_clips": 0,
            "characters": [],
            "created_at": "",
            "source_video": "952473473_da2-1-192.mp4"
        },
        "clips": []
    }
    
    # 遍历所有角色目录
    character_dirs = [d for d in clips_path.iterdir() if d.is_dir()]
    
    for char_dir in character_dirs:
        print(f"\n处理角色目录: {char_dir.name}")
        
        # 提取说话人ID
        speaker_match = re.search(r'说话人(\d+)', char_dir.name)
        if not speaker_match:
            continue
        
        speaker_id = speaker_match.group(1)
        
        # 分析角色信息
        character_info = analyze_character_role(speaker_id, json_data['subtitles'])
        results["project_info"]["characters"].append({
            "speaker_id": speaker_id,
            "role": character_info["role"],
            "description": character_info["description"]
        })
        
        # 处理该角色的所有视频片段
        video_files = sorted([f for f in char_dir.iterdir() if f.suffix == '.mp4'])
        
        for i, video_file in enumerate(video_files, 1):
            print(f"  处理片段: {video_file.name}")
            
            # 提取字幕内容
            subtitle_text = extract_subtitle_from_filename(str(video_file), json_data)
            
            # 生成片段描述
            clip_description = generate_clip_description(
                str(video_file), subtitle_text, character_info, i
            )
            
            # 上传到COS
            try:
                with open(video_file, 'rb') as f:
                    video_bytes = f.read()
                
                cos_url = upload_to_cos(
                    client=cos_client,
                    file_bytes=video_bytes,
                    bucket=config.cos_bucket,
                    region=config.cos_region,
                    file_extension="mp4"
                )
                
                print(f"    ✅ 上传成功: {cos_url}")
                
                # 添加到结果中
                clip_info = {
                    "id": f"clip_{speaker_id}_{i:02d}",
                    "speaker_id": speaker_id,
                    "clip_index": i,
                    "cos_url": cos_url,
                    "local_path": str(video_file.relative_to(clips_path)),
                    **clip_description
                }
                
                results["clips"].append(clip_info)
                results["project_info"]["total_clips"] += 1
                
            except Exception as e:
                print(f"    ❌ 上传失败: {e}")
    
    # 添加创建时间
    from datetime import datetime
    results["project_info"]["created_at"] = datetime.now().isoformat()
    
    # 保存结果到JSON文件
    with open(output_json, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n🎉 处理完成!")
    print(f"📊 总共处理了 {results['project_info']['total_clips']} 个视频片段")
    print(f"📁 结果保存到: {output_json}")
    
    return results


def main():
    """主函数"""
    clips_dir = "final_outputs/character_clips"
    json_file = "real_tool_call_result.json"
    output_json = "video_clips_cos_info.json"
    
    print("=" * 60)
    print("视频片段分析和上传工具")
    print("=" * 60)
    
    if not os.path.exists(clips_dir):
        print(f"错误: 视频片段目录不存在: {clips_dir}")
        return
    
    if not os.path.exists(json_file):
        print(f"错误: JSON文件不存在: {json_file}")
        return
    
    process_video_clips(clips_dir, json_file, output_json)


if __name__ == "__main__":
    main()

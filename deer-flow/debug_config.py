#!/usr/bin/env python3
"""
调试配置加载问题
检查通义听悟配置是否正确加载
"""

import logging
import sys
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_config():
    """调试配置加载"""
    logger.info("🔍 调试通义听悟配置加载...")
    
    try:
        # 设置环境变量
        os.environ["TONGYI_TINGWU_ACCESS_KEY_ID"] = "LTAI5tBTpMAPNJ3Z6jpZyTSo"
        os.environ["TONGYI_TINGWU_ACCESS_KEY_SECRET"] = "******************************"
        os.environ["TONGYI_TINGWU_APP_KEY"] = "wbp1hepOKEWDiQEC"
        
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入配置
        from config.configuration import Configuration
        
        # 创建配置
        config = Configuration.from_runnable_config()
        logger.info("✅ 配置对象创建成功")
        
        # 检查配置对象的属性
        logger.info("🔍 检查配置对象属性:")
        logger.info(f"   subtitle_api_provider: {getattr(config, 'subtitle_api_provider', 'NOT_FOUND')}")
        logger.info(f"   tongyi_tingwu_access_key_id: {getattr(config, 'tongyi_tingwu_access_key_id', 'NOT_FOUND')}")
        logger.info(f"   tongyi_tingwu_access_key_secret: {getattr(config, 'tongyi_tingwu_access_key_secret', 'NOT_FOUND')}")
        logger.info(f"   tongyi_tingwu_app_key: {getattr(config, 'tongyi_tingwu_app_key', 'NOT_FOUND')}")
        
        # 检查环境变量
        logger.info("🔍 检查环境变量:")
        logger.info(f"   TONGYI_TINGWU_ACCESS_KEY_ID: {os.getenv('TONGYI_TINGWU_ACCESS_KEY_ID', 'NOT_SET')}")
        logger.info(f"   TONGYI_TINGWU_ACCESS_KEY_SECRET: {os.getenv('TONGYI_TINGWU_ACCESS_KEY_SECRET', 'NOT_SET')}")
        logger.info(f"   TONGYI_TINGWU_APP_KEY: {os.getenv('TONGYI_TINGWU_APP_KEY', 'NOT_SET')}")
        
        # 模拟工具中的配置检查逻辑
        logger.info("🔍 模拟工具配置检查:")
        
        api_provider = config.subtitle_api_provider or "tongyi"
        logger.info(f"   api_provider: {api_provider}")
        
        if api_provider == "tongyi":
            tongyi_access_key_id = (config.tongyi_tingwu_access_key_id or 
                                   os.getenv("TONGYI_TINGWU_ACCESS_KEY_ID") or
                                   os.getenv("ALIBABA_CLOUD_ACCESS_KEY_ID"))
            tongyi_access_key_secret = (config.tongyi_tingwu_access_key_secret or
                                       os.getenv("TONGYI_TINGWU_ACCESS_KEY_SECRET") or
                                       os.getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET"))
            tongyi_app_key = (config.tongyi_tingwu_app_key or
                             os.getenv("TONGYI_TINGWU_APP_KEY"))
            
            logger.info(f"   检查结果:")
            logger.info(f"      tongyi_access_key_id: {tongyi_access_key_id}")
            logger.info(f"      tongyi_access_key_secret: {tongyi_access_key_secret}")
            logger.info(f"      tongyi_app_key: {tongyi_app_key}")
            
            if not tongyi_access_key_id or not tongyi_access_key_secret or not tongyi_app_key:
                logger.warning("❌ 通义听悟API配置缺失")
                logger.info("   缺失的配置:")
                if not tongyi_access_key_id:
                    logger.info("      - tongyi_access_key_id")
                if not tongyi_access_key_secret:
                    logger.info("      - tongyi_access_key_secret")
                if not tongyi_app_key:
                    logger.info("      - tongyi_app_key")
            else:
                logger.info("✅ 通义听悟API配置完整")
        
        # 检查配置类定义
        logger.info("🔍 检查配置类定义:")
        config_attrs = [attr for attr in dir(config) if not attr.startswith('_')]
        tongyi_attrs = [attr for attr in config_attrs if 'tongyi' in attr.lower()]
        logger.info(f"   通义听悟相关属性: {tongyi_attrs}")
        
        # 检查配置文件
        logger.info("🔍 检查配置文件加载:")
        try:
            import yaml
            config_file_path = '/Users/<USER>/openArt-1/deer-flow/conf.yaml'
            if os.path.exists(config_file_path):
                with open(config_file_path, 'r', encoding='utf-8') as f:
                    yaml_config = yaml.safe_load(f)
                logger.info(f"   配置文件存在: {config_file_path}")
                logger.info(f"   配置文件内容键: {list(yaml_config.keys()) if yaml_config else 'Empty'}")
                
                # 检查是否有通义听悟配置
                if 'TONGYI_TINGWU' in yaml_config:
                    logger.info(f"   通义听悟配置: {yaml_config['TONGYI_TINGWU']}")
                else:
                    logger.info("   配置文件中没有TONGYI_TINGWU配置")
            else:
                logger.info(f"   配置文件不存在: {config_file_path}")
        except Exception as e:
            logger.error(f"   配置文件检查失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("🎯 开始配置调试...")
    
    success = debug_config()
    
    logger.info(f"\n{'='*80}")
    logger.info("🎯 配置调试总结")
    logger.info(f"{'='*80}")
    
    if success:
        logger.info("✅ 配置调试完成")
        logger.info("")
        logger.info("💡 **可能的问题:**")
        logger.info("   1. 配置对象属性未正确定义")
        logger.info("   2. 环境变量未正确传递到配置对象")
        logger.info("   3. 配置文件中缺少通义听悟配置")
        logger.info("   4. 配置加载顺序问题")
    else:
        logger.error("❌ 配置调试失败")

if __name__ == "__main__":
    main()
